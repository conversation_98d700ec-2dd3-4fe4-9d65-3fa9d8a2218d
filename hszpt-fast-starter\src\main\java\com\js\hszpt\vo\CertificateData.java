package com.js.hszpt.vo;

import com.baomidou.mybatisplus.annotation.TableField;import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 电子证照服务参数对象
 * <AUTHOR>
 */
@Data
public class CertificateData implements Serializable {
    private static final long serialVersionUID = 8542744970180842525L;

    /**
     * 证照二维码地址
     */
    private String qrCode;

    /**
     * 证照名称:"社会团体法人登记证书"
     */
    private String catalogName;
    /**
     * 版本号:"V1"
     */
    private String version;
    /**
     * 证照类型:"社会团体法人登记证书"
     */
    private String certificateType;
    /**
     * 颁证单位所属区划代码:"110000"
     */
    private String certificateAreaCode;
    /**
     * 持证主体名称:"**省体育总会"
     */
    private String certificateHolder;
    /**
     * 持证主体代码:"51**********4266"
     */
    private String certificateHolderCode;
    /**
     * 持证主体代码类型：身份证/统一社会信用代码
     */
    private String certificateHolderType;
    /**
     * 颁证单位:"**省民政厅"
     */
    private String issueDept;
    /**
     * 证照编号:"51**********4266"
     */
    private String certificateNumber;
    /**
     * 证照唯一标识
     */
    private String certificateId;
    /**
     * 颁证机构代码:"11**********1926"
     */
    private String issueDeptCode;
    /**
     * 颁证日期:"2011年11月1日"
     */
    private String issueDate;
    /**
     * 有效期起始:"2011年11月1日"
     */
    private String certificateValidateStart;
    /**
     * 有效期截止:"2015年10月31日"
     */
    private String certificateValidateEnd;
    List<Surface> surface;
    /**
     * 申请编号
     */
    private String applyNum;
    /**
     * 事项主键
     */
    private String affairId;
    /**
     * 事项编号
     */
    private String affairNum;
    /**
     * 事项名称
     */
    private String affairName;
    /**
     * 事项类型
     */
    private String affairType;
    /**
     * 业务类型
     */
    private String serveBusiness;
    /**
     * 签章类型
     * 01通航安全管理专用章
     * 02业务受理专用章
     * 03公章
     * 04船舶审核签注专用章
     * 05审核业务专用章
     * 06船舶文书专用章
     * 07航标设置许可专用章
     * 08船员证书专用章
     * 09危险货物管理专用章
     * 10航行警告专用章
     * 11航行通告专用章
     * 12海事声明签注章
     */
    private String qzType;
    /**
     * 证照类型1:证 2:文书
     */
    private String zzType;
    /**
     * 颁发证书机构简称
     */
    private String issueShortDept;
    /**
     * 预览
     */
    private String isView;
    /**
     * 变更次数
     */
    private String count;
    /**
     * 排序号
     */
    private String sortNo;
    /**
     * 证照名称（针对业务进行拼接后传的值）
     */
    private String sortName;
    /**
     *
     */
    private String userId;
    /**
     * 登录的用户名
     */
    private String name;
    /**
     * 操作人部门ID
     */
    private String deptCode;
    /**
     * 编号使用说明
     */
    private String useReason;
    /**
     * 海事社会信用代码
     */
    private String creditCode;
    /**
     * 证照状态
     */
    private String createStatus;

    @TableField(exist = false)
    private String operType;

}
