package com.js.hszpt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.js.hszpt.entity.SysDept;
import com.js.hszpt.entity.SysDeptIamb;
import com.js.hszpt.entity.SysDeptInfoResponse;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * @ClassName:  SysDeptIamb
 * @Description:TODO(组织信息表数据处理层)
 * @author:   System Generation
 */
public interface SysDeptIambDao extends BaseMapper<SysDeptIamb> {

    List<SysDeptInfoResponse> getSonOrgTree(String deptId);

    @Select("SELECT * FROM sys_dept WHERE code LIKE CONCAT(#{code}, '%')")
    List<SysDept> list(String code);

    @Select("SELECT * FROM sys_dept WHERE DEPT_ID=#{deptId}")
    SysDeptIamb get(String deptId);


//    @Select("SELECT * FROM sys_dept_iamb WHERE code ILIKE #{benCode} AND gov_level > 4 and name in ('政策法规处','法规规范处（执法督察处）','法规规范处')")
//    @Select("SELECT * FROM sys_dept_iamb WHERE code ILIKE '%' || #{benCode} || '%' and name in ('法规规范处（执法督察处）','法规规范处')")
    List<SysDeptIamb> getCurrentDeptInfo(@Param("benCode") String benCode);

    List<SysDeptInfoResponse> deptSon(@Param("orgCode") String orgCode, @Param("flag") String flag);

    /**
     * 根据机构编码查询机构及其子机构
     *
     * @param orgCode 机构编码
     * @return 机构列表
     */
    List<SysDeptIamb> findDeptByOrgCode(String orgCode);

    /**
     * 根据父级ID查询子机构
     *
     * @param parentId 父级ID
     * @return 子机构列表
     */
    List<SysDeptIamb> findDeptByParentId(String parentId);

    /**
     * 查询指定机构的所有后代机构（最多3级）
     * @param rootId 根机构ID
     * @return 所有相关机构列表
     */
    List<SysDeptIamb> findAllDescendants(String rootId);

    List<SysDeptInfoResponse> deptNow(String deptId);

    List<com.js.hszpt.vo.SysDeptInfoResponse> deptSonApp(@Param("orgCode") String orgCode, @Param("flag") String flag);
}
