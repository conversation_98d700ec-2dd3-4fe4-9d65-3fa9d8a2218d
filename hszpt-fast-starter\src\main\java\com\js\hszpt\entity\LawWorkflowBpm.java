package com.js.hszpt.entity;

import com.js.core.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.Builder;
import java.util.Date;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import com.js.util.SnowFlakeUtil;


/**
 * 
 * @ClassName:  LawWorkflowBpm   
 * @Description:TODO(待办任务表)   
 * @author:   System Generation 
 */
@Data

@TableName("LAW_WORKFLOW_BPM")
@ApiModel(value = "待办任务表")
public class LawWorkflowBpm {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "WORKFLOW_BPM_ID", type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value = "主键ID")
    private String workflowBpmId = String.valueOf(SnowFlakeUtil.nextId());

    /**
    * 业务类型
    */
    @ApiModelProperty(value = "业务类型")
    private String bpmType;


    /**
    * 业务名称
    */
    @ApiModelProperty(value = "业务名称")
    private String bpmName;


    /**
    * 业务编号
    */
    @ApiModelProperty(value = "业务编号")
    private String bpmNum;


    /**
    * 待办人员
    */
    @ApiModelProperty(value = "待办人员")
    private String userCode;


    /**
    * 实例ID
    */
    @ApiModelProperty(value = "实例ID")
    private String executionId;


    /**
    * 业务日期
    */
    @ApiModelProperty(value = "业务日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date businessDate;


    /**
    * 流程状态
    */
    @ApiModelProperty(value = "流程状态")
    private String bpmStatus;


    /**
    * 版本号
    */
    @ApiModelProperty(value = "版本号")
    private String recordVersion;


}