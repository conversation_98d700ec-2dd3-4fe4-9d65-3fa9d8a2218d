{"imageActionName": "uploadimage", "imageFieldName": "upfile", "imageMaxSize": 52428800, "imageAllowFiles": [".png", ".jpg", ".jpeg", ".gif", ".bmp"], "imageCompressEnable": true, "imageCompressBorder": 1600, "imageInsertAlign": "none", "imageUrlPrefix": "https://mjtest.jingsaitech.com", "imagePathFormat": "/upfile/image/{yyyy}{mm}{dd}/{time}{rand:6}", "scrawlActionName": "uploadscrawl", "scrawlFieldName": "upfile", "scrawlPathFormat": "/upfile/image/{yyyy}{mm}{dd}/{time}{rand:6}", "scrawlMaxSize": 52428800, "scrawlUrlPrefix": "https://mjtest.jingsaitech.com", "scrawlInsertAlign": "none", "snapscreenActionName": "uploadimage", "snapscreenPathFormat": "/upfile/image/{yyyy}{mm}{dd}/{time}{rand:6}", "snapscreenUrlPrefix": "https://mjtest.jingsaitech.com", "snapscreenInsertAlign": "none", "catcherLocalDomain": ["127.0.0.1", "localhost", "img.baidu.com"], "catcherActionName": "catchimage", "catcherFieldName": "source", "catcherPathFormat": "/upfile/image/{yyyy}{mm}{dd}/{time}{rand:6}", "catcherUrlPrefix": "https://mjtest.jingsaitech.com", "catcherMaxSize": 52428800, "catcherAllowFiles": [".png", ".jpg", ".jpeg", ".gif", ".bmp"], "videoActionName": "uploadvideo", "videoFieldName": "upfile", "videoPathFormat": "/upfile/video/{yyyy}{mm}{dd}/{time}{rand:6}", "videoUrlPrefix": "https://mjtest.jingsaitech.com", "videoMaxSize": 512000000, "videoAllowFiles": [".flv", ".swf", ".mkv", ".avi", ".rm", ".rmvb", ".mpeg", ".mpg", ".ogg", ".ogv", ".mov", ".wmv", ".mp4", ".webm", ".mp3", ".wav", ".mid"], "fileActionName": "uploadfile", "fileFieldName": "upfile", "filePathFormat": "/upfile/file/{yyyy}{mm}{dd}/{time}{rand:6}", "fileUrlPrefix": "https://mjtest.jingsaitech.com", "fileMaxSize": 52428800, "fileAllowFiles": [".png", ".jpg", ".jpeg", ".gif", ".bmp", ".flv", ".swf", ".mkv", ".avi", ".rm", ".rmvb", ".mpeg", ".mpg", ".ogg", ".ogv", ".mov", ".wmv", ".mp4", ".webm", ".mp3", ".wav", ".mid", ".rar", ".zip", ".tar", ".gz", ".7z", ".bz2", ".cab", ".iso", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".pdf", ".txt", ".md"], "imageManagerActionName": "listimage", "imageManagerListPath": "/upfile/image/", "imageManagerListSize": 20, "imageManagerUrlPrefix": "", "imageManagerInsertAlign": "none", "imageManagerAllowFiles": [".png", ".jpg", ".jpeg", ".gif", ".bmp"], "fileManagerActionName": "listfile", "fileManagerListPath": "/upfile/file/", "fileManagerUrlPrefix": "", "fileManagerListSize": 20, "fileManagerAllowFiles": [".png", ".jpg", ".jpeg", ".gif", ".bmp", ".flv", ".swf", ".mkv", ".avi", ".rm", ".rmvb", ".mpeg", ".mpg", ".ogg", ".ogv", ".mov", ".wmv", ".mp4", ".webm", ".mp3", ".wav", ".mid", ".rar", ".zip", ".tar", ".gz", ".7z", ".bz2", ".cab", ".iso", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".pdf", ".txt", ".md"]}