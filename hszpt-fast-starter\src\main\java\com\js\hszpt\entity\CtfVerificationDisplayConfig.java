package com.js.hszpt.entity;

import com.js.core.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.Builder;
import java.util.Date;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;


/**
 * 
 * @ClassName:  CtfVerificationDisplayConfig   
 * @Description:TODO(证照核验展示配置表)   
 * @author:   System Generation 
 */
@Data

@TableName("ctf_verification_display_config")
@ApiModel(value = "证照核验展示配置表")
public class CtfVerificationDisplayConfig extends BaseEntity {

    private static final long serialVersionUID = 1L;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String configId;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String certificateTypeCode;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String dataSourceTableName;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String chineseLabelName;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String englishLabelName;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String chineseDataFieldName;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String englishDataFieldName;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String chineseDataDefaultValue;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String englishDataDefaultValue;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String highlight;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private BigDecimal sortOrder;


}