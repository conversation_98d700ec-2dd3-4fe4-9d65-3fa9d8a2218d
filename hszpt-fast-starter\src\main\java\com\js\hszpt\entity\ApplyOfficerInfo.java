package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

@Data
@TableName("APPLY_OFFICER_INFO")
public class ApplyOfficerInfo implements Serializable {

    @TableId(value = "OFFICER_INFO_ID")
    private String officerInfoId;

    @TableField("WINDOW_APPLY_ID")
    private String windowApplyId;

    @TableField("OFFICER_NAME")
    private String officerName;

    @TableField("SEX")
    private String sex;

    @TableField("DEPARTMENT")
    private String department;

    @TableField("POSITION")
    private String position;

    @TableField("MECHANISM")
    private String mechanism;

    @TableField("PROCESSSTATUS")
    private String processStatus;

    @TableField("COL2")
    private String col2;

    @TableField("COL3")
    private String col3;

    @TableField("COL4")
    private String col4;

    @TableField("MODIFY_OPER_ID")
    private String modifyOperId;

    @TableField("MODITY_DATE")
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date modifyDate;

    @TableField("OFFICIAL_NAME_EN")
    private String officialNameEn;

    @TableField("OFFICIAL_TITLE_EN")
    private String officialTitleEn;
}
