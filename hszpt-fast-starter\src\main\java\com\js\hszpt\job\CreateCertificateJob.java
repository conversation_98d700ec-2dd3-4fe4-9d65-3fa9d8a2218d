package com.js.hszpt.job;

import cn.hutool.core.collection.CollUtil;
import com.js.hszpt.enmus.CertificateCreateStatus;
import com.js.hszpt.entity.Certificate;
import com.js.hszpt.service.CertificateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 调用电子证照接口生成电子证照定时任务
 */

@Component
@Slf4j
public class CreateCertificateJob {

    @Value("${job.createCertificate.enabled:false}")
    private boolean enabled;

    @Autowired
    private CertificateService certificateService;

    @Scheduled(cron = "${job.createCertificate.cron}")
    public void execute() {
        if (!enabled) {
            return;
        }
        long start = System.currentTimeMillis();
        log.info("【电子证照生成】电子证照生成任务执行开始：{}", start);
        // 扫描未生成电子证照的信息
        List<Certificate> certificateList = certificateService.getCertificateListByCertificateCreateStatus(CertificateCreateStatus.UN_CREATE);
        if (CollUtil.isEmpty(certificateList)) {
            log.info("【电子证照生成】暂无办件生成电子证照");
            return;
        }
        log.info("【电子证照生成】待生成电子证照办件数量：{}", certificateList.size());
        // 查询电子证照信息和对应的照面信息生成电子证照
        certificateList.forEach(certificate -> certificateService.createCertificate(certificate));
        long end = System.currentTimeMillis();
        log.info("【电子证照生成】电子证照生成任务执行结束：{}", end);
        log.info("【电子证照生成】电子证照生成任务执行耗时：{}", end - start);
    }

}
