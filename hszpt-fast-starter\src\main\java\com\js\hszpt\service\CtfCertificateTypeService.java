package com.js.hszpt.service;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.js.hszpt.dto.CtfCertificateTypeQueryDto;
import com.js.hszpt.entity.CertTypeDirectory;
import com.js.hszpt.entity.CertificateType;
import com.js.hszpt.mapper.CtfCertificateTypeDao;
import com.js.hszpt.entity.CtfCertificateType;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.core.common.utils.PageUtil;
import com.js.core.common.vo.PageVo;
import com.js.core.common.vo.SearchVo;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * @ClassName:  CtfCertificateTypeService
 * @Description:TODO(证照分类表接口实现)
 * @author:  System Generation
 *
 */
@Slf4j
@Service
public class CtfCertificateTypeService extends ServiceImpl<CtfCertificateTypeDao,CtfCertificateType> {

	@Autowired
	private CertTypeDirectoryService certTypeDirectoryService;

	/**
	 * 多条件分页查询
	 * @param param
	 * @param searchVo
	 * @param pageVo
	 * @return
	 */
	public Page<CtfCertificateType> findByCondition(CtfCertificateType param, SearchVo searchVo, PageVo pageVo) {
		Page<CtfCertificateType> page = PageUtil.initMpPage(pageVo);
		QueryWrapper<CtfCertificateType> queryWrapper = this.getCondition(param,searchVo);
		return this.page(page, queryWrapper);
	}

	/**
	 *
	 * @Title: findByCondition
	 * @Description: TODO(多条件列表查询)
	 * @param param
	 * @param searchVo
	 * @return  List<CtfCertificateType>
	 * @throws
	 */
	public List<CtfCertificateType> findByCondition(CtfCertificateType param, SearchVo searchVo){
		QueryWrapper<CtfCertificateType> queryWrapper = this.getCondition(param, searchVo);
		//添加排序字段
		queryWrapper.orderByAsc("create_time");

		return this.list(queryWrapper);
	}


	/**
	 *
	 * @Title: getCondition
	 * @Description: TODO(构建自定义查询条件)
	 * @param param
	 * @param searchVo
	 * @return  QueryWrapper<CtfCertificateType>
	 * @throws
	 */
	private QueryWrapper<CtfCertificateType> getCondition(CtfCertificateType param, SearchVo searchVo){
		QueryWrapper<CtfCertificateType> queryWrapper = new QueryWrapper<CtfCertificateType>();
		//请根据实际字段自行扩展

		// 创建时间
		if (StrUtil.isNotBlank(searchVo.getStartDate()) && StrUtil.isNotBlank(searchVo.getEndDate())) {
			Date start = DateUtil.parse(searchVo.getStartDate());
			Date end = DateUtil.parse(searchVo.getEndDate());
			queryWrapper.between("create_time",start, DateUtil.endOfDay(end));
		}
		return queryWrapper;

	}

	public CtfCertificateType getByCertificateTypeCode(String certificateTypeCode) {
		QueryWrapper<CtfCertificateType> queryWrapper = Wrappers.query();
		queryWrapper.lambda().eq(CtfCertificateType::getCertificateTypeCode, certificateTypeCode);
		return this.getOne(queryWrapper,false);
	}

	/**
	 * 获取证照名称列表
	 * @return 证照名称列表
	 */
	public List<CtfCertificateType> getCertificateNameList() {
		QueryWrapper<CtfCertificateType> queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda()
				.isNotNull(CtfCertificateType::getCertTypeDirCode)
				.isNotNull(CtfCertificateType::getCertificateName)
				.ne(CtfCertificateType::getCertificateName, "游艇驾驶证");
		return this.list(queryWrapper);
	}

	/**
	 * 根据条件查询证照分类
	 * @param queryDto 查询条件
	 * @return 证照分类列表
	 */
	public List<CtfCertificateType> queryByCondition(CtfCertificateTypeQueryDto queryDto) {
		LambdaQueryWrapper<CtfCertificateType> queryWrapper = new LambdaQueryWrapper<>();

		// 添加查询条件
		if (StrUtil.isNotBlank(queryDto.getCertificateName())) {
			queryWrapper.eq(CtfCertificateType::getCertificateName, queryDto.getCertificateName());
		}

		if (StrUtil.isNotBlank(queryDto.getCertificateCnEn())) {
			queryWrapper.eq(CtfCertificateType::getCertificateCnEn, queryDto.getCertificateCnEn());
		}

		if (StrUtil.isNotBlank(queryDto.getCertTypeDirId())) {
			String[] certTypeIds = queryDto.getCertTypeDirId().split(",");
			// 查询证书类型配置
			QueryWrapper<CertTypeDirectory> q = new QueryWrapper<>();
			q.in("CERT_TYPE_DIR_ID", Arrays.asList(certTypeIds));
			List<CertTypeDirectory> certificateConfigs = certTypeDirectoryService.list(q);
			log.info("查询到的证书类型配置数量：{}", certificateConfigs.size());
			// 获取certTypeDirCode列表
			List<String> certTypeDirCodes = certificateConfigs.stream()
					.map(CertTypeDirectory::getCertificateTypeCode)
					.filter(StrUtil::isNotBlank)
					.collect(Collectors.toList());
			log.info("获取到的证书类型目录代码列表：{}", certTypeDirCodes);
			if (CollUtil.isNotEmpty(certTypeDirCodes)) {
				queryWrapper.in(CtfCertificateType::getCertTypeDirCode, certTypeDirCodes);
			}
		}

		queryWrapper.eq(StrUtil.isNotBlank(queryDto.getCertClassification()), CtfCertificateType::getCertClassification, queryDto.getCertClassification());

		// 只查询未删除的记录
		queryWrapper.eq(CtfCertificateType::getDelFlag, "0");

		// 按创建时间降序排序
		queryWrapper.orderByDesc(CtfCertificateType::getCreateDate);

        //按typeCOde过滤 重复证书名称
        queryWrapper.notIn(CtfCertificateType::getCertificateTypeCode, "0100_2","0100_1","0101_2","0101_1");

        //过滤游艇驾驶证
        queryWrapper.ne(CtfCertificateType::getCertificateName, "游艇驾驶证");
		List<CtfCertificateType> list = this.list(queryWrapper);
		list.forEach(certificate -> {
			String certificateName = certificate.getCertificateName();
			// 处理对应的证照名称 映射
			if (StrUtil.equals("海船船员内河航线行驶资格证明",  certificateName)) {
				certificateName = "海船船员适任证书（内河水域航线签注）";
			}

			if (StrUtil.equals("游艇驾驶证（海上）",  certificateName)) {
				certificateName = "海上游艇操作人员适任证书";
			}

			if (StrUtil.equals("游艇驾驶证（内河）",  certificateName)) {
				certificateName = "内河游艇操作人员适任证书";
			}

			if (StrUtil.equals("小型海船适任证书",  certificateName)) {
				certificateName = "小型海船船员适任证书";
			}
			certificate.setCertificateName(certificateName);
		});
		return list;
	}
}
