package com.js.hszpt.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "SYS_DEPT")
@ApiModel(value = "", description = "-添加实体OR多条件查询")
public class SysDept {

    private static final long serialVersionUID = -71625126688712498L;

    @Id
    @Column(name = "DEPT_ID")
    @ApiModelProperty(value = "DEPT_ID", notes = "DEPT_ID")
    private String deptId;

    @Column(name = "NAME")
    @ApiModelProperty(value = "NAME", notes = "NAME")
    private String name;

    @Column(name = "SORT")
    @ApiModelProperty(value = "SORT", notes = "SORT")
    private Integer sort;

    @Column(name = "CREATE_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "CREATE_TIME", notes = "CREATE_TIME")
    private Date createTime;

    @Column(name = "UPDATE_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "UPDATE_TIME", notes = "UPDATE_TIME")
    private Date updateTime;

    @Column(name = "DEL_FLAG")
    @ApiModelProperty(value = "DEL_FLAG", notes = "DEL_FLAG")
    private String delFlag;

    @Column(name = "PARENT_ID")
    @ApiModelProperty(value = "PARENT_ID", notes = "PARENT_ID")
    private String parentId;

    @Column(name = "GOV_LEVEL")
    @ApiModelProperty(value = "GOV_LEVEL", notes = "GOV_LEVEL")
    private String govLevel;

    @Column(name = "CODE")
    @ApiModelProperty(value = "CODE", notes = "CODE")
    private String code;

    @Column(name = "IS_SHOW")
    @ApiModelProperty(value = "IS_SHOW", notes = "IS_SHOW")
    private String isShow;

    @Column(name = "HALL_SHOW")
    @ApiModelProperty(value = "HALL_SHOW", notes = "HALL_SHOW")
    private String hallShow;

    @Column(name = "CREDIT_CODE")
    @ApiModelProperty(value = "CREDIT_CODE", notes = "CREDIT_CODE")
    private String creditCode;

    @Column(name = "HANDLE_DATE")
    @ApiModelProperty(value = "HANDLE_DATE", notes = "HANDLE_DATE")
    private String handleDate;

    @Column(name = "APPROVE_ADDRESS")
    @ApiModelProperty(value = "APPROVE_ADDRESS", notes = "APPROVE_ADDRESS")
    private String approveAddress;

    @Column(name = "CONSULT_TYPE")
    @ApiModelProperty(value = "CONSULT_TYPE", notes = "CONSULT_TYPE")
    private String consultType;

    @Column(name = "SUPERVISE_TYPE")
    @ApiModelProperty(value = "SUPERVISE_TYPE", notes = "SUPERVISE_TYPE")
    private String superviseType;

    @Column(name = "IS_CENTER")
    @ApiModelProperty(value = "IS_CENTER", notes = "IS_CENTER")
    private String isCenter;

    @Column(name = "DEPT_PHONE")
    @ApiModelProperty(value = "DEPT_PHONE", notes = "DEPT_PHONE")
    private String deptPhone;

    @Column(name = "TASK_CODE")
    @ApiModelProperty(value = "TASK_CODE", notes = "TASK_CODE")
    private String taskCode;

    @Column(name = "PARENT_CODE")
    @ApiModelProperty(value = "PARENT_CODE", notes = "PARENT_CODE")
    private String parentCode;

    @Column(name = "DEPT_TYPE")
    @ApiModelProperty(value = "DEPT_TYPE", notes = "DEPT_TYPE")
    private String deptType;

    @Column(name = "NAME_ABBR")
    @ApiModelProperty(value = "NAME_ABBR", notes = "NAME_ABBR")
    private String nameAbbr;

//    private List<String> deptIds;

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

//    public List<String> getDeptIds() {
//        return deptIds;
//    }
//
//    public void setDeptIds(List<String> deptIds) {
//        this.deptIds = deptIds;
//    }

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getGovLevel() {
        return govLevel;
    }

    public void setGovLevel(String govLevel) {
        this.govLevel = govLevel;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getIsShow() {
        return isShow;
    }

    public void setIsShow(String isShow) {
        this.isShow = isShow;
    }

    public String getHallShow() {
        return hallShow;
    }

    public void setHallShow(String hallShow) {
        this.hallShow = hallShow;
    }

    public String getCreditCode() {
        return creditCode;
    }

    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    public String getHandleDate() {
        return handleDate;
    }

    public void setHandleDate(String handleDate) {
        this.handleDate = handleDate;
    }

    public String getApproveAddress() {
        return approveAddress;
    }

    public void setApproveAddress(String approveAddress) {
        this.approveAddress = approveAddress;
    }

    public String getConsultType() {
        return consultType;
    }

    public void setConsultType(String consultType) {
        this.consultType = consultType;
    }

    public String getSuperviseType() {
        return superviseType;
    }

    public void setSuperviseType(String superviseType) {
        this.superviseType = superviseType;
    }

    public String getIsCenter() {
        return isCenter;
    }

    public void setIsCenter(String isCenter) {
        this.isCenter = isCenter;
    }

    public String getDeptPhone() {
        return deptPhone;
    }

    public void setDeptPhone(String deptPhone) {
        this.deptPhone = deptPhone;
    }

    public String getTaskCode() {
        return taskCode;
    }

    public void setTaskCode(String taskCode) {
        this.taskCode = taskCode;
    }

    public String getParentCode() {
        return parentCode;
    }

    public void setParentCode(String parentCode) {
        this.parentCode = parentCode;
    }

    public String getDeptType() {
        return deptType;
    }

    public void setDeptType(String deptType) {
        this.deptType = deptType;
    }

    public String getNameAbbr() {
        return nameAbbr;
    }

    public void setNameAbbr(String nameAbbr) {
        this.nameAbbr = nameAbbr;
    }

}
