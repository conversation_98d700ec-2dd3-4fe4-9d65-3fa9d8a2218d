package com.js.hszpt.vo;

import lombok.Data;

/**
 * 船员系统通知请求参数
 */
@Data
public class CrewNotificationRequest {

    /**
     * 证照ID
     */
    private String certificateId;

    /**
     * 回调状态码
     */
    private String callbackCode;

    /**
     * 回调消息
     */
    private String callbackMsg;

    public CrewNotificationRequest(String certificateId, String callbackCode, String callbackMsg) {
        this.certificateId = certificateId;
        this.callbackCode = callbackCode;
        this.callbackMsg = callbackMsg;
    }
} 