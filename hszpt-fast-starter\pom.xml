<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.js</groupId>
	<artifactId>hszpt-starter</artifactId>
	<version>1.0.0-SNAPSHOT</version>

	<properties>
		<startMainClass>com.js.hszpt.HszptStarterApplication</startMainClass>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<java.version>1.8</java.version>
		<spring.boot.version>2.3.2.RELEASE</spring.boot.version>
		<maven-compiler-plugin.version>3.1</maven-compiler-plugin.version>
		<jsdp-project.version>2.0.0-SNAPSHOT</jsdp-project.version>

	</properties>

	<dependencyManagement>
		<dependencies>
			<!-- spring boot -->
			<dependency>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-dependencies</artifactId>
				<version>${spring.boot.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<dependencies>
		 <dependency>
			<groupId>com.js</groupId>
			<artifactId>jsdp-auth-server</artifactId>
			<version>${jsdp-project.version}</version>
		</dependency>

		<dependency>
			<groupId>com.js</groupId>
			<artifactId>jsdp-sys</artifactId>
			<version>${jsdp-project.version}</version>
		</dependency>

		<dependency>
			<groupId>com.js</groupId>
			<artifactId>jsdp-file</artifactId>
			<version>${jsdp-project.version}</version>
		</dependency>

<!--  		<dependency>-->
<!--			<groupId>com.js</groupId>-->
<!--			<artifactId>jsdp-activiti</artifactId>-->
<!--			<version>2.0.0-SNAPSHOT</version>-->
<!--		</dependency>-->

		<!-- 人大金仓 -->
		<dependency>
			<groupId>com.kingbase8</groupId>
			<artifactId>kingbase8</artifactId>
			<version>8.6.0</version>
		</dependency>

		<dependency>
			<groupId>org.postgresql</groupId>
			<artifactId>postgresql</artifactId>
			<version>42.2.18</version>
		</dependency>

		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>dynamic-datasource-spring-boot-starter</artifactId>
			<version>3.2.1</version>
		</dependency>

		<dependency>
			<groupId>com.belerweb</groupId>
			<artifactId>pinyin4j</artifactId>
			<version>2.5.1</version>
		</dependency>
	</dependencies>

	<build>
		<pluginManagement>
			<plugins>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-compiler-plugin</artifactId>
					<version>${maven-compiler-plugin.version}</version>
				</plugin>
			</plugins>
		</pluginManagement>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<version>${spring.boot.version}</version>
				<configuration>
					<mainClass>${startMainClass}</mainClass><!--这个要改成自己的入口类 -->
				</configuration>
				<executions>
					<execution>
						<goals>
							<goal>repackage</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>${java.version}</source>
					<target>${java.version}</target>
				</configuration>
			</plugin>



			<!-- 分离方式打包	-->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-assembly-plugin</artifactId>
				<configuration>
					<descriptors>
						<descriptor>src/main/scripts/assembly/assembly-boot-part.xml</descriptor>
					</descriptors>
				</configuration>
				<executions>
					<execution>
						<id>make-assembly</id>
						<phase>package</phase>
						<goals>
							<goal>single</goal>
						</goals>
					</execution>
				</executions>
			</plugin>


		</plugins>
	</build>

	<!-- 打包需要 但是编译会有问题 begin -->
	<repositories>
		<repository>
			<id>js-release</id>
			<name>Project Release Repository</name>
			<url>http://repo.maven.js:8081/repository/maven-releases/</url>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
		</repository>
	</repositories>
	<pluginRepositories>
		<pluginRepository>
			<id>js-snapshot</id>
			<name>Project Snapshot Repository</name>
			<url>http://repo.maven.js:8081/repository/maven-snapshots/</url>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
		</pluginRepository>
	</pluginRepositories>

</project>
