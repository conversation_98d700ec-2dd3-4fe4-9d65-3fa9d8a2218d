package com.js.hszpt.utils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpException;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

import static cn.hutool.http.HttpStatus.HTTP_OK;

/**
 * 证书数据获取工具类
 * 用于调用证书数据接口获取文件路径
 * 
 * <AUTHOR>
 * @date 2025-01-31
 */
@Slf4j
public class CertificateDataUtil {

    /**
     * 请求超时时间（毫秒）
     */
    private static final int TIME_OUT = 30000;

    /**
     * 根据证书ID获取文件路径（自定义服务器地址）
     * 
     * @param baseUrl       服务器基础地址
     * @param certificateId 证书ID
     * @return 文件路径，如果获取失败返回null
     */
    public static String getFilePathByCertificateId(String baseUrl, String certificateId) {
        if (StrUtil.isBlank(certificateId)) {
            log.error("【查询正孚接口】证书ID不能为空");
            return null;
        }

        if (StrUtil.isBlank(baseUrl)) {
            log.error("【查询正孚接口】服务器地址不能为空");
            return null;
        }

        HttpResponse response = null;
        try {
            // 构建请求URL
            String requestUrl = baseUrl + certificateId;
            log.info("【查询正孚接口】开始请求证书数据，URL: {}", requestUrl);

            // 发送HTTP GET请求
            response = HttpUtil.createGet(requestUrl)
                    .timeout(TIME_OUT)
                    .execute();

            // 检查响应状态
            if (response.getStatus() != HTTP_OK) {
                log.error("【查询正孚接口】请求失败，HTTP状态码: {}, URL: {}", response.getStatus(), requestUrl);
                return null;
            }

            // 获取响应内容
            String responseBody = response.body();
            log.debug("【查询正孚接口】接收到响应数据: {}", responseBody);

            // 解析JSON响应
            return parseFilePathFromResponse(responseBody);

        } catch (HttpException e) {
            log.error("【查询正孚接口】HTTP请求异常，证书ID: {}, 异常信息: {}", certificateId, e.getMessage());
            return null;
        } catch (Exception e) {
            log.error("【查询正孚接口】请求处理异常，证书ID: {}, 异常信息: {}", certificateId, e.getMessage(), e);
            return null;
        } finally {
            // 🔑 关键修复：确保HttpResponse被正确关闭
            if (response != null) {
                response.close();
            }
        }
    }

    /**
     * 从响应JSON中解析文件路径
     * 
     * @param responseBody 响应体JSON字符串
     * @return 文件路径，解析失败返回null
     */
    private static String parseFilePathFromResponse(String responseBody) {
        if (StrUtil.isBlank(responseBody)) {
            log.error("【查询正孚接口】响应内容为空");
            return null;
        }

        try {
            // 解析JSON
            JSONObject jsonObject = JSONUtil.parseObj(responseBody);

            // 检查success字段
            Boolean success = jsonObject.getBool("success");
            if (success == null || !success) {
                String message = jsonObject.getStr("message", "未知错误");
                log.error("【查询正孚接口】接口返回失败，错误信息: {}", message);
                return null;
            }

            // 获取result字段
            String result = jsonObject.getStr("result");
            if (StrUtil.isBlank(result)) {
                log.error("【查询正孚接口】响应中result字段为空");
                return null;
            }

            log.info("【查询正孚接口】成功获取文件路径: {}", result);
            return result;

        } catch (Exception e) {
            log.error("【查询正孚接口】解析响应JSON异常，响应内容: {}, 异常信息: {}", responseBody, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取完整的响应信息（自定义服务器地址）
     * 
     * @param baseUrl       服务器基础地址
     * @param certificateId 证书ID
     * @return 完整的响应JSON对象，获取失败返回null
     */
    public static JSONObject getFullResponse(String baseUrl, String certificateId) {
        if (StrUtil.isBlank(certificateId)) {
            log.error("【查询正孚接口】证书ID不能为空");
            return null;
        }

        if (StrUtil.isBlank(baseUrl)) {
            log.error("【查询正孚接口】服务器地址不能为空");
            return null;
        }

        HttpResponse response = null;
        try {
            // 构建请求URL
            String requestUrl = baseUrl + certificateId;
            log.info("【查询正孚接口】开始请求证书数据（完整响应），URL: {}", requestUrl);

            // 发送HTTP GET请求
            response = HttpUtil.createGet(requestUrl)
                    .timeout(TIME_OUT)
                    .execute();

            // 检查响应状态
            if (response.getStatus() != HTTP_OK) {
                log.error("【查询正孚接口】请求失败，HTTP状态码: {}, URL: {}", response.getStatus(), requestUrl);
                return null;
            }

            // 获取响应内容并解析为JSON
            String responseBody = response.body();
            log.debug("【查询正孚接口】接收到响应数据: {}", responseBody);

            if (StrUtil.isBlank(responseBody)) {
                log.error("【查询正孚接口】响应内容为空");
                return null;
            }

            return JSONUtil.parseObj(responseBody);

        } catch (HttpException e) {
            log.error("【查询正孚接口】HTTP请求异常，证书ID: {}, 异常信息: {}", certificateId, e.getMessage());
            return null;
        } catch (Exception e) {
            log.error("【查询正孚接口】请求处理异常，证书ID: {}, 异常信息: {}", certificateId, e.getMessage(), e);
            return null;
        } finally {
            // 🔑 关键修复：确保HttpResponse被正确关闭
            if (response != null) {
                response.close();
            }
        }
    }
}