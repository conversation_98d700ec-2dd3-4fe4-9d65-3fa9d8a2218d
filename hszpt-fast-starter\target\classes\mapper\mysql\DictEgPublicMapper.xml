<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.js.hszpt.mapper.DictEgPublicMapper">

	<!-- 查询条件构造 -->
	<sql id="where">
		<where>


		</where>
	</sql>

	<select id="getValidDictByType" resultType="com.js.hszpt.entity.DictEgPublic">
		select * from dict_eg_public
		         where upper(dict_type) like upper(#{type})
		           and delete_flag_code = '0'
			<if test="value != null and value != ''">
				and upper(dict_value) like upper(#{value})
			</if>
	</select>
</mapper>