package com.js.hszpt.utils;

import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import javax.servlet.ServletOutputStream;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.Random;

/**
 * <AUTHOR>
 * @date 2021/11/5 14:12
 */
@Slf4j
public class CreateVerifyCode {


    private int width = 160;
    private int height = 40;
    private int codeCount = 4;
    private int lineCount = 20;
    private String code = null;
    private BufferedImage buffImg = null;
    private int fontSize = 20;

    public CreateVerifyCode() {
    }

    public CreateVerifyCode(int width, int height, int codeCount, int lineCount, int fontSize, String code) {
        this.width = width;
        this.height = height;
        this.codeCount = codeCount;
        this.lineCount = lineCount;
        this.fontSize =fontSize;
        this.creatImage(code);
    }





    private void  creatImage(String code) {

        char[] codeSequence = {'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J',
                'K', 'L', 'M', 'N', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W',
                'X', 'Y', 'Z', '1', '2', '3', '4', '5', '6', '7', '8', '9'};

        StringBuilder builderCode = new StringBuilder();
        this.buffImg  = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);

        //定义随机数类
        Random r = new Random();

        //得到画笔
        Graphics g = buffImg.getGraphics();
        //1.设置颜色,画边框
        g.setColor(Color.gray);
        g.drawRect(0, 0, width, height);

        //2.设置颜色,填充内部
        g.setColor(Color.white);
        g.fillRect(1, 1, width - 2, height - 2);

        //3.设置干扰线
        for (int i = 0; i < lineCount; i++) {
            int _R = (int) Math.floor(Math.random() * 256);
            int _G = (int) Math.floor(Math.random() * 256);
            int _B = (int) Math.floor(Math.random() * 256);
            g.setColor(new Color(_R, _G, _B, 255));
            g.drawLine(r.nextInt(width), r.nextInt(width), r.nextInt(width), r.nextInt(width));
        }

        //4.设置验证码
        g.setColor(Color.blue);
        //4.1设置验证码字体
        g.setFont(new Font("宋体", Font.BOLD | Font.ITALIC, fontSize));
        for (int i = 0; i < code.length(); ++i) {
            String strRand = code.substring(i, i + 1);
            g.drawString(strRand + "", ((width / codeCount) * i + 2), height * 4 / 5);
        }
    }

    public String randomStr(int n) {
        String str1 = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz1234567890";
        String str2 = "";
        int len = str1.length() - 1;

        for(int i = 0; i < n; ++i) {
            double r = Math.random() * (double)len;
            str2 = str2 + str1.charAt((int)r);
        }

        return str2;
    }

    public void write(ServletOutputStream sos) throws IOException {
        //定义存储验证码的类
        ImageIO.write(this.buffImg, "png", sos);
        sos.close();
    }
}
