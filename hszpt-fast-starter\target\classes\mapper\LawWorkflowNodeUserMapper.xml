<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.js.hszpt.mapper.LawWorkflowNodeUserDao">
    <!--    <select id="selectWorkflowNodeUser" resultType="com.js.hszpt.vo.LawWorkflowNodeUserListVo">-->
    <!--        SELECT-->
    <!--            wnu.LAW_WORKFLOW_NODE_USER_ID,-->
    <!--            (SELECT d.name FROM "SYS_DEPT_IAMB" d WHERE wnu.ORG_CODE = d."CODE") AS ORG_NAME,-->
    <!--                (SELECT su.DEPARTMENT_NAME-->
    <!--                 FROM hszpt."SYS_USER" su-->
    <!--                 WHERE su.USERNAME = wnu.USER_CODE) AS DEPARTMENT_NAME,-->
    <!--                CASE-->
    <!--                    WHEN wnu.ORG_CODE = '01' THEN '审批'-->
    <!--                    ELSE '审核'-->
    <!--                    END AS "type",-->
    <!--                wnu.USER_NAME,-->
    <!--                wnu.CREATE_OPER_ID,-->
    <!--                wnu.CREATE_TIME-->
    <!--        FROM-->
    <!--            "LAW_WORKFLOW_NODE_USER" wnu-->
    <!--        WHERE-->
    <!--            wnu.LAW_WORKFLOW_NODE_set_ID = #{dto.lawWorkflowNodeSetId}-->
    <!--          AND wnu.ORG_CODE =#{dto.orgCode}-->
    <!--        AND wnu.LAW_WORKFLOW_NODE_INFO_ID=#{dto.lawWorkflowNodeInfoId}-->
    <!--    </select>-->

    <select id="selectWorkflowNodeUser" resultType="com.js.hszpt.vo.LawWorkflowNodeUserListVo">
        SELECT
            wnu.LAW_WORKFLOW_NODE_USER_ID,
            (SELECT d.name FROM "SYS_DEPT_IAMB" d WHERE wnu.ORG_CODE = d."CODE") AS ORG_NAME,
            (SELECT su.DEPARTMENT_NAME
             FROM dzzz_sys_user su
             WHERE su.USERNAME = wnu.USER_CODE) AS DEPARTMENT_NAME,
            CASE
                WHEN wnu.ORG_CODE = '01' THEN '审批'
                ELSE '审核'
                END AS "type",
            wnu.USER_NAME,
            wnu.CREATE_OPER_ID,
            wnu.CREATE_TIME
        FROM
            "LAW_WORKFLOW_NODE_USER" wnu
        WHERE
            wnu.LAW_WORKFLOW_NODE_set_ID = #{dto.lawWorkflowNodeSetId}
          AND wnu.LAW_WORKFLOW_NODE_INFO_ID = #{dto.lawWorkflowNodeInfoId}
          AND (
            -- 如果当前用户是部局用户（gov_level = 1），可以查询部局和直属局数据
                (EXISTS (
                    SELECT 1 FROM "SYS_DEPT_IAMB" d
                    WHERE d."CODE" = #{dto.orgCode[0]} AND d."GOV_LEVEL" = '1'
                ) AND (
                     -- 查询部局自己的数据
                             wnu.ORG_CODE = #{dto.orgCode[0]}
                         OR
                         -- 查询直属局数据
                             EXISTS (
                                 SELECT 1 FROM "SYS_DEPT_IAMB" d
                                 WHERE d."CODE" = wnu.ORG_CODE AND d."GOV_LEVEL" > '1'
                             )
                     ))
                OR
                -- 如果当前用户是直属局用户，只能查询自己的数据
                (EXISTS (
                    SELECT 1 FROM "SYS_DEPT_IAMB" d
                    WHERE d."CODE" = #{dto.orgCode[0]} AND d."GOV_LEVEL" > '1'
                ) AND wnu.ORG_CODE = #{dto.orgCode[0]})
            )
    </select>

    <select id="selectWorkflowNodeUserbj" resultType="com.js.hszpt.vo.LawWorkflowNodeUserListVo">
        SELECT
            wnu.LAW_WORKFLOW_NODE_USER_ID,
            (SELECT d.name FROM "SYS_DEPT_IAMB" d WHERE wnu.ORG_CODE = d."CODE") AS ORG_NAME,
            (SELECT su.DEPARTMENT_NAME
             FROM dzzz_sys_user su
             WHERE su.USERNAME = wnu.USER_CODE) AS DEPARTMENT_NAME,
            '审批' AS "type",
            wnu.USER_NAME,
            wnu.CREATE_OPER_ID,
            wnu.CREATE_TIME
        FROM
            "LAW_WORKFLOW_NODE_USER" wnu
        WHERE
            wnu.LAW_WORKFLOW_NODE_set_ID = #{dto.lawWorkflowNodeSetId}
          AND wnu.LAW_WORKFLOW_NODE_INFO_ID = #{dto.lawWorkflowNodeInfoId}
          AND EXISTS (
            SELECT 1
            FROM "SYS_DEPT_IAMB" d
            WHERE d."CODE" = wnu.ORG_CODE
              AND d."GOV_LEVEL" = '1'
        )
          AND wnu.ORG_CODE = #{dto.orgCode[0]}
        ORDER BY wnu.CREATE_TIME DESC
    </select>

    <select id="selectUserForWorkflowSet" resultType="com.js.hszpt.vo.LawWorkflowNodeUserListVo">
        SELECT
        (SELECT d.name FROM "SYS_DEPT_IAMB" d WHERE wnu.ORG_CODE = d."CODE") AS ORG_NAME,
        (SELECT su.DEPARTMENT_NAME
        FROM dzzz_sys_user su
        WHERE su.USERNAME = wnu.USER_CODE) AS DEPARTMENT_NAME,
        CASE
        WHEN wnu.ORG_CODE = '01' THEN '审批'
        ELSE '审核'
        END AS "type",
        wnu.USER_NAME,
        wnu.CREATE_OPER_ID,
        wnu.CREATE_TIME,
        wns.ITEMS_NAME as itemsName,
        wni.NODE_NAME as nodeName
        FROM
        "LAW_WORKFLOW_NODE_USER" wnu
        LEFT JOIN "LAW_WORKFLOW_NODE_SET" wns ON wnu.LAW_WORKFLOW_NODE_set_ID = wns.LAW_WORKFLOW_NODE_SET_ID
        LEFT JOIN "LAW_WORKFLOW_NODE_INFO" wni ON wnu.LAW_WORKFLOW_NODE_INFO_ID = wni.LAW_WORKFLOW_NODE_INFO_ID
        WHERE
        <if test="dto.lawWorkflowNodeSetId != null and dto.lawWorkflowNodeSetId != ''">
            wnu.LAW_WORKFLOW_NODE_set_ID = #{dto.lawWorkflowNodeSetId}
        </if>
        <if test="dto.lawWorkflowNodeSetId == null or dto.lawWorkflowNodeSetId == ''">
            1=1
        </if>
        AND
        <if test="dto.orgCode != null and dto.orgCode.size() > 0">
            (
            <foreach collection="dto.orgCode" item="code" separator=" OR ">
                wnu.ORG_CODE ~ ('^' || #{code} || '.*')
            </foreach>
            )
        </if>
        <if test="dto.orgCode == null or dto.orgCode.isEmpty()">
            1=1
        </if>
    </select>

    <select id="selectUserForWorkflowSet2" resultType="com.js.hszpt.vo.LawWorkflowNodeUserListVo">
        SELECT
        (SELECT d.name FROM "SYS_DEPT_IAMB" d WHERE wnu.ORG_CODE = d."CODE") AS ORG_NAME,
        (SELECT su.DEPARTMENT_NAME
        FROM dzzz_sys_user su
        WHERE su.USERNAME = wnu.USER_CODE) AS DEPARTMENT_NAME,
        CASE
        WHEN wnu.ORG_CODE = '01' THEN '审批'
        ELSE '审核'
        END AS "type",
        wnu.USER_NAME,
        wnu.CREATE_OPER_ID,
        wnu.CREATE_TIME,
        wns.ITEMS_NAME as itemsName,
        wni.NODE_NAME as nodeName
        FROM
        "LAW_WORKFLOW_NODE_USER" wnu
        LEFT JOIN "LAW_WORKFLOW_NODE_SET" wns ON wnu.LAW_WORKFLOW_NODE_set_ID = wns.LAW_WORKFLOW_NODE_SET_ID
        LEFT JOIN "LAW_WORKFLOW_NODE_INFO" wni ON wnu.LAW_WORKFLOW_NODE_INFO_ID = wni.LAW_WORKFLOW_NODE_INFO_ID
        WHERE
        <if test="dto.lawWorkflowNodeSetId != null and dto.lawWorkflowNodeSetId != ''">
            wnu.LAW_WORKFLOW_NODE_set_ID = #{dto.lawWorkflowNodeSetId}
        </if>
        <if test="dto.lawWorkflowNodeSetId == null or dto.lawWorkflowNodeSetId == ''">
            1=1
        </if>
        AND
        <if test="dto.orgCode != null and dto.orgCode.size() > 0">
            (
            <foreach collection="dto.orgCode" item="code" separator=" OR ">
                wnu.ORG_CODE =#{code}
            </foreach>
            )
        </if>
        <if test="dto.orgCode == null or dto.orgCode.isEmpty()">
            1=1
        </if>
    </select>
    <select id="selectWorkflowNodeUserList" resultType="com.js.hszpt.vo.LawWorkflowNodeUserListVo">
        SELECT
            wnu.LAW_WORKFLOW_NODE_USER_ID,
            (SELECT d.name FROM "SYS_DEPT_IAMB" d WHERE wnu.ORG_CODE = d."CODE") AS ORG_NAME,
            (SELECT su.DEPARTMENT_NAME
             FROM dzzz_sys_user su
             WHERE su.USERNAME = wnu.USER_CODE) AS DEPARTMENT_NAME,
            CASE
                WHEN wnu.ORG_CODE = '01' THEN '审批'
                ELSE '审核'
                END AS "type",
            wnu.USER_NAME,
            wnu.USER_CODE,
            wnu.CREATE_OPER_ID,
            wnu.CREATE_TIME
        FROM
            "LAW_WORKFLOW_NODE_USER" wnu
        WHERE
            wnu.LAW_WORKFLOW_NODE_set_ID = #{dto.lawWorkflowNodeSetId}
          AND wnu.LAW_WORKFLOW_NODE_INFO_ID = #{dto.lawWorkflowNodeInfoId}
          AND (
            -- 如果当前用户是部局用户（gov_level = 1），可以查询部局和直属局数据
            (EXISTS (
                SELECT 1 FROM "SYS_DEPT_IAMB" d
                WHERE d."CODE" = #{dto.orgCode[0]} AND d."GOV_LEVEL" = '1'
            ) AND (
                 -- 查询部局自己的数据
                 wnu.ORG_CODE = #{dto.orgCode[0]}
                     OR
                     -- 查询直属局数据
                 EXISTS (
                     SELECT 1 FROM "SYS_DEPT_IAMB" d
                     WHERE d."CODE" = wnu.ORG_CODE AND d."GOV_LEVEL" > '1'
                 )
                 ))
                OR
                -- 如果当前用户是直属局用户，只能查询自己的数据
            (EXISTS (
                SELECT 1 FROM "SYS_DEPT_IAMB" d
                WHERE d."CODE" = #{dto.orgCode[0]} AND d."GOV_LEVEL" > '1'
            ) AND wnu.ORG_CODE = #{dto.orgCode[0]})
            )
    </select>

    <select id="selectWorkflowNodeUserbjList" resultType="com.js.hszpt.vo.LawWorkflowNodeUserListVo">
        SELECT
            wnu.LAW_WORKFLOW_NODE_USER_ID,
            (SELECT d.name FROM "SYS_DEPT_IAMB" d WHERE wnu.ORG_CODE = d."CODE") AS ORG_NAME,
            (SELECT su.DEPARTMENT_NAME
             FROM dzzz_sys_user su
             WHERE su.USERNAME = wnu.USER_CODE) AS DEPARTMENT_NAME,
            '审批' AS "type",
            wnu.USER_NAME,
            wnu.CREATE_OPER_ID,
            wnu.CREATE_TIME
        FROM
            "LAW_WORKFLOW_NODE_USER" wnu
        WHERE
            wnu.LAW_WORKFLOW_NODE_set_ID = #{dto.lawWorkflowNodeSetId}
          AND wnu.LAW_WORKFLOW_NODE_INFO_ID = #{dto.lawWorkflowNodeInfoId}
          AND EXISTS (
            SELECT 1
            FROM "SYS_DEPT_IAMB" d
            WHERE d."CODE" = wnu.ORG_CODE
              AND d."GOV_LEVEL" = '1'
        )
          AND wnu.ORG_CODE = #{dto.orgCode[0]}
        ORDER BY wnu.CREATE_TIME DESC
    </select>

    <!-- 批量保存工作流节点用户 -->
    <insert id="batchSaveWorkflowNodeUsers" parameterType="java.util.List">
        INSERT INTO law_workflow_node_user (
        law_workflow_node_user_id,
        law_workflow_node_org_id,
        law_workflow_node_set_id,
        law_workflow_node_info_id,
        cert_no,
        user_code,
        user_name,
        create_oper_id,
        create_oper_dept,
        create_time,
        modify_oper_id,
        modify_oper_dept,
        modify_time,
        record_version,
        org_code
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.lawWorkflowNodeUserId},
            #{item.lawWorkflowNodeOrgId},
            #{item.lawWorkflowNodeSetId},
            #{item.lawWorkflowNodeInfoId},
            #{item.certNo},
            #{item.userCode},
            #{item.userName},
            #{item.createOperId},
            #{item.createOperDept},
            #{item.createTime},
            #{item.modifyOperId},
            #{item.modifyOperDept},
            #{item.modifyTime},
            #{item.recordVersion},
            #{item.orgCode}
            )
        </foreach>
    </insert>

    <!-- 批量删除工作流节点用户 -->
    <!--    <delete id="deleteBylawWorkflowNodeUserId" parameterType="java.util.List">-->
    <!--        DELETE FROM LAW_WORKFLOW_NODE_USER-->
    <!--        WHERE LAW_WORKFLOW_NODE_USER_ID IN-->
    <!--        <foreach collection="list" item="id" open="(" separator="," close=")">-->
    <!--            #{id}-->
    <!--        </foreach>-->
    <!--    </delete>-->

    <!-- 批量删除工作流节点用户 -->
    <delete id="deleteBylawWorkflowNodeUserId" parameterType="java.util.List">
        DELETE FROM law_workflow_node_user
        WHERE law_workflow_node_user_id IN
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>