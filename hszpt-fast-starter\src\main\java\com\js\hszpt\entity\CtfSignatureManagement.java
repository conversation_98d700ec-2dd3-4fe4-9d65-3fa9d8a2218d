package com.js.hszpt.entity;

import com.js.core.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.Builder;
import java.util.Date;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import com.js.util.SnowFlakeUtil;


/**
 * 
 * @ClassName:  CtfSignatureManagement   
 * @Description:TODO(证照签名管理表)   
 * @author:   System Generation 
 */
@Data

@TableName("CTF_SIGNATURE_MANAGEMENT")
@ApiModel(value = "证照签名管理表")
public class CtfSignatureManagement extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "CTF_ID", type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value = "主键ID")
    private String id = String.valueOf(SnowFlakeUtil.nextId());

    /**
     * 证照ID
     */
    @TableField("CERTIFICATE_ID")
    @ApiModelProperty(value = "证照ID")
    private String certificateId;

    /**
     * 证照名称
     */
    @TableField("CERTIFICATE_NAME")
    @ApiModelProperty(value = "证照名称")
    private String certificateName;

    /**
     * 签名状态
     */
    @TableField("SIGNATURE_STATUS")
    @ApiModelProperty(value = "签名状态")
    private String signatureStatus;

    /**
     * 申请日期
     */
    @TableField(value = "APPLY_DATE", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "申请日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date applyDate;

    /**
     * 签名日期
     */
    @TableField(value = "SIGNATURE_DATE", fill = FieldFill.UPDATE)
    @ApiModelProperty(value = "签名日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date signatureDate;

    /**
     * 签名用户ID
     */
    @TableField("SIGNATURE_USER_ID")
    @ApiModelProperty(value = "签名用户ID")
    private String signatureUserId;

    /**
     * 签名用户名称
     */
    @TableField("SIGNATURE_USER_NAME")
    @ApiModelProperty(value = "签名用户名称")
    private String signatureUserName;

    /**
     * 签名内容
     */
    @TableField("SIGNATURE_CONTENT")
    @ApiModelProperty(value = "签名内容")
    private String signatureContent;

    /**
     * 签名文件路径
     */
    @TableField("SIGNATURE_FILE_PATH")
    @ApiModelProperty(value = "签名文件路径")
    private String signatureFilePath;

    /**
     * 失败原因
     */
    @TableField("FAIL_REASON")
    @ApiModelProperty(value = "失败原因")
    private String failReason;

    /**
     * 删除标志：0-正常，1-已删除
     */
    @TableField("DEL_FLAG")
    @ApiModelProperty(value = "删除标志：0-正常，1-已删除")
    private String delFlag;

}