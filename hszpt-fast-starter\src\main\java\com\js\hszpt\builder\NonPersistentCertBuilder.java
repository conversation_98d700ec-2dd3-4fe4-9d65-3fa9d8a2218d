package com.js.hszpt.builder;

import com.js.hszpt.vo.CertificateVo;

import java.util.Collections;
import java.util.Map;

import static com.js.hszpt.constants.OilPollutionWarrantyConstant.NON_PERSISTENT;
import static com.js.hszpt.constants.OilPollutionWarrantyConstant.NON_PERSISTENT_REVERSE;

public class NonPersistentCertBuilder implements CertificateInfoBuilder{
    @Override
    public Map<String, String> certInfo() {
        return NON_PERSISTENT;
    }

    @Override
    public Map<String, String> certReverseInfo() {
        return NON_PERSISTENT_REVERSE;
    }

    @Override
    public Map<String, String> buildCertificateAttribute(CertificateVo certificateVo) {
        return Collections.emptyMap();
    }
}
