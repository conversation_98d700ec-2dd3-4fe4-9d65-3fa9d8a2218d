package com.js.hszpt.api;


import com.alibaba.fastjson.JSONArray;
import com.js.hszpt.dto.LawWorkflowNodeUserDTO;
import com.js.hszpt.dto.LawWorkflowNodeUserListDto;
import com.js.hszpt.entity.LawWorkflowNodeUser;
import com.js.hszpt.service.LawWorkflowNodeUserService;
 
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.js.core.api.BaseApiPlus;
import com.js.core.common.utils.ResultUtil;
import com.js.core.common.vo.PageVo;
import com.js.core.common.vo.Result;
import com.js.core.common.vo.SearchVo;
import com.js.annotation.SystemLog;
import com.js.enums.LogType;
import com.js.hszpt.vo.LawWorkflowNodeInfoVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;


/**
 * 
 * @ClassName: LawWorkflowNodeUserApi  
 * @Description:TODO(工作流用户信息接口)   
 * @author:  System Generation  
 *
 */
@Slf4j
@RestController
@Api(description = "工作流用户信息接口")
@RequestMapping("/lawWorkflowNodeUser")
public class LawWorkflowNodeUserApi extends BaseApiPlus<LawWorkflowNodeUserService,LawWorkflowNodeUser,String> {

	@SystemLog(description = "工作流用户信息-分页查询", type = LogType.OPERATION)
	@RequestMapping(value = "/getPage", method = RequestMethod.GET)
	@ApiOperation(value = "分页查询")
	public Result<Page<LawWorkflowNodeUser>> getPage(@ModelAttribute LawWorkflowNodeUser param, @ModelAttribute SearchVo searchVo, @ModelAttribute PageVo pageVo) {
		Page<LawWorkflowNodeUser> page = this.baseService.findByCondition(param, searchVo, pageVo);
		return ResultUtil.data(page);
	}

	@SystemLog(description = "工作流用户信息-列表查询", type = LogType.OPERATION)
	@RequestMapping(value = "/getList", method = RequestMethod.GET)
	@ApiOperation(value = "列表查询")
	public Result<List<LawWorkflowNodeUser>> getList(@ModelAttribute LawWorkflowNodeUser param, @ModelAttribute SearchVo searchVo) {
		List<LawWorkflowNodeUser> list = this.baseService.findByCondition(param, searchVo);
		return ResultUtil.data(list);
	}


	@PostMapping("/selectJgWorkflowNodeUser")
	@ApiOperation("查询工作流用户信息")
	public Result<Map<String, Object>> selectJgWorkflowNodeUser(@RequestBody LawWorkflowNodeUserListDto lawWorkflowNodeOrgId) {

		try {
			Map<String, Object> workflowNodeUsers = this.baseService.selectJgWorkflowNodeUser(lawWorkflowNodeOrgId);
			String result = JSONArray.toJSONString(workflowNodeUsers);
			log.info("查询工作流用户信息{}" + result);
			return ResultUtil.data(workflowNodeUsers);
		} catch (Exception e) {
			log.error("查询工作流用户信息失败", e);
			return ResultUtil.error("查询工作流用户信息失败：" + e.getMessage());
		}

	}


	@PostMapping("/JgWorkflowNodeSetUser")
	@ApiOperation("查某模板下本级和下级机构 工作流中所有用户")
	public Result<Map<String, Object>> JgWorkflowNodeSetUser(@RequestBody LawWorkflowNodeUserListDto lawWorkflowNodeOrgId) {
		try {
			Map<String, Object> workflowNodeUsers = this.baseService.JgWorkflowNodeSetUser(lawWorkflowNodeOrgId);
			String result = JSONArray.toJSONString(workflowNodeUsers);
			log.info("分页查某模板下本级和下级机构 工作流中所有用户" + result);
			return ResultUtil.data(workflowNodeUsers);
		} catch (Exception e) {
			log.error("查询工作流用户信息失败", e);
			return ResultUtil.error("查询工作流用户信息失败：" + e.getMessage());
		}
	}

	@ApiOperation("新增用户")
	@PostMapping("/insertLawWorkflowNodeUser")
	public Result<String> insertLawWorkflowNodeUser(@RequestBody List<LawWorkflowNodeUserDTO> lawWorkflowNodeUserDTOS) {
		String userCode = "";
		try {
			for (LawWorkflowNodeUserDTO obj : lawWorkflowNodeUserDTOS) {
				userCode += obj.getUserCode() + ",";
			}
//			userCode = userCode.substring(0, userCode.length() - 1);
			String result = this.baseService.insertLawWorkflowNodeUser(lawWorkflowNodeUserDTOS);
			log.info("新增用户" + result);
			return ResultUtil.data(result);
		} catch (Exception e) {
			log.error("新增用户信息失败", e);
			return ResultUtil.error("新增用户用户信息失败：" + e.getMessage());
		}
	}

	@ApiOperation("删除用户")
	@PostMapping("/deleteLawWorkflowNodeUser")
	public Result<String> deleteLawWorkflowNodeUser(@RequestBody List<String> lawWorkflowNodeUserId) {
		try {
			if (lawWorkflowNodeUserId == null || lawWorkflowNodeUserId.isEmpty()) {
				return ResultUtil.error("删除失败：用户ID列表为空");
			}

			log.info("开始删除用户，ID列表：{}", String.join(",", lawWorkflowNodeUserId));
			boolean deleteSuccess = this.baseService.deleteLawWorkflowNodeUser(lawWorkflowNodeUserId);

			if (deleteSuccess) {
				log.info("删除用户成功，ID列表：{}", String.join(",", lawWorkflowNodeUserId));
				return ResultUtil.data("删除成功");
			} else {
				log.error("删除用户失败，ID列表：{}", String.join(",", lawWorkflowNodeUserId));
				return ResultUtil.error("删除失败");
			}
		} catch (Exception e) {
			log.error("删除用户信息失败", e);
			return ResultUtil.error("删除用户信息失败：" + e.getMessage());
		}
	}
}
