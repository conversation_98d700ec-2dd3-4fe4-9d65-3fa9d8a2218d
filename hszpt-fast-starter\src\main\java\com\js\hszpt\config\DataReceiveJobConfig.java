package com.js.hszpt.config;

import cn.hutool.json.JSONUtil;
import com.js.hszpt.factory.DataReceiveJobFactory;
import com.js.hszpt.job.DataReceiveJob;
import com.js.hszpt.properties.DataReceiveJobProperties;
import com.js.hszpt.properties.ZptProperties;
import lombok.extern.slf4j.Slf4j;
import org.quartz.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;

import java.util.List;

/**
 * 数据接收定时任务触发配置
 */

@Configuration
@Slf4j
public class DataReceiveJobConfig {

    @Autowired
    private ZptProperties zptProperties;

    @Autowired
    private SchedulerFactoryBean schedulerFactoryBean;

    // 将定时任务工厂加入spring容器
    @Bean
    public DataReceiveJobFactory createJobFactory(ApplicationContext applicationContext) {
        DataReceiveJobFactory factory = new DataReceiveJobFactory();
        factory.setApplicationContext(applicationContext);
        return factory;
    }

    // 创建定时任务工程bean
    @Bean
    public SchedulerFactoryBean schedulerFactoryBean(DataReceiveJobFactory dataReceiveJobFactory) {
        SchedulerFactoryBean factory = new SchedulerFactoryBean();
        factory.setJobFactory(dataReceiveJobFactory);
        return factory;
    }

    // 初始化定时任务
    @Bean
    public void initJob() {
        try {
            Scheduler scheduler = schedulerFactoryBean.getScheduler();
            // 读取任务配置信息 每个任务启动一个定时任务
            List<DataReceiveJobProperties> dataReceiveJob = zptProperties.getDataReceiveJob();
            for (DataReceiveJobProperties dataReceiveJobProperties : dataReceiveJob) {
                // 创建任务
                JobDetail jobDetail = JobBuilder.newJob(DataReceiveJob.class)
                        .withIdentity("当前任务名称",dataReceiveJobProperties.getTaskName())
                        // 任务传参
                        .usingJobData("dataReceiveJobProperties", JSONUtil.toJsonStr(dataReceiveJobProperties))
                        .build();
                // 创建触发器
                Trigger trigger = TriggerBuilder.newTrigger()
                        .forJob(jobDetail)
                        // 配置任务触发频率
                        .withSchedule(CronScheduleBuilder.cronSchedule(dataReceiveJobProperties.getCron()))
                        .build();
                scheduler.scheduleJob(jobDetail, trigger);
            }
            scheduler.start();
            log.info("【数据接收】定时任务初始化完成");
        } catch (SchedulerException e) {
            log.error("【数据接收】定时任务执行异常:{}", e.getMessage());
        }
    }

}
