package com.js.hszpt.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.hszpt.entity.CertificateAttribute;
import com.js.hszpt.mapper.CertificateAttributeMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class CertificateAttributeService extends ServiceImpl<CertificateAttributeMapper, CertificateAttribute> {

    public List<CertificateAttribute> getCertificateAttributeListByCertificateId(String certificateId) {
        QueryWrapper<CertificateAttribute> queryWrapper = Wrappers.<CertificateAttribute>query();
        queryWrapper.lambda()
                .eq(CertificateAttribute::getCertificateId,certificateId);
        return this.list(queryWrapper);
    }
}
