package com.js.hszpt.api;

import cn.hutool.core.util.StrUtil;
import com.js.core.common.utils.ResultUtil;
import com.js.core.common.vo.Result;
import com.js.hszpt.config.ProxyConfig;
import com.js.hszpt.dto.*;
import com.js.hszpt.service.DwsCertificateUsageService;
import com.js.hszpt.service.SysUserService;
import com.js.hszpt.vo.CertificateUsageStatisticsVO;
import com.js.hszpt.utils.HttpProxyUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 证照使用统计API接口
 */
@Slf4j
@RestController
@RequestMapping("/certificate/usage/statistics")
@Api(tags = "证照使用统计接口")
public class CertificateUsageStatisticsApi {

    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private HttpProxyUtil httpProxyUtil;

    @Autowired
    private ProxyConfig proxyConfig;

    /**
     * 按下级机构统计使用情况
     * 
     * @param param 查询参数
     * @return 按下级机构统计结果
     */
    @PostMapping("/usageBySubOrg")
    @ApiOperation(value = "按下级机构统计使用情况", notes = "根据条件按下级机构统计证照使用情况")
    public Result<List<SubOrgUsageStatisticsDTO>> statisticsBySubOrg(
            @RequestBody CertificateUsageStatisticsVO param,
            HttpServletRequest request, HttpServletResponse response) {
        log.info("开始按下级机构统计证照使用情况，参数：{}", param);
        String currUserOrgCode = sysUserService.getCurrUserOrgCode();
        if (StrUtil.isBlank(currUserOrgCode)) {
            return ResultUtil.error("获取当前登录用户信息失败");
        }
        log.info("获取当前登录用户机构编码: {}", currUserOrgCode);
        // 部局账号不带上条件
        // 部局账号不带上条件
        if (!StrUtil.equals(currUserOrgCode,"01")) {
            param.setLoginUserOrgCode(currUserOrgCode);
        }else {
            param.setLoginUserOrgCode("");
        }
        // 参数校验
        if (param.getIssuerCodes() == null || param.getIssuerCodes().isEmpty()) {
            return ResultUtil.error("签发机构不能为空");
        }

        if (param.getUsageType() == null || param.getUsageType().isEmpty()) {
            return ResultUtil.error("使用类型不能为空");
        }

        if (param.getStatusCode() == null || param.getStatusCode().isEmpty()) {
            return ResultUtil.error("证照状态代码不能为空");
        }

        if (!param.getUsageType().equals("1") && !param.getUsageType().equals("2")) {
            return ResultUtil.error("使用类型无效，支持的类型：1-下载 2-核验");
        }

        // 如果未指定统计类型，默认按月统计
        if (param.getTimeType() == null || param.getTimeType().isEmpty()) {
            param.setTimeType("month");
        }

        try {
            // 使用HttpProxyUtil转发请求到第三方服务
            httpProxyUtil.forwardRequest(request, response, proxyConfig.getTargetUrl(), true,param);

            // 由于forwardRequest方法已经处理了响应，这里不需要返回值
            return null;
        } catch (Exception e) {
            log.error("证照使用情况按下级机构统计异常", e);
            return ResultUtil.error("统计异常：" + e.getMessage());
        }
    }

    /**
     * 按不同时间维度和下级机构统计使用情况
     * @param param 查询参数
     * @return 按不同时间维度和下级机构统计结果
     */
    @PostMapping("/usageByTimeTypeAndSubOrg")
    @ApiOperation(value = "按不同时间维度和下级机构统计使用情况", notes = "根据条件按不同时间维度和下级机构统计证照使用情况")
    public Result<List<TimeTypeSubOrgUsageStatisticsDTO>> statisticsByTimeTypeAndSubOrg(
            @RequestBody CertificateUsageStatisticsVO param,
            HttpServletRequest request, HttpServletResponse response) {
        log.info("开始按年份和下级机构统计证照使用情况，参数：{}", param);
        String currUserOrgCode = sysUserService.getCurrUserOrgCode();
        if (StrUtil.isBlank(currUserOrgCode)) {
            return ResultUtil.error("获取当前登录用户信息失败");
        }
        log.info("获取当前登录用户机构编码: {}", currUserOrgCode);
        // 部局账号不带上条件
        // 部局账号不带上条件
        if (!StrUtil.equals(currUserOrgCode,"01")) {
            param.setLoginUserOrgCode(currUserOrgCode);
        }else {
            param.setLoginUserOrgCode("");
        }
        // 参数校验
        if (param.getIssuerCodes() == null || param.getIssuerCodes().isEmpty()) {
            return ResultUtil.error("签发机构不能为空");
        }

        if (param.getStatusCode() == null || param.getStatusCode().isEmpty()) {
            return ResultUtil.error("证照状态代码不能为空");
        }

        if (param.getUsageType() == null || param.getUsageType().isEmpty()) {
            return ResultUtil.error("使用类型不能为空");
        }

        if (!param.getUsageType().equals("1") && !param.getUsageType().equals("2")) {
            return ResultUtil.error("使用类型无效，支持的类型：1-下载 2-核验");
        }

        if (!StrUtil.equalsAny(param.getTimeType(), "year", "quarter", "month", "day")) {
            return ResultUtil.error("不支持该时间维度");
        }

        try {
            // 使用HttpProxyUtil转发请求到第三方服务
            httpProxyUtil.forwardRequest(request, response, proxyConfig.getTargetUrl(),
                    true, param);
            // 由于forwardRequest方法已经处理了响应，这里不需要返回值
            return null;
        } catch (Exception e) {
            log.error("证照使用情况按年份和下级机构统计异常", e);
            return ResultUtil.error("统计异常：" + e.getMessage());
        }
    }

    /**
     * 按年份和下级机构统计使用情况
     * 
     * @param param 查询参数
     * @return 按年份和下级机构统计结果
     */
    @PostMapping("/usageByYearAndSubOrg")
    @ApiOperation(value = "按年份和下级机构统计使用情况", notes = "根据条件按年份和下级机构统计证照使用情况")
    public Result<List<TimeTypeSubOrgUsageStatisticsDTO>> statisticsByYearAndSubOrg(
            @RequestBody CertificateUsageStatisticsVO param,
            HttpServletRequest request, HttpServletResponse response) {
        log.info("开始按年份和下级机构统计证照使用情况，参数：{}", param);

        // 参数校验
        if (param.getLoginUserOrgCode() == null || param.getLoginUserOrgCode().isEmpty()) {
            return ResultUtil.error("登录人所在机构编码不能为空");
        }

        if (param.getIssuerCodes() == null || param.getIssuerCodes().isEmpty()) {
            return ResultUtil.error("签发机构不能为空");
        }

        if (param.getStatusCode() == null || param.getStatusCode().isEmpty()) {
            return ResultUtil.error("证照状态代码不能为空");
        }

        if (param.getUsageType() == null || param.getUsageType().isEmpty()) {
            return ResultUtil.error("使用类型不能为空");
        }

        if (!param.getUsageType().equals("1") && !param.getUsageType().equals("2")) {
            return ResultUtil.error("使用类型无效，支持的类型：1-下载 2-核验");
        }

        // 设置统计类型为年
        param.setTimeType("year");

        try {
            // 使用HttpProxyUtil转发请求到第三方服务
            httpProxyUtil.forwardRequest(request, response, proxyConfig.getTargetUrl(),
                    true);

            // 由于forwardRequest方法已经处理了响应，这里不需要返回值
            return null;
        } catch (Exception e) {
            log.error("证照使用情况按年份和下级机构统计异常", e);
            return ResultUtil.error("统计异常：" + e.getMessage());
        }
    }

    /**
     * 按下级机构统计使用情况并计算占比
     * 
     * @param param 查询参数
     * @return 按下级机构统计结果带占比
     */
    @PostMapping("/usageBySubOrgWithRatio")
    @ApiOperation(value = "按下级机构统计使用情况并计算占比", notes = "根据条件按下级机构统计证照使用情况并计算占比")
    public Result<List<SubOrgUsageRatioStatisticsDTO>> statisticsBySubOrgWithRatio(
            @RequestBody CertificateUsageStatisticsVO param,
            HttpServletRequest request, HttpServletResponse response) {
        log.info("开始按下级机构统计证照使用情况并计算占比，参数：{}", param);
        String currUserOrgCode = sysUserService.getCurrUserOrgCode();
        if (StrUtil.isBlank(currUserOrgCode)) {
            return ResultUtil.error("获取当前登录用户信息失败");
        }
        log.info("获取当前登录用户机构编码: {}", currUserOrgCode);
        // 部局账号不带上条件
        // 部局账号不带上条件
        if (!StrUtil.equals(currUserOrgCode,"01")) {
            param.setLoginUserOrgCode(currUserOrgCode);
        }else {
            param.setLoginUserOrgCode("");
        }
        // 参数校验
        if (param.getIssuerCodes() == null || param.getIssuerCodes().isEmpty()) {
            return ResultUtil.error("签发机构不能为空");
        }

        if (param.getStatusCode() == null || param.getStatusCode().isEmpty()) {
            return ResultUtil.error("证照状态代码不能为空");
        }

        if (param.getUsageType() == null || param.getUsageType().isEmpty()) {
            return ResultUtil.error("使用类型不能为空");
        }

        if (!param.getUsageType().equals("1") && !param.getUsageType().equals("2")) {
            return ResultUtil.error("使用类型无效，支持的类型：1-下载 2-核验");
        }

        // 如果未指定统计类型，默认按月统计
        if (param.getTimeType() == null || param.getTimeType().isEmpty()) {
            param.setTimeType("month");
        }

        try {
            // 使用HttpProxyUtil转发请求到第三方服务
            httpProxyUtil.forwardRequest(request, response, proxyConfig.getTargetUrl(),
                    true,param);

            // 由于forwardRequest方法已经处理了响应，这里不需要返回值
            return null;
        } catch (Exception e) {
            log.error("证照使用情况按下级机构统计并计算占比异常", e);
            return ResultUtil.error("统计异常：" + e.getMessage());
        }
    }

    /**
     * 按持证主体类别统计使用情况并计算占比
     * 
     * @param param 查询参数
     * @return 按持证主体类别统计结果带占比
     */
    @PostMapping("/usageByHolderCategoryWithRatio")
    @ApiOperation(value = "按持证主体类别统计使用情况并计算占比", notes = "根据条件按持证主体类别统计证照使用情况并计算占比")
    public Result<List<HolderCategoryUsageStatisticsDTO>> statisticsByHolderCategoryWithRatio(
            @RequestBody CertificateUsageStatisticsVO param,
            HttpServletRequest request, HttpServletResponse response) {
        log.info("开始按持证主体类别统计证照使用情况并计算占比，参数：{}", param);
        String currUserOrgCode = sysUserService.getCurrUserOrgCode();
        if (StrUtil.isBlank(currUserOrgCode)) {
            return ResultUtil.error("获取当前登录用户信息失败");
        }
        log.info("获取当前登录用户机构编码: {}", currUserOrgCode);
        // 部局账号不带上条件
        // 部局账号不带上条件
        if (!StrUtil.equals(currUserOrgCode,"01")) {
            param.setLoginUserOrgCode(currUserOrgCode);
        }else {
            param.setLoginUserOrgCode("");
        }
        // 参数校验
        if (param.getIssuerCodes() == null || param.getIssuerCodes().isEmpty()) {
            return ResultUtil.error("签发机构不能为空");
        }

        if (param.getStatusCode() == null || param.getStatusCode().isEmpty()) {
            return ResultUtil.error("证照状态代码不能为空");
        }

        if (param.getUsageType() == null || param.getUsageType().isEmpty()) {
            return ResultUtil.error("使用类型不能为空");
        }

        if (!param.getUsageType().equals("1") && !param.getUsageType().equals("2")) {
            return ResultUtil.error("使用类型无效，支持的类型：1-下载 2-核验");
        }

        // 如果未指定统计类型，默认按月统计
        if (param.getTimeType() == null || param.getTimeType().isEmpty()) {
            param.setTimeType("month");
        }

        try {
            // 使用HttpProxyUtil转发请求到第三方服务
            httpProxyUtil.forwardRequest(request, response,
                    proxyConfig.getTargetUrl(), true,param);

            // 由于forwardRequest方法已经处理了响应，这里不需要返回值
            return null;
        } catch (Exception e) {
            log.error("证照使用情况按持证主体类别统计并计算占比异常", e);
            return ResultUtil.error("统计异常：" + e.getMessage());
        }
    }

    /**
     * 按时间统计使用情况并计算占比
     * 
     * @param param 查询参数
     * @return 按时间统计结果带占比
     */
    @PostMapping("/usageByTimeWithRatio")
    @ApiOperation(value = "按时间统计使用情况并计算占比", notes = "根据条件按时间统计证照使用情况并计算占比")
    public Result<List<TimeUsageStatisticsDTO>> statisticsByTimeWithRatio(
            @RequestBody CertificateUsageStatisticsVO param,
            HttpServletRequest request, HttpServletResponse response) {
        log.info("开始按时间统计证照使用情况并计算占比，参数：{}", param);
        String currUserOrgCode = sysUserService.getCurrUserOrgCode();
        if (StrUtil.isBlank(currUserOrgCode)) {
            return ResultUtil.error("获取当前登录用户信息失败");
        }
        log.info("获取当前登录用户机构编码: {}", currUserOrgCode);
        // 部局账号不带上条件
        // 部局账号不带上条件
        if (!StrUtil.equals(currUserOrgCode,"01")) {
            param.setLoginUserOrgCode(currUserOrgCode);
        }else {
            param.setLoginUserOrgCode("");
        }
        // 参数校验
        if (param.getIssuerCodes() == null || param.getIssuerCodes().isEmpty()) {
            return ResultUtil.error("签发机构不能为空");
        }

        if (param.getStatusCode() == null || param.getStatusCode().isEmpty()) {
            return ResultUtil.error("证照状态代码不能为空");
        }

        if (param.getUsageType() == null || param.getUsageType().isEmpty()) {
            return ResultUtil.error("使用类型不能为空");
        }

        if (!param.getUsageType().equals("1") && !param.getUsageType().equals("2")) {
            return ResultUtil.error("使用类型无效，支持的类型：1-下载 2-核验");
        }

        // 如果未指定统计类型，默认按月统计
        if (param.getTimeType() == null || param.getTimeType().isEmpty()) {
            param.setTimeType("month");
        }

        try {
            // 使用HttpProxyUtil转发请求到第三方服务
            httpProxyUtil.forwardRequest(request, response, proxyConfig.getTargetUrl(),
                    true, param);

            // 由于forwardRequest方法已经处理了响应，这里不需要返回值
            return null;
        } catch (Exception e) {
            log.error("证照使用情况按时间统计并计算占比异常", e);
            return ResultUtil.error("统计异常：" + e.getMessage());
        }
    }

    /**
     * 按证照类型统计使用情况
     * @param param 查询参数
     * @return 按证照类型统计
     */
    @PostMapping("/usageByType")
    @ApiOperation(value = "按证照类型统计使用情况", notes = "根据条件按证照类型统计证照使用情况")
    public Result<List<CertificateTypeUsageStatisticsDTO>> statisticsByType(
            @RequestBody CertificateUsageStatisticsVO param,HttpServletRequest request, HttpServletResponse response) {
        log.info("开始按证照类型统计证照使用情况，参数：{}", param);

        // 参数校验
        if (param.getLoginUserOrgCode() == null || param.getLoginUserOrgCode().isEmpty()) {
            return ResultUtil.error("登录人所在机构编码不能为空");
        }

        if (param.getIssuerCodes() == null || param.getIssuerCodes().isEmpty()) {
            return ResultUtil.error("签发机构不能为空");
        }

        if (param.getStatusCode() == null || param.getStatusCode().isEmpty()) {
            return ResultUtil.error("证照状态代码不能为空");
        }

        // 如果未指定统计类型，默认按月统计
        if (param.getTimeType() == null || param.getTimeType().isEmpty()) {
            param.setTimeType("month");
        }

        try {
            // 使用HttpProxyUtil转发请求到第三方服务
            httpProxyUtil.forwardRequest(request, response, proxyConfig.getTargetUrl(),
                    true, param);

            // 由于forwardRequest方法已经处理了响应，这里不需要返回值
            return null;
        } catch (Exception e) {
            log.error("证照使用情况按证照类型统计并计算占比异常", e);
            return ResultUtil.error("统计异常：" + e.getMessage());
        }
    }
}