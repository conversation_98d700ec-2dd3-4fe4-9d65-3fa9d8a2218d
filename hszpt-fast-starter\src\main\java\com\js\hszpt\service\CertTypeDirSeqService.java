package com.js.hszpt.service;

import com.js.hszpt.mapper.CertTypeDirSeqDao;
import com.js.hszpt.entity.CertTypeDirSeq;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.core.common.utils.PageUtil;
import com.js.core.common.vo.PageVo;
import com.js.core.common.vo.SearchVo;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
 
/**
 * 
 * @ClassName:  CertTypeDirSeqService    
 * @Description:TODO(证照类型代码序号表接口实现)   
 * @author:  System Generation
 *
 */
@Slf4j
@Service
public class CertTypeDirSeqService extends ServiceImpl<CertTypeDirSeqDao,CertTypeDirSeq> {
	/**
	 * 多条件分页查询
	 * @param param
	 * @param searchVo
	 * @param pageVo
	 * @return
	 */
	public Page<CertTypeDirSeq> findByCondition(CertTypeDirSeq param, SearchVo searchVo, PageVo pageVo) {
		Page<CertTypeDirSeq> page = PageUtil.initMpPage(pageVo);
		QueryWrapper<CertTypeDirSeq> queryWrapper = this.getCondition(param,searchVo);
		return this.page(page, queryWrapper);
	}
	
	/**
	 * 
	 * @Title: findByCondition   
	 * @Description: TODO(多条件列表查询)   
	 * @param param
	 * @param searchVo
	 * @return  List<CertTypeDirSeq>      
	 * @throws
	 */
	public List<CertTypeDirSeq> findByCondition(CertTypeDirSeq param, SearchVo searchVo){
		QueryWrapper<CertTypeDirSeq> queryWrapper = this.getCondition(param, searchVo);
		//添加排序字段
		queryWrapper.orderByAsc("create_time");
		
		return this.list(queryWrapper);
	}
	
	
	/**
	 * 
	 * @Title: getCondition   
	 * @Description: TODO(构建自定义查询条件)   
	 * @param param
	 * @param searchVo
	 * @return  QueryWrapper<CertTypeDirSeq>      
	 * @throws
	 */
	private QueryWrapper<CertTypeDirSeq> getCondition(CertTypeDirSeq param, SearchVo searchVo){
		QueryWrapper<CertTypeDirSeq> queryWrapper = new QueryWrapper<CertTypeDirSeq>();
		//请根据实际字段自行扩展
		 
		// 创建时间
		if (StrUtil.isNotBlank(searchVo.getStartDate()) && StrUtil.isNotBlank(searchVo.getEndDate())) {
			Date start = DateUtil.parse(searchVo.getStartDate());
			Date end = DateUtil.parse(searchVo.getEndDate());
			queryWrapper.between("create_time",start, DateUtil.endOfDay(end));
		}
		return queryWrapper;
		
	}

	/**
	 * 根据机构编码获取序列值
	 * @param orgCode 机构编码
	 * @return 序列值
	 */
	public Integer getSeqValueByOrgCode(String orgCode) {
		log.info("开始查询序列值，机构编码：{}", orgCode);
		
		try {
			// 使用baseMapper直接查询
			QueryWrapper<CertTypeDirSeq> queryWrapper = new QueryWrapper<>();
			queryWrapper.eq("org_code", orgCode)
					   .last("limit 1");
			
			// 使用baseMapper.selectList而不是getOne
			List<CertTypeDirSeq> list = baseMapper.selectList(queryWrapper);
			
			if (list != null && !list.isEmpty()) {
				Integer seqValue = list.get(0).getSeqValue();
				log.info("查询到的序列值：{}", seqValue);
				return seqValue;
			}
			
			log.warn("未查询到序列值，机构编码：{}", orgCode);
			return null;
		} catch (Exception e) {
			log.error("查询序列值失败，机构编码：{}", orgCode, e);
			return null;
		}
	}

	/**
	 * 更新机构的序列值
	 * @param orgCode 机构编码
	 * @return 更新后的序列值
	 */
	public Integer updateSeqValueByOrgCode(String orgCode) {
		log.info("开始更新序列值，机构编码：{}", orgCode);
		
		try {
			// 1. 先查询当前序列值
			QueryWrapper<CertTypeDirSeq> queryWrapper = new QueryWrapper<>();
			queryWrapper.eq("org_code", orgCode)
					   .last("limit 1");
			
			CertTypeDirSeq seq = this.getOne(queryWrapper);
			
			if (seq == null) {
				log.warn("未找到机构序列记录，机构编码：{}", orgCode);
				return null;
			}
			
			// 2. 更新序列值和更新时间
			Integer newSeqValue = seq.getSeqValue() + 1;
			
			// 3. 使用LambdaUpdateWrapper按org_code更新
			LambdaUpdateWrapper<CertTypeDirSeq> updateWrapper = new LambdaUpdateWrapper<>();
			updateWrapper.eq(CertTypeDirSeq::getOrgCode, orgCode)
						.set(CertTypeDirSeq::getSeqValue, newSeqValue)
						.set(CertTypeDirSeq::getUpdateTime, new Date());
			
			// 4. 执行更新
			boolean success = this.update(updateWrapper);
			
			if (success) {
				log.info("更新序列值成功，机构编码：{}，新序列值：{}", orgCode, newSeqValue);
				return newSeqValue;
			} else {
				log.error("更新序列值失败，机构编码：{}", orgCode);
				return null;
			}
		} catch (Exception e) {
			log.error("更新序列值异常，机构编码：{}", orgCode, e);
			return null;
		}
	}
}