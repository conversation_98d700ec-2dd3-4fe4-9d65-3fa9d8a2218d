package com.js.hszpt.util;

import com.js.hszpt.util.SM2Utils;
import com.google.gson.Gson;
import com.google.gson.JsonObject;

import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;

public class SM2Test {

    // 接口地址
    private static final String API_URL = "https://tst.shpicc.cn/envir/api/queryPolicy";

    public static void main(String[] args) {
        try {
            // 要查询的保单号
            String policyNo = "PCAE202431000000000383";

            // 构造请求数据
            String plainText = String.format("{\"policyNo\":\"%s\"}", policyNo);

            // 打印请求参数
            System.out.println("请求参数（明文）：" + plainText);

            // 使用SM2加密
            String encryptedData = SM2Utils.decode(plainText);

            // 构造请求JSON
            String requestJson = String.format("{\"uuid\":\"%s\",\"timestamp\":%d,\"data\":\"%s\"}",
                    "UNIQUE_ID_123", System.currentTimeMillis(), encryptedData);

            // 打印加密后的请求数据
            System.out.println("加密后的请求数据：" + requestJson);

            // 发送HTTP POST请求
            String responseJson = sendPostRequest(requestJson);

            // 打印原始响应
            System.out.println("原始响应：" + responseJson);

            // 解析并解密响应
            JsonObject responseObject = new Gson().fromJson(responseJson, JsonObject.class);
            String encryptedResponseData = responseObject.get("data").getAsString();

            // 打印解密前的响应数据
            System.out.println("解密前的响应数据：" + encryptedResponseData);
            // 使用 SM2 解密
            String decryptedResponse = SM2Utils.encode(encryptedResponseData);

            // 打印解密后的响应数据
            System.out.println("解密后的响应数据：" + decryptedResponse);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static String sendPostRequest(String jsonData) throws Exception {
        URL url = new URL(API_URL);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();

        // 设置请求方法和参数
        conn.setRequestMethod("GET");
        conn.setRequestProperty("Content-Type", "application/json; utf-8");
        conn.setRequestProperty("Accept", "application/json");
        conn.setDoOutput(true);

        // 发送POST数据
        try (OutputStream os = conn.getOutputStream()) {
            byte[] input = jsonData.getBytes("utf-8");
            os.write(input, 0, input.length);
        }

        // 读取响应
        int responseCode = conn.getResponseCode();
        System.out.println("响应码: " + responseCode);

        if (responseCode == HttpURLConnection.HTTP_OK) { // 成功响应
            StringBuilder response = new StringBuilder();
            try (java.io.BufferedReader br = new java.io.BufferedReader(
                    new java.io.InputStreamReader(conn.getInputStream(), "utf-8"))) {
                String responseLine;
                while ((responseLine = br.readLine()) != null) {
                    response.append(responseLine.trim());
                }
            }
            return response.toString();
        } else {
            return String.format("{\"error\":\"HTTP error code: %d\"}", responseCode);
        }
    }
}
