package com.js.hszpt.api;


import cn.hutool.core.util.StrUtil;
import com.js.common.entity.CurrentUser;
import com.js.hszpt.entity.CertTypeApproval;
import com.js.hszpt.entity.SysDeptIamb;
import com.js.hszpt.service.CertTypeApprovalService;
import com.js.hszpt.service.CertTypeDirectoryService;
import com.js.hszpt.service.SysDeptIambService;
import com.js.hszpt.service.SysUserService;
import com.js.hszpt.vo.CertTypeApprovalSubmitVO;
 
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.js.core.api.BaseApiPlus;
import com.js.core.common.utils.ResultUtil;
import com.js.core.common.vo.PageVo;
import com.js.core.common.vo.Result;
import com.js.core.common.vo.SearchVo;
import com.js.common.service.SecurityService;
import com.js.annotation.SystemLog;
import com.js.enums.LogType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import java.util.List;


 /**
 * 
 * @ClassName: CertTypeApprovalApi  
 * @Description:TODO(证照类型目录审批表接口)   
 * @author:  System Generation  
 *
 */
@Slf4j
@RestController
@Api(description = "证照类型目录审批表接口")
@RequestMapping("/certTypeApproval")
public class CertTypeApprovalApi extends BaseApiPlus<CertTypeApprovalService,CertTypeApproval,String>{

	@Autowired
	private SysUserService sysUserService;

	@Autowired
	private SysDeptIambService sysDeptIambService;
     @Autowired
     private CertTypeDirectoryService certTypeDirectoryService;

	 @SystemLog(description = "证照类型目录审批表-分页查询", type = LogType.OPERATION)
	@RequestMapping(value = "/getPage", method = RequestMethod.GET)
	@ApiOperation(value = "分页查询")
	public Result<Page<CertTypeApproval>> getPage(@ModelAttribute CertTypeApproval param, @ModelAttribute SearchVo searchVo, @ModelAttribute PageVo pageVo) {
		Page<CertTypeApproval> page = this.baseService.findByCondition(param, searchVo, pageVo);
		return ResultUtil.data(page);
	}
	
	@SystemLog(description = "证照类型目录审批表-列表查询", type = LogType.OPERATION)
	@RequestMapping(value = "/getList", method = RequestMethod.GET)
	@ApiOperation(value = "列表查询")
	public Result<List<CertTypeApproval>> getList(@ModelAttribute CertTypeApproval param, @ModelAttribute SearchVo searchVo) {
		List<CertTypeApproval> list = this.baseService.findByCondition(param, searchVo);
		return ResultUtil.data(list);
	}
	
	@SystemLog(description = "证照类型目录审批-提交保存", type = LogType.OPERATION)
	@RequestMapping(value = "/submitApproval", method = RequestMethod.POST)
	@ApiOperation(value = "提交或保存审批")
	public Result<String> submitApproval(@ModelAttribute @Validated CertTypeApprovalSubmitVO submitVO) {
		log.info("开始处理证照类型目录审批提交/保存，参数：{}", submitVO);
		
		try {
			// 获取当前用户信息
			//todo 通过智慧海事接口获取
			CurrentUser user = sysUserService.getCurrUser();
			String currentUserId = user.getId();
			String currentUserName = user.getUsername();
			String currentOrgCode = sysUserService.getCurrUserOrgCode();

			SysDeptIamb dept = this.sysDeptIambService.getByOrgCode(currentOrgCode);
			if (null == dept) {
				return ResultUtil.error("获取用户机构信息失败");
			}

			String currentOrgName = dept.getName();
			String orgLevel = dept.getGovLevel();
			
			log.info("当前用户信息: 用户ID={}, 用户名={}, 机构编码={}, 机构名称={}, 机构级别={}", 
				currentUserId, currentUserName, currentOrgCode, currentOrgName, orgLevel);
			
			// 设置操作用户信息
			submitVO.setOperatorId(currentUserId);
			submitVO.setOperatorName(currentUserName);
			submitVO.setOrgCode(currentOrgCode);
			submitVO.setOrgName(currentOrgName);
			submitVO.setOrgLevel(orgLevel);
			// 判断操作类型，调用对应的处理方法
			if ("save".equals(submitVO.getOperationType())) {
				// 保存草稿
				this.baseService.saveApprovalDraft(submitVO);
				log.info("保存审批草稿成功，审批ID：{}", submitVO.getCertTypeApprApplyId());
				return ResultUtil.success("保存成功");
			} else if ("submit".equals(submitVO.getOperationType())) {
				// 提交审批 - 工作流处理逻辑已移至Service层
				this.baseService.submitApproval(submitVO);
				
				log.info("提交审批成功，审批ID：{}", submitVO.getCertTypeApprApplyId());
				return ResultUtil.success("提交成功");
			} else {
				log.error("未知的操作类型: {}", submitVO.getOperationType());
				return ResultUtil.error("未知的操作类型");
			}
		} catch (Exception e) {
			log.error("处理证照类型目录审批提交/保存失败", e);
			return ResultUtil.error("操作失败：" + e.getMessage());
		}
	}
}
