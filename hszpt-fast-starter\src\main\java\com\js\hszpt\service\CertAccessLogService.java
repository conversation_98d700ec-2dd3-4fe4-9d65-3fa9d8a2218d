package com.js.hszpt.service;

import cn.hutool.http.HttpUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.js.common.entity.CurrentUser;
import com.js.common.service.SecurityService;
import com.js.hszpt.entity.Certificate;
import com.js.hszpt.entity.CtfCertificate;
import com.js.hszpt.mapper.CertAccessLogDao;
import com.js.hszpt.entity.CertAccessLog;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.core.common.utils.PageUtil;
import com.js.core.common.vo.PageVo;
import com.js.core.common.vo.SearchVo;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import java.util.Date;
import java.util.List;
import java.util.Map;
import com.js.util.JasyptUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 *
 * @ClassName:  CertAccessLogService
 * @Description:TODO(电子证照访问记录表接口实现)
 * @author:  System Generation
 *
 */
@Slf4j
@Service
public class CertAccessLogService extends ServiceImpl<CertAccessLogDao,CertAccessLog> {

    @Autowired
    private  SecurityService securityService;

    @Autowired
	private SysUserService sysUserService;

    @Autowired
    private CertificateService certificateService;

    @Value("${proxy.target-url}")
    private String targetUrl;

	/**
	 * 多条件分页查询
	 * @param param
	 * @param searchVo
	 * @param pageVo
	 * @return
	 */
	public Page<CertAccessLog> findByCondition(CertAccessLog param, SearchVo searchVo, PageVo pageVo) {
		Page<CertAccessLog> page = PageUtil.initMpPage(pageVo);
		QueryWrapper<CertAccessLog> queryWrapper = this.getCondition(param,searchVo);
		return this.page(page, queryWrapper);
	}

	/**
	 *
	 * @Title: findByCondition
	 * @Description: TODO(多条件列表查询)
	 * @param param
	 * @param searchVo
	 * @return  List<CertAccessLog>
	 * @throws
	 */
	public List<CertAccessLog> findByCondition(CertAccessLog param, SearchVo searchVo){
		QueryWrapper<CertAccessLog> queryWrapper = this.getCondition(param, searchVo);
		//添加排序字段
		queryWrapper.orderByAsc("create_time");

		return this.list(queryWrapper);
	}


	/**
	 *
	 * @Title: getCondition
	 * @Description: TODO(构建自定义查询条件)
	 * @param param
	 * @param searchVo
	 * @return  QueryWrapper<CertAccessLog>
	 * @throws
	 */
	private QueryWrapper<CertAccessLog> getCondition(CertAccessLog param, SearchVo searchVo){
		QueryWrapper<CertAccessLog> queryWrapper = new QueryWrapper<CertAccessLog>();
		//请根据实际字段自行扩展

		// 创建时间
		if (StrUtil.isNotBlank(searchVo.getStartDate()) && StrUtil.isNotBlank(searchVo.getEndDate())) {
			Date start = DateUtil.parse(searchVo.getStartDate());
			Date end = DateUtil.parse(searchVo.getEndDate());
			queryWrapper.between("create_time",start, DateUtil.endOfDay(end));
		}
		return queryWrapper;

	}

	public static void main(String[] args) {

		System.out.println(JasyptUtil.encyptPwd("jsdp", "rs@2015#0505"));

		String encryptPwd = JasyptUtil.decyptPwd("jsdp","s4AzKRUmHOxCraqbap71YAM1ZKOinB4nDvWuwcd2uHQ=");
		System.out.println(encryptPwd);

	}

    /**
     *
     * @Title: certOperationLog
     * @Description: TODO(电子证照查询、电子证照核验逻辑配合统计功能进行改造，记录次数，
     * @param accessSource 访问来源
     * @param OperType 操作类型
     * @return  QueryWrapper<CertAccessLog>
     * @throws
     */
    public void certOperationLog(String accessSource, String OperType,String accessResult, String failReason,String certificateId){
        CertAccessLog certAccessLog = new CertAccessLog();
        // 访问来源 1-海事通APP 2-海事一网通办 3-智慧海事
        certAccessLog.setAccessSource(accessSource);
        // 操作类型：1-电子证照下载 2-查询 3-海事通APP扫描核验 4-其他APP扫码核验
        certAccessLog.setOperType(OperType);
        try {
            CurrentUser currUser = sysUserService.getCurrUser();
            if (currUser != null && StrUtil.isNotBlank(currUser.getId())) {
                certAccessLog.setAccessUserId(currUser.getId());
                certAccessLog.setAccessUserName(currUser.getUsername());
//                certAccessLog.setAccessIp(securityService.getRequest().getRemoteAddr());
            }
		} catch (Exception e) {
			log.error("获取当前用户失败", e);
			certAccessLog.setAccessUserId("user");
			certAccessLog.setAccessUserName("外网用户");
			certAccessLog.setAccessIp("127.0.0.1");
		}

        // 访问结果：1-成功 0-失败
        certAccessLog.setAccessResult(accessResult);
        // 失败原因
        certAccessLog.setFailReason(failReason);
        //访问时间
        certAccessLog.setAccessTime(new Date());
        //创建时间
        certAccessLog.setCreateTime(new Date());
        certAccessLog.setCertificateId(StrUtil.isNotBlank(certificateId)?certificateId:" ");


//        if(StrUtil.isNotBlank(certificateId)){
//            //透传 query 服务 获取证书信息
//            String requestUrl = targetUrl + "/certificate/certById/" + certificateId;
//            try {
//                String response = HttpUtil.get(requestUrl);
//                log.info("接口响应: {}", response);
//                // 解析响应数据（示例：提取证书名称）
//                ObjectMapper objectMapper = new ObjectMapper();
//                Map<String, Object> responseMap = objectMapper.readValue(response, Map.class);
//                String certificateName = (String) responseMap.get("certificateName");
//                certAccessLog.setCertName(certificateName); // 假设存在该字段
//                certAccessLog.setCertTypeCode((String) responseMap.get("certificateTypeCode"));
//
//            } catch (Exception e) {
//                log.error("调用接口失败或解析数据异常", e);
//                certAccessLog.setFailReason("调用接口失败: " + e.getMessage());
//            }
//        }

        log.info("记录统计日志,accessSource = {},OperType = {}",accessSource,OperType);
        baseMapper.insert(certAccessLog);
    }
}
