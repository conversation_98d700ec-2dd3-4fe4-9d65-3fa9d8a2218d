package com.js.hszpt.api;


import com.js.hszpt.entity.CtfHandwrittenSignature;
import com.js.hszpt.service.CtfHandwrittenSignatureService;
 
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.js.core.api.BaseApiPlus;
import com.js.core.common.utils.ResultUtil;
import com.js.core.common.vo.PageVo;
import com.js.core.common.vo.Result;
import com.js.core.common.vo.SearchVo;
import com.js.annotation.SystemLog;
import com.js.enums.LogType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import java.util.List;


 /**
 * 
 * @ClassName: CtfHandwrittenSignatureApi  
 * @Description:TODO(证照手写签名表接口)   
 * @author:  System Generation  
 *
 */
@Slf4j
@RestController
@Api(description = "证照手写签名表接口")
@RequestMapping("/ctfHandwrittenSignature")
public class CtfHandwrittenSignatureApi extends BaseApiPlus<CtfHandwrittenSignatureService,CtfHandwrittenSignature,String>{

	@SystemLog(description = "证照手写签名表-分页查询", type = LogType.OPERATION)
	@RequestMapping(value = "/getPage", method = RequestMethod.GET)
	@ApiOperation(value = "分页查询")
	public Result<Page<CtfHandwrittenSignature>> getPage(@ModelAttribute CtfHandwrittenSignature param, @ModelAttribute SearchVo searchVo, @ModelAttribute PageVo pageVo) {
		Page<CtfHandwrittenSignature> page = this.baseService.findByCondition(param, searchVo, pageVo);
		return ResultUtil.data(page);
	}
	
	@SystemLog(description = "证照手写签名表-列表查询", type = LogType.OPERATION)
	@RequestMapping(value = "/getList", method = RequestMethod.GET)
	@ApiOperation(value = "列表查询")
	public Result<List<CtfHandwrittenSignature>> getList(@ModelAttribute CtfHandwrittenSignature param, @ModelAttribute SearchVo searchVo) {
		List<CtfHandwrittenSignature> list = this.baseService.findByCondition(param, searchVo);
		return ResultUtil.data(list);
	}
	
}
