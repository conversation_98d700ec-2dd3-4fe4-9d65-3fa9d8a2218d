package com.js.hszpt.api;


import com.js.hszpt.entity.CtfAffair;
import com.js.hszpt.service.CtfAffairService;
import com.js.hszpt.dto.AffairDropdownDTO;
 
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.js.core.api.BaseApiPlus;
import com.js.core.common.utils.ResultUtil;
import com.js.core.common.vo.PageVo;
import com.js.core.common.vo.Result;
import com.js.core.common.vo.SearchVo;
import com.js.annotation.SystemLog;
import com.js.enums.LogType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import java.util.List;


 /**
 * 
 * @ClassName: AffairApi  
 * @Description:TODO(事项表接口)   
 * @author:  System Generation  
 *
 */
@Slf4j
@RestController
@Api(description = "事项表接口")
@RequestMapping("/ctfAffair")
public class CtfAffairApi extends BaseApiPlus<CtfAffairService, CtfAffair,String>{

	@SystemLog(description = "事项表-分页查询", type = LogType.OPERATION)
	@RequestMapping(value = "/getPage", method = RequestMethod.GET)
	@ApiOperation(value = "分页查询")
	public Result<Page<CtfAffair>> getPage(@ModelAttribute CtfAffair param, @ModelAttribute SearchVo searchVo, @ModelAttribute PageVo pageVo) {
		Page<CtfAffair> page = this.baseService.findByCondition(param, searchVo, pageVo);
		return ResultUtil.data(page);
	}
	
	@SystemLog(description = "事项表-列表查询", type = LogType.OPERATION)
	@RequestMapping(value = "/getList", method = RequestMethod.GET)
	@ApiOperation(value = "列表查询")
	public Result<List<CtfAffair>> getList(@ModelAttribute CtfAffair param, @ModelAttribute SearchVo searchVo) {
		List<CtfAffair> list = this.baseService.findByCondition(param, searchVo);
		return ResultUtil.data(list);
	}
	
	@SystemLog(description = "事项表-下拉列表", type = LogType.OPERATION)
	@RequestMapping(value = "/getDropdownList", method = RequestMethod.GET)
	@ApiOperation(value = "获取事项下拉列表")
	public Result<List<AffairDropdownDTO>> getDropdownList() {
		List<AffairDropdownDTO> list = this.baseService.getAffairDropdownList("all");
		return ResultUtil.data(list);
	}

	 @SystemLog(description = "事项表-下拉列表(船舶)", type = LogType.OPERATION)
	 @RequestMapping(value = "/getDropdownListShip", method = RequestMethod.GET)
	 @ApiOperation(value = "获取事项下拉列表(船舶)")
	 public Result<List<AffairDropdownDTO>> getDropdownListShip() {
		 List<AffairDropdownDTO> list = this.baseService.getAffairDropdownList("ship");
		 return ResultUtil.data(list);
	 }
	
}
