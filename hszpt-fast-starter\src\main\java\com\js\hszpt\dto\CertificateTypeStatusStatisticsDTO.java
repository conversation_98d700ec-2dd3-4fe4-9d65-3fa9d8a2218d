package com.js.hszpt.dto;

import lombok.Data;

/**
 * 证照类型状态统计结果DTO
 */
@Data
public class CertificateTypeStatusStatisticsDTO {

    /**
     * 一级证照类型代码
     */
    private String firstTypeCode;

    /**
     * 一级证照类型名称
     */
    private String firstTypeName;

    /**
     * 证照类型代码
     */
    private String certificateTypeCode;
    
    /**
     * 证照类型名称
     */
    private String certificateTypeName;
    
    /**
     * 签发数量总和
     */
    private Long issueCount;
    
    /**
     * 签发数量占比（百分比）
     */
    private Double issueRatio;
    
    /**
     * 有效证照数量
     */
    private Long validCount;
    
    /**
     * 无效证照数量
     */
    private Long invalidCount;
    
    /**
     * 环比率（百分比）
     */
    private Double chainRatio;
    
    /**
     * 是否为汇总数据
     */
    private Boolean isSummary = false;
} 