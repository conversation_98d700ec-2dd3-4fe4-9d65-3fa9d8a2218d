package com.js.hszpt.job;

import com.js.hszpt.service.CertificateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class DebrisCertJob {

    @Value("${job.oilDamageInsuranceCert.enabled:false}")
    private boolean enabled;

    @Autowired
    private CertificateService certificateService;

    @Scheduled(cron = "${job.oilDamageInsuranceCert.cron}")
    public void execute() {
        if (!enabled) {
            return;
        }
        long start = System.currentTimeMillis();
        log.info("【残骸事项】电子证照信息生成任务执行开始：{}", start);
        // 创建残骸事项电子证照信息
        try {
            certificateService.createDebrisCert();
        } catch (Exception e) {
            log.error("【残骸事项】电子证照信息生成任务执行失败：{}", e.getMessage());
            e.printStackTrace();
        }
        long end = System.currentTimeMillis();
        log.info("【残骸事项】电子证照信息生成任务执行结束：{}", end);
        log.info("【残骸事项】电子证照信息生成任务执行耗时：{}ms", end - start);
    }
}
