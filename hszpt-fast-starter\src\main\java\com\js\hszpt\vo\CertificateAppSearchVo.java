package com.js.hszpt.vo;

import cn.hutool.core.bean.BeanUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.js.hszpt.entity.Certificate;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.text.SimpleDateFormat;
import java.util.Date;

@Data
public class CertificateAppSearchVo {

    // 证书Id
    private String certificateId;

    // 证书名称
    private String certificateName;

    // 证书英文名称
    private String certificateNameEn;

    // 证书编号
    private String certificateNum;

    // 证书类型
    private String certificateType;

    // 生效日期
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") // 用于Spring MVC绑定
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")    // 用于JSON序列化
    private Date effectDate;

    // 截止日期
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") // 用于Spring MVC绑定
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")    // 用于JSON序列化
    private Date expireDate;

    // 船舶名称
    private String shipName;

    // 船舶英文名称
    private String shipNameEn;

    // IMO船舶识别号
    private String shipImo;

    // 船舶编号或呼号
    private String shipCallSign;

    // 签发机关名称
    private String issuOrgNameCn;

    // 签发机关英文名称
    private String issuOrgNameEn;

    // 持证人姓名
    private String holderName;

    // 持有人身份标识号码
    private String holderIdentityNumber;

    // 是否有效
    private String status;

    // 是否有效英文
    private String statusEn;

    @DateTimeFormat(pattern = "yyyy-MM-dd") // 用于Spring MVC绑定
    @JsonFormat(pattern = "yyyy-MM-dd")    // 用于JSON序列化
    private Date issueDate;
}
