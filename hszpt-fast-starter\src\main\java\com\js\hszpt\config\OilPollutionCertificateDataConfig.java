package com.js.hszpt.config;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.js.hszpt.constants.Common;
import com.js.hszpt.constants.OilPollutionWarrantyConstant;
import com.js.hszpt.enmus.AffairApplyCleanStatus;
import com.js.hszpt.entity.*;
import com.js.hszpt.service.*;
//import com.js.hszpt.utils.OilDamageInsuranceCertUtil;
import com.js.hszpt.utils.OilPollutionCertificateUtil;
import com.js.hszpt.vo.OilPollutionCertificateVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


import java.util.*;


import static com.js.hszpt.enmus.CertificateCreateStatus.UN_CREATE;


@Component
@Slf4j
public class OilPollutionCertificateDataConfig {

    @Autowired
    private BizAffairInsuranceService bizAffairInsuranceService;

    @Autowired
    private BizDgrOpicService bizDgrOpicService;

    @Autowired
    private SysDeptEnService sysDeptEnService;

    @Autowired
    private BizEgApproveService bizEgApproveService;

    @Autowired
    private CertificateService certificateService;

    @Autowired
    private CertificateAttributeService certificateAttributeService;

    @Autowired
    private DictEgUserService dictEgUserService;
    @Autowired
    private DictEgOrgService dictEgOrgService;
    @Autowired
    private BizEgCertCheckService bizEgCertCheckService;
    @Autowired
    private DictEgPortService dictEgPortService;

    @Autowired
    private CertificateTypeService certificateTypeService;
    @Autowired
    private DictEgPublicService dictEgPublicService;
    @Autowired
    private BizAffairApplyService bizAffairApplyService;
    @Autowired
    private OfficerInfoService officerInfoService;
    @Autowired
    private DictYthOrgMappingService dictYthOrgMappingService;
    @Autowired
    private BizEgAcceptService bizEgAcceptService;

    @Autowired
    private ZwApplyOfficerInfoService zwApplyOfficerInfoService;

    public void createCertificate(BizAffairApply bizAffairApply) {
        OilPollutionCertificateVo oilPollutionCertificateVo = createCertificateVo(bizAffairApply);
        if (!oilPollutionCertificateVo.getFlag()) {
            log.error("缺少完整的申请数据，申请ApplyId:{}", bizAffairApply.getApplyId());
            return;
        }
        // 保存证书信息
        Certificate certificate = createoilPollutionCertificate(oilPollutionCertificateVo);

        setCertificateType(certificate, oilPollutionCertificateVo);

        log.info("证照主表开始入库。");
        certificateService.save(certificate);

        log.info("证照主表入库完成，开始整理照面信息。");
        Map<String, String> map = OilPollutionCertificateUtil.createOilDamageInsuranceCert(oilPollutionCertificateVo);
        // 保存证照照面属性信息表
        List<CertificateAttribute> certificateAttributeList = new ArrayList<>();
        String id = certificate.getId();

        log.info("油污照面信息开始整理，记录数：{}", map.size());
        log.info("船舶航线类型代码：{}", oilPollutionCertificateVo.getBizDgrOpic().getShipRouteCode());
        if (StringUtils.equals("1", oilPollutionCertificateVo.getBizDgrOpic().getShipRouteCode())) {
            OilPollutionWarrantyConstant.NON_PERSISTENT.forEach((key, value) -> {
                CertificateAttribute certificateAttribute = new CertificateAttribute();
                certificateAttribute.setCertificateId(id);
                certificateAttribute.setAttributeName(key);
                certificateAttribute.setAttributeColumnName(value);
                certificateAttribute.setAttributeValue(map.get(key));
                certificateAttribute.setCreateDate(new Date());
                certificateAttributeList.add(certificateAttribute);
            });
        } else if (StringUtils.equals("2", oilPollutionCertificateVo.getBizDgrOpic().getShipRouteCode())) {
            log.info("证书类型名称：{}", oilPollutionCertificateVo.getBizAffairInsurance().getInsurCertName());
            if (oilPollutionCertificateVo.getBizAffairInsurance().getInsurCertName().contains("非持久性油类污染损害民事责任保险或其他财务保证证书")) {
                OilPollutionWarrantyConstant.NON_PERSISTENT.forEach((key, value) -> {
                    CertificateAttribute certificateAttribute = new CertificateAttribute();
                    certificateAttribute.setCertificateId(id);
                    certificateAttribute.setAttributeName(key);
                    certificateAttribute.setAttributeColumnName(value);
                    certificateAttribute.setAttributeValue(map.get(key));
                    certificateAttribute.setCreateDate(new Date());
                    certificateAttributeList.add(certificateAttribute);
                });
            } else {
                if ("燃油污染损害民事责任保险或其他财务保证证书".equals(oilPollutionCertificateVo.getBizAffairInsurance().getInsurCertName())) {
                    OilPollutionWarrantyConstant.FUEL_POLLUTION
                            .forEach((key, value) -> {
                                CertificateAttribute certificateAttribute = new CertificateAttribute();
                                certificateAttribute.setCertificateId(id);
                                certificateAttribute.setAttributeName(key);
                                certificateAttribute.setAttributeColumnName(value);
                                certificateAttribute.setAttributeValue(map.get(key));
                                certificateAttribute.setCreateDate(new Date());
                                certificateAttributeList.add(certificateAttribute);
                            });
                } else {
                    OilPollutionWarrantyConstant.OIL_DAMAGE
                            .forEach((key, value) -> {
                                CertificateAttribute certificateAttribute = new CertificateAttribute();
                                certificateAttribute.setCertificateId(id);
                                certificateAttribute.setAttributeName(key);
                                certificateAttribute.setAttributeColumnName(value);
                                certificateAttribute.setAttributeValue(map.get(key));
                                certificateAttribute.setCreateDate(new Date());
                                certificateAttributeList.add(certificateAttribute);
                            });
                }
            }
        }
        log.info("油污照面信息开始入库，记录数：{}", certificateAttributeList.size());
        certificateAttributeService.saveBatch(certificateAttributeList);
        bizAffairApply.setCleanStatus(AffairApplyCleanStatus.CREATED.getCode());
        bizAffairApplyService.saveOrUpdate(bizAffairApply);
    }

    /**
     * 设置证书类型，调电子证照接口使用
     *
     * @param certificate
     * @param oilPollutionCertificateVo
     */
    private void setCertificateType(Certificate certificate, OilPollutionCertificateVo oilPollutionCertificateVo) {
        try {
            log.info("【燃油污染或非持久性油类污染】生成证照照面属性信息开始");
            // 根据国内航线和国际航线生成不同的证书信息
            log.info("船舶航线类型代码：{}", oilPollutionCertificateVo.getBizDgrOpic().getShipRouteCode());
            if (StringUtils.equals("1", oilPollutionCertificateVo.getBizDgrOpic().getShipRouteCode())) {
                certificate.setCertificateType("非持久性油类污染损害民事责任保险或其他财务保证证书");//非持久性油类污染损害民事责任保险或其他财务保证证书
                String certificateName = certificate.getCertificateName();
                if (certificateName.equals("油污损害民事责任保险或其他财务保证证书")) {
                    certificate.setCertificateType(certificateName + "（" + "国内" + "）");
                } else if (certificateName.equals("燃油污染损害民事责任保险或其他财务保证证书")) {
                    certificate.setCertificateType(certificateName + "（" + "国内" + "）");
                } else if (certificateName.equals("非持久性油类污染损害民事责任保险或其他财务保证证书")) {
                    // 证照类型
                    certificate.setCertificateType(certificateName);
                }

            } else if (StringUtils.equals("2", oilPollutionCertificateVo.getBizDgrOpic().getShipRouteCode())) {
                log.info("证书分类名称：{}", oilPollutionCertificateVo.getBizAffairInsurance().getInsurCertName());
                if (oilPollutionCertificateVo.getBizAffairInsurance().getInsurCertName().contains("非持久性油类污染损害民事责任保险或其他财务保证证书")) {
                    String certificateName = certificate.getCertificateName();
                    if (certificateName.equals("油污损害民事责任保险或其他财务保证证书")) {
                        certificate.setCertificateType(certificateName + "（" + "国内" + "）");
                    } else if (certificateName.equals("燃油污染损害民事责任保险或其他财务保证证书")) {
                        certificate.setCertificateType(certificateName + "（" + "国内" + "）");
                    } else if (certificateName.equals("非持久性油类污染损害民事责任保险或其他财务保证证书")) {
                        // 证照类型
                        certificate.setCertificateType(certificateName);
                    }

                } else {
                    if ("燃油污染损害民事责任保险或其他财务保证证书".equals(oilPollutionCertificateVo.getBizAffairInsurance().getInsurCertName())) {
                        certificate.setCertificateType("燃油污染损害民事责任保险或其他财务保证证书（国际）");//燃油污染损害民事责任保险或其他财务保证证书
                    } else {
                        certificate.setCertificateType("油污损害民事责任保险或其他财务保证证书（国际）");//油污损害民事责任保险或其他财务保证证书
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("【燃油污染或非持久性油类污染】生成证照照面属性信息异常:{}", e.getStackTrace());
        }
    }

    public OilPollutionCertificateVo createCertificateVo(BizAffairApply bizAffairApply) {
        OilPollutionCertificateVo oilPollutionCertificateVo = new OilPollutionCertificateVo();
        // 获取当前登录人
//        String username = SecurityContextHolder.getContext().getAuthentication().getName();
        // 处理人信息
//        SysUser sysUser = windowApplyService.getUserByUsernameOnly(username);
        DictEgUser dictEgUser = dictEgUserService.getOne(new LambdaQueryWrapper<DictEgUser>()
                .eq(DictEgUser::getIdcardNo, bizAffairApply.getUscc())
                .last("LIMIT 1")
        );
        if (dictEgUser == null) {
            dictEgUser = dictEgUserService.getOne(new LambdaQueryWrapper<DictEgUser>()
                    .eq(DictEgUser::getIdcardNo, bizAffairApply.getApplicantId())
                    .last("LIMIT 1")
            );
            if (dictEgUser == null) {
                log.error("缺少用户信息，申请业务标识号:{}", bizAffairApply.getApplyId());
                bizAffairApply.setCleanStatus("E3");    //缺少用户信息
                this.bizAffairApplyService.saveOrUpdate(bizAffairApply);
                return oilPollutionCertificateVo.setFlag(false);
            }
        }
        // 获取操作人机构信息
//        SysDept sysDept = windowApplyService.getDeptByDeptId(String.valueOf(sysUser.getDeptId()));
        DictEgOrg dictEgOrg = dictEgOrgService.getOne(new LambdaQueryWrapper<DictEgOrg>().eq(DictEgOrg::getCode, bizAffairApply.getAcceptOrgCode()).last("LIMIT 1"));
        // 获取发证机关中文和英文信息
        SysDeptEn sysDeptEn = sysDeptEnService.getSysDeptEn(dictYthOrgMappingService.getSrcOrgCode(bizAffairApply.getAcceptOrgCode()));
        // 如果是政务中心,取上级层级code
        if (dictEgOrg != null && "1".equals(dictEgOrg.getIfCenter())) {
//            sysDept = windowApplyService.getDeptByDeptId(sysDept.getParentId());
            dictEgOrg = dictEgOrgService.getOne(new LambdaQueryWrapper<DictEgOrg>().eq(DictEgOrg::getCode, dictEgOrg.getParentCode()).last("LIMIT 1"));
        }
        if (dictEgOrg == null) {
//            sysDept = windowApplyService.getDeptByCode(windowApply.getMechanism());
            dictEgOrg = dictEgOrgService.getOne(new LambdaQueryWrapper<DictEgOrg>().eq(DictEgOrg::getCode, bizAffairApply.getAcceptOrgCode()).last("LIMIT 1"));
        }
        // 获取保险信息
        BizAffairInsurance bizAffairInsurance = bizAffairInsuranceService.getByWindowApplyId(bizAffairApply.getApplyId());
        if (bizAffairInsurance == null) {
            log.error("缺少保险信息，申请业务标识号:{}", bizAffairApply.getApplyId());
            bizAffairApply.setCleanStatus("E8");    //缺少保险信息
            this.bizAffairApplyService.saveOrUpdate(bizAffairApply);
            return oilPollutionCertificateVo.setFlag(false);
        }
        if(null == bizAffairInsurance.getEffStartDate() ||
                null == bizAffairInsurance.getDeadLine()){
            bizAffairApply.setCleanStatus("E4");    //保险日期为空
            this.bizAffairApplyService.saveOrUpdate(bizAffairApply);
            log.error("保险信息日期为空，申请业务标识号:{}", bizAffairApply.getApplyId());
            return oilPollutionCertificateVo.setFlag(false);
        }

        // 获取船舶信息
        BizDgrOpic bizDgrOpic = bizDgrOpicService.getByWindowApplyId(bizAffairApply.getApplyId());
        if (bizDgrOpic == null) {
            log.error("缺少船舶信息，申请业务标识号:{}", bizAffairApply.getApplyId());
            bizAffairApply.setCleanStatus("EA");    //缺少船舶信息
            this.bizAffairApplyService.saveOrUpdate(bizAffairApply);
            return oilPollutionCertificateVo.setFlag(false);
        }
        // 证书信息
//        CertCheck certCheckOne = certCheckService.getCertCheckByWindowApplyId(bizAffairApply.getApplyId());
        BizEgCertCheck bizEgCertCheck = bizEgCertCheckService.getOne(new LambdaQueryWrapper<BizEgCertCheck>()
                .eq(BizEgCertCheck::getApplyId, bizAffairApply.getApplyId())
                .eq(BizEgCertCheck::getCheckFlagCode, Common.CheckResult.PASS_CERT_CHECK).last("LIMIT 1"));
//        if (bizEgCertCheck == null){
//            log.error("缺少证书信息，申请业务标识号:{}",bizAffairApply.getApplyId());
//            return oilPollutionCertificateVo.setFlag(false);
//        }
        // 获取船籍港英文
//        QueryWrapper<EmiDictPort> emiDictPortQuery = new QueryWrapper<>();
//        emiDictPortQuery.eq("PORT_NAME_CN", applyShipInfo.getVRegistryPort());
//        emiDictPortQuery.eq("ACTIVE", "1");
//        emiDictPortQuery.isNotNull("PORT_NAME_EN");
//        EmiDictPort emiDictPort = emiDictPortService.getOne(emiDictPortQuery, false);
        DictEgPort dictEgPort = dictEgPortService.getOne(new LambdaQueryWrapper<DictEgPort>()
                .eq(DictEgPort::getPortName, bizDgrOpic.getRegportName())
                .eq(DictEgPort::getValidFlagCode, "1")
                .isNotNull(DictEgPort::getPortNameEn)
                .last("LIMIT 1"));
        if (dictEgPort == null && !"非持久性油类污染损害民事责任保险或其他财务保证证书".equals(bizAffairInsurance.getInsurCertName())) {
            log.error("缺少港口信息，申请业务标识号:{}", bizAffairApply.getApplyId());
            log.error("【电子证照信息】港口名称：{}", bizDgrOpic.getRegportName());
            bizAffairApply.setCleanStatus("E1");    //缺少港口信息
            this.bizAffairApplyService.saveOrUpdate(bizAffairApply);
            return oilPollutionCertificateVo.setFlag(false);
        }
        // 签证官员
//        QueryWrapper<ApplyOfficerInfo> applyOfficerQuery = new QueryWrapper<>();
//        applyOfficerQuery.eq("WINDOW_APPLY_ID", windowApply.getWindowApplyId());
//        applyOfficerQuery.eq("PROCESSSTATUS", "审批");
//        ApplyOfficerInfo applyOfficerInfo = applyOfficerInfoService.getOne(applyOfficerQuery, false);

        ZwApplyOfficerInfo zwApplyOfficerInfo = zwApplyOfficerInfoService.getZwApplyOfficerInfoByWindowApplyId(bizAffairApply.getApplyId());
        if (zwApplyOfficerInfo == null && !"非持久性油类污染损害民事责任保险或其他财务保证证书".equals(bizAffairInsurance.getInsurCertName())) {
            log.error("缺少签证官员信息，申请业务标识号:{}", bizAffairApply.getApplyId());
            log.error("【电子证照信息】查询签证官员机构编码为：{}", bizAffairApply.getAcceptOrgCode());
            bizAffairApply.setCleanStatus("E2");    //缺少签证官员信息
            this.bizAffairApplyService.saveOrUpdate(bizAffairApply);
            return oilPollutionCertificateVo.setFlag(false);
        }
        // 获取审批信息
        BizEgApprove bizEgApprove = bizEgApproveService.getBizEgApproveByApplyId(bizAffairApply.getApplyId());
        if (bizEgApprove == null) {
            log.error("缺少审批信息，申请业务标识号:{}", bizAffairApply.getApplyId());
            bizAffairApply.setCleanStatus("E5");    //缺少审批信息
            this.bizAffairApplyService.saveOrUpdate(bizAffairApply);
            return oilPollutionCertificateVo.setFlag(false);
        }

        // 获取受理信息
        BizEgAccept bizEgAccept = bizEgAcceptService.getByApplyId(bizAffairApply.getApplyId());
        if (bizEgAccept == null) {
            log.error("【电子证照信息】当前办件：{}，查询受理信息失败", bizAffairApply.getApplyId());
            bizAffairApply.setCleanStatus("E9");    //缺少受理信息
            this.bizAffairApplyService.saveOrUpdate(bizAffairApply);
            return null;
        }

        // 创建证书对象
        CertificateType certificateType = certificateTypeService.getCertificateTypeByName(bizAffairInsurance.getInsurCertName());
        return oilPollutionCertificateVo.setBizAffairApply(bizAffairApply)
                .setDictEgUser(dictEgUser)
                .setBizAffairInsurance(bizAffairInsurance)
                .setDictEgOrg(dictEgOrg)
                .setSysDeptEn(sysDeptEn)
                .setBizEgApprove(bizEgApprove)
                .setBizDgrOpic(bizDgrOpic)
                .setDictEgPort(dictEgPort)
                .setBizEgCertCheck(bizEgCertCheck)
                .setZwApplyOfficerInfo(zwApplyOfficerInfo)
                .setCertificateType(certificateType)
                .setBizEgAccept(bizEgAccept);
    }


    public Certificate createoilPollutionCertificate(OilPollutionCertificateVo oilPollutionCertificateVo) {

        Certificate certificate = new Certificate();
        certificate.setCertificateName(oilPollutionCertificateVo.getBizAffairInsurance().getInsurCertName());
//        certificate.setCertPrintNo(oilPollutionCertificateVo.getBizEgCertCheck().getCertNo());
        // 对应证照分类表的证照分类编码
        certificate.setCertificateTypeCode(oilPollutionCertificateVo.getCertificateType().getCertificateTypeCode());
        String srcOrgCode = dictYthOrgMappingService.getSrcOrgCode(oilPollutionCertificateVo.getBizAffairApply().getAcceptOrgCode());
        certificate.setIssuOrgNameCn(sysDeptEnService.getSysDeptEn(srcOrgCode).getOrgName());
        // 3.0编码
        certificate.setIssuOrgCode3(oilPollutionCertificateVo.getBizAffairApply().getAcceptOrgCode());
        //2.0编码
        certificate.setIssuOrgCode2(srcOrgCode);
        certificate.setHolderName(oilPollutionCertificateVo.getBizAffairApply().getApplicantName());
        certificate.setHolderIdentityNumber(oilPollutionCertificateVo.getBizAffairApply().getApplicantId());
        if (null != oilPollutionCertificateVo.getDictEgUser()) {
            certificate.setCreateOperId(String.valueOf(oilPollutionCertificateVo.getDictEgUser().getAccountId()));
            certificate.setModifyOperId(String.valueOf(oilPollutionCertificateVo.getDictEgUser().getAccountId()));
            certificate.setApplicantName(oilPollutionCertificateVo.getBizAffairApply().getApplicantName());
        }
        // 发证时间
        BizEgApprove bizEgApprove = oilPollutionCertificateVo.getBizEgApprove();
        Date issueDate = bizEgApprove != null ? bizEgApprove.getApprDate() : new Date();
        certificate.setIssueDate(issueDate);
        certificate.setEffectDate(DateUtil.parseDate(oilPollutionCertificateVo.getBizAffairInsurance().getEffStartDate()));
        certificate.setExpireDate(DateUtil.parseDate(oilPollutionCertificateVo.getBizAffairInsurance().getDeadLine()));
        certificate.setStatusFlag("1");
        certificate.setCreateStatus(UN_CREATE.getCode());
        certificate.setApplyId(oilPollutionCertificateVo.getBizAffairApply().getApplyId());
        certificate.setShipId(oilPollutionCertificateVo.getBizDgrOpic().getShipId());
        certificate.setShipName(oilPollutionCertificateVo.getBizDgrOpic().getShipName());
        certificate.setCreateDate(new Date());

        certificate.setModifyDate(new Date());
        // 14RYG 0018460 中间加空格
        String certificateNum = oilPollutionCertificateVo.getBizEgCertCheck().getCertNo();
        char[] chars = certificateNum.toCharArray();
        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < chars.length; i++) {//判断是否是英文
            if (i > 1 && (chars[i - 1] + "").matches("^[a-zA-Z\\s,.!?;:'\"-]+$") && (chars[i] + "").matches("^\\d+$")) {//判断是否是数字
                stringBuilder.append(" ").append(chars[i]);
            } else {
                stringBuilder.append(chars[i]);
            }
        }
        certificate.setCertificateNum(String.valueOf(stringBuilder));
        certificate.setAffairName(oilPollutionCertificateVo.getBizAffairApply().getAffairName());

        certificate.setApplyDate(oilPollutionCertificateVo.getBizAffairApply().getApplyDate());
        certificate.setApplyType(oilPollutionCertificateVo.getBizAffairApply().getApplyType());

        certificate.setAcceptOrgCode3(oilPollutionCertificateVo.getBizEgAccept().getMsaOrgCode());
        certificate.setAcceptDate(oilPollutionCertificateVo.getBizEgAccept().getCreateDate());
        // 受理机构2.0
        String acceptOrgCode2 = dictYthOrgMappingService.getSrcOrgCode(oilPollutionCertificateVo.getBizEgAccept().getMsaOrgCode());
        certificate.setAcceptOrgCode2(acceptOrgCode2);

        // 审批机构没有值，判断下标准层没值时，取受理机构即可
        if (StringUtils.isBlank(oilPollutionCertificateVo.getBizAffairApply().getApprOrgCode())) {
            certificate.setApprOrgCode3(oilPollutionCertificateVo.getBizEgAccept().getMsaOrgCode());
            certificate.setApprOrgCode2(acceptOrgCode2);
        }else{
            certificate.setApprOrgCode3(oilPollutionCertificateVo.getBizAffairApply().getApprOrgCode());
            String apprOrgCode2 = dictYthOrgMappingService.getSrcOrgCode(oilPollutionCertificateVo.getBizAffairApply().getApprOrgCode());
            certificate.setApprOrgCode2(apprOrgCode2);
        }

        certificate.setApprDate(oilPollutionCertificateVo.getBizEgApprove().getApprDate());
        certificate.setShipImo(oilPollutionCertificateVo.getBizDgrOpic().getShipImo());
        certificate.setShipCallSign(oilPollutionCertificateVo.getBizDgrOpic().getShipCallsign());
        certificate.setShipMmsi(oilPollutionCertificateVo.getBizDgrOpic().getShipMmsi());
        certificate.setShipNameEn(oilPollutionCertificateVo.getBizDgrOpic().getShipNameEn());
        certificate.setApplyNo(oilPollutionCertificateVo.getBizAffairApply().getApplyNo());
        certificate.setProType(oilPollutionCertificateVo.getBizAffairApply().getProType());
        return certificate;
    }

}

