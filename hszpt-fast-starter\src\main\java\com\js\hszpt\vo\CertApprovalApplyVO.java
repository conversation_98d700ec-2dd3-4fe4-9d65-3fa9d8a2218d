package com.js.hszpt.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

@Data
@ApiModel(value = "证照类型目录审批申请VO")
public class CertApprovalApplyVO {
    @ApiModelProperty("主键ID")
    private String id;

    @ApiModelProperty("申请编号，按规则生成")
    private String applyNo;

    @ApiModelProperty("证照类型目录ID")
    private String certTypeDirId;

    @ApiModelProperty("工作流实例ID")
    private String executionId;

    @ApiModelProperty("申请类型：1-新增 2-废止 3-启用")
    private String applyType;

    @ApiModelProperty("申请机构代码")
    private String applyOrgCode;

    @ApiModelProperty("申请机构名称")
    private String applyOrgName;

    @ApiModelProperty("受理机构代码")
    private String acceptOrgCode;

    @ApiModelProperty("受理机构名称")
    private String acceptOrgName;

    @ApiModelProperty("审批机构代码")
    private String approvalOrgCode;

    @ApiModelProperty("审批机构名称")
    private String approvalOrgName;

    @ApiModelProperty("发布机构代码")
    private String publishOrgCode;

    @ApiModelProperty("发布机构名称")
    private String publishOrgName;

    @ApiModelProperty("申请人ID")
    private String applicantId;

    @ApiModelProperty("申请人姓名")
    private String applicantName;

    @ApiModelProperty("申请原因说明")
    private String applyReason;

    @ApiModelProperty("申请时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date applyTime;

    @ApiModelProperty("受理时间")
    private Date acceptTime;

    @ApiModelProperty("审批时间")
    private Date approvalTime;

    @ApiModelProperty("发布时间")
    private Date publishTime;

    @ApiModelProperty("当前节点：1-申请 2-受理 3-审核 4-审批")
    private String currentNode;

    @ApiModelProperty("当前处理人ID")
    private String currentHandlerId;

    @ApiModelProperty("当前处理人姓名")
    private String currentHandlerName;

    @ApiModelProperty("状态：1-待审核 2-审核通过 3-审核不通过 4-审核退回 5-撤回 6-待审批 7-审批通过 8-审批不通过 9-审批退回")
    private String status;

    @ApiModelProperty("删除标志：0-正常，1-已删除")
    private String delFlag;

    @ApiModelProperty(value = "下发状态，1-未下发 2-已下发 3-已废止")
    private String issueStatus;
}