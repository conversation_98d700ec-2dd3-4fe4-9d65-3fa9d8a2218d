package com.js.hszpt.utils;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.js.hszpt.entity.BizEgCertCheck;
import com.js.hszpt.entity.ZwApplyOfficerInfo;

import java.util.HashMap;
import java.util.Map;

public class OfficerInfoUtil {

    public static Map<String,String> getOfficerInfoMap(BizEgCertCheck bizEgCertCheck, ZwApplyOfficerInfo zwApplyOfficerInfo, boolean flag) {
        // 签证官员(中文)
        String officerName = StrUtil.isNotBlank(zwApplyOfficerInfo.getOfficerName()) ? zwApplyOfficerInfo.getOfficerName() : "--";
        // 签证官员(英文)
        String officerNameEn = !flag ? " " : StrUtil.isNotBlank(zwApplyOfficerInfo.getOfficialNameEn()) ?
                zwApplyOfficerInfo.getOfficialNameEn().replaceAll("&", "＆") : "--";
        // 签证官员职务(中文)
        String position = StrUtil.isNotBlank(zwApplyOfficerInfo.getPosition()) ? zwApplyOfficerInfo.getPosition() : "--";
        // 签证官员职务(英文)
        String positionEn = !flag ? " " : StrUtil.isNotBlank(zwApplyOfficerInfo.getOfficialTitleEn()) ?
                zwApplyOfficerInfo.getOfficialTitleEn().replaceAll("&", "＆") : "--";
        // 证照编号
        String finalNumber = "";
        // 证照类型
        if (StrUtil.isNotBlank(bizEgCertCheck.getCertName())) {
            finalNumber = bizEgCertCheck.getCertName();
        }
        return MapUtil.builder(new HashMap<String,String>())
                .put("签证官员(中文)",officerName)
                .put("签证官员(英文)", officerNameEn)
                .put("签证官员职务(中文)",position)
                .put("签证官员职务(英文)", positionEn)
                .put("证照编号",finalNumber)
                .build();
    }
}
