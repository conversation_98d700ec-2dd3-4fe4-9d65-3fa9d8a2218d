package com.js.hszpt.service;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.hszpt.dto.CtfCmsHandbookQueryDTO;
import com.js.hszpt.entity.CtfCmsHandbook;
import com.js.hszpt.mapper.CtfCmsHandbookDao;
import com.js.util.SnowFlakeUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * @ClassName: CtfCmsHandbookService
 * @Description: 帮助手册-内容管理服务实现类
 * @author: System Generation
 */
@Service
public class CtfCmsHandbookService extends ServiceImpl<CtfCmsHandbookDao, CtfCmsHandbook> {

    @Autowired
    private SysUserService sysUserService;

    /**
     * 构建查询条件
     * 
     * @param queryDTO 查询参数
     * @return 查询条件构造器
     */
    private LambdaQueryWrapper<CtfCmsHandbook> buildQueryWrapper(CtfCmsHandbookQueryDTO queryDTO) {
        LambdaQueryWrapper<CtfCmsHandbook> queryWrapper = new LambdaQueryWrapper<>();

        // 设置查询条件
        if (queryDTO != null) {
            // 标题模糊查询
            if (StringUtils.isNotBlank(queryDTO.getTitle())) {
                queryWrapper.like(CtfCmsHandbook::getTitle, queryDTO.getTitle());
            }

            // 栏目分类
            if (StringUtils.isNotBlank(queryDTO.getColumnType())) {
                queryWrapper.eq(CtfCmsHandbook::getColumnType, queryDTO.getColumnType());
            }

            // 发布状态
            if (StringUtils.isNotBlank(queryDTO.getIsRelease())) {
                queryWrapper.eq(CtfCmsHandbook::getIsRelease, queryDTO.getIsRelease());
            }

            // 语言类型
            if (StringUtils.isNotBlank(queryDTO.getLangType())) {
                queryWrapper.eq(CtfCmsHandbook::getLangType, queryDTO.getLangType());
            }
        }

        // 只查询未删除的数据
        queryWrapper.eq(CtfCmsHandbook::getDelFlag, 0);

        // 按排序和创建时间排序
        queryWrapper.orderByAsc(CtfCmsHandbook::getSortOrder)
                .orderByDesc(CtfCmsHandbook::getCreateTime);

        return queryWrapper;
    }

    /**
     * 分页查询帮助手册内容
     * 
     * @param queryDTO 查询参数
     * @return 分页结果
     */
    public IPage<CtfCmsHandbook> pageList(CtfCmsHandbookQueryDTO queryDTO) {
        if (queryDTO == null) {
            queryDTO = new CtfCmsHandbookQueryDTO();
        }

        Page<CtfCmsHandbook> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());
        LambdaQueryWrapper<CtfCmsHandbook> queryWrapper = buildQueryWrapper(queryDTO);

        return this.page(page, queryWrapper);
    }

    /**
     * 不分页查询帮助手册内容
     * 
     * @param queryDTO 查询参数
     * @return 查询结果列表
     */
    public List<CtfCmsHandbook> listAll(CtfCmsHandbookQueryDTO queryDTO) {
        LambdaQueryWrapper<CtfCmsHandbook> queryWrapper = buildQueryWrapper(queryDTO);
        queryWrapper.eq(CtfCmsHandbook::getIsRelease, "1");
        return this.list(queryWrapper);
    }

    public CtfCmsHandbook getHandbookById(String id) {
        if (StringUtils.isBlank(id)) {
            return null;
        }
        return this.getById(id);
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean saveHandbook(CtfCmsHandbook handbook) {
        if (handbook == null) {
            return false;
        }

        String username = sysUserService.getCurrUsername();

        // 设置默认值
        handbook.setId(SnowFlakeUtil.getSnowflake().nextIdStr());
        handbook.setCreateBy(username);
        handbook.setIsRelease("0"); // 默认未发布
        handbook.setCreateTime(new Date());

        return this.save(handbook);
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean updateHandbook(CtfCmsHandbook handbook) {
        if (handbook == null || StringUtils.isBlank(handbook.getId())) {
            return false;
        }

        String username = sysUserService.getCurrUsername();
        handbook.setUpdateTime(new Date());
        handbook.setUpdateBy(username);

        return this.updateById(handbook);
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean removeHandbookById(String id) {
        if (StringUtils.isBlank(id)) {
            return false;
        }

        String username = sysUserService.getCurrUsername();

        // 逻辑删除
        CtfCmsHandbook handbook = new CtfCmsHandbook();
        handbook.setId(id);
        handbook.setDelFlag(1);
        handbook.setUpdateTime(new Date());
        handbook.setUpdateBy(username);

        return this.updateById(handbook);
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean publishHandbook(String id) {
        if (StringUtils.isBlank(id)) {
            return false;
        }

        String username = sysUserService.getCurrUsername();
        CtfCmsHandbook handbook = new CtfCmsHandbook();
        handbook.setId(id);
        handbook.setIsRelease("1"); // 已发布
        handbook.setIssueTime(new Date());
        handbook.setIssuePerson(username);
        handbook.setUpdateTime(new Date());
        handbook.setUpdateBy(username);

        return this.updateById(handbook);
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean offlineHandbook(String id) {
        if (StringUtils.isBlank(id)) {
            return false;
        }

        String username = sysUserService.getCurrUsername();

        CtfCmsHandbook handbook = new CtfCmsHandbook();
        handbook.setId(id);
        handbook.setIsRelease("2"); // 已下线
        handbook.setUpdateTime(new Date());
        handbook.setUpdateBy(username);

        return this.updateById(handbook);
    }
}