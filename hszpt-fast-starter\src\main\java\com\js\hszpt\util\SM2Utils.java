package com.js.hszpt.util;

import cn.hutool.core.codec.Base64;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.jce.interfaces.ECPublicKey;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import java.security.*;
import java.security.spec.*;

public class SM2Utils {
    private static final String publicKeyDer = "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEKjYbpn6jewQZfZe4UZ/457TpamDkdZVXmCONq1F7YfYJ22fENPXmMDOytxDTUyCmkluY4O+pvlcvhLT9H5fJMw==";
    private static final String privateKeyBytes = "MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQgrMoisjYNVku0gW49a54h0HNaSLOscOd69qtxf/nwgl2gCgYIKoEcz1UBgi2hRANCAAR2UPyqffFknGj/I3O8WB6a8/2f0Xs9uPTGfxx/4RCa9/sqX0Ojp5QQPIgYVAT6RuQbJpXpAAyhLRk6rFPSN4NY";

    static {
        Security.addProvider(new BouncyCastleProvider());
    }

    public static void main(String[] args) {
        try {
            //生成秘钥
            KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("EC", "BC");
            ECGenParameterSpec ecGenParameterSpec = new ECGenParameterSpec("sm2p256v1"); // 使用SM2曲线参数
//            keyPairGenerator.initialize(ecGenParameterSpec, new SecureRandom());
//            byte[] privatebyte = keyPairGenerator.generateKeyPair().getPrivate().getEncoded();
//            byte[] publicbyte = keyPairGenerator.generateKeyPair().getPublic().getEncoded();
            keyPairGenerator.initialize(ecGenParameterSpec, new SecureRandom());
            KeyPair keyPair = keyPairGenerator.generateKeyPair();
            byte[] privatebyte = keyPair.getPrivate().getEncoded();
            byte[] publicbyte =  keyPair.getPublic().getEncoded();
            System.out.println("公钥："+Base64.encode(publicbyte));
            System.out.println("私钥："+Base64.encode(privatebyte));
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        } catch (NoSuchProviderException e) {
            throw new RuntimeException(e);
        } catch (InvalidAlgorithmParameterException e) {
            throw new RuntimeException(e);
        }

    }

    /**
     *  国密SM2解密
     * @param data 解密内容
     * @return
     */
    public static String encode(String data) {
        try {
            // 生成SM2密钥对
            KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("EC", "BC");
            ECGenParameterSpec ecGenParameterSpec = new ECGenParameterSpec("sm2p256v1"); // 使用SM2曲线参数
            keyPairGenerator.initialize(ecGenParameterSpec, new SecureRandom());
            // 从字节恢复私钥
            PKCS8EncodedKeySpec privKeySpec = new PKCS8EncodedKeySpec(Base64.decode(privateKeyBytes));
            PrivateKey privateKey = KeyFactory.getInstance("EC", "BC").generatePrivate(privKeySpec);
            // 解密数据
            Cipher cipher = Cipher.getInstance("SM2", "BC");
            cipher.init(Cipher.DECRYPT_MODE, privateKey);
            byte[] decrypted = cipher.doFinal(hexToBytes(data));
            return new String(decrypted);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        } catch (NoSuchProviderException e) {
            throw new RuntimeException(e);
        } catch (InvalidAlgorithmParameterException e) {
            throw new RuntimeException(e);
        } catch (InvalidKeySpecException e) {
            throw new RuntimeException(e);
        } catch (NoSuchPaddingException e) {
            throw new RuntimeException(e);
        } catch (InvalidKeyException e) {
            throw new RuntimeException(e);
        } catch (IllegalBlockSizeException e) {
            throw new RuntimeException(e);
        } catch (BadPaddingException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     *  国密SM2加密
     * @param data 加密内容
     * @return
     */
    public static String decode(String data) {
        try {
            // 生成SM2密钥对
            KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("EC", "BC");
            ECGenParameterSpec ecGenParameterSpec = new ECGenParameterSpec("sm2p256v1"); // 使用SM2曲线参数
            keyPairGenerator.initialize(ecGenParameterSpec, new SecureRandom());
            X509EncodedKeySpec pubKeySpec = new X509EncodedKeySpec(Base64.decode(publicKeyDer));
            ECPublicKey publicKey = (ECPublicKey) KeyFactory.getInstance("EC", "BC").generatePublic(pubKeySpec);
            // 加密数据
            Cipher cipher = Cipher.getInstance("SM2", "BC");
            cipher.init(Cipher.ENCRYPT_MODE, publicKey);
            byte[] encrypted = cipher.doFinal(data.getBytes());
            return bytesToHex(encrypted);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        } catch (NoSuchProviderException e) {
            throw new RuntimeException(e);
        } catch (InvalidAlgorithmParameterException e) {
            throw new RuntimeException(e);
        } catch (InvalidKeySpecException e) {
            throw new RuntimeException(e);
        } catch (NoSuchPaddingException e) {
            throw new RuntimeException(e);
        } catch (InvalidKeyException e) {
            throw new RuntimeException(e);
        } catch (IllegalBlockSizeException e) {
            throw new RuntimeException(e);
        } catch (BadPaddingException e) {
            throw new RuntimeException(e);
        }
    }


    private static String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }

    public static byte[] hexToBytes(String hexStr) {
        hexStr = hexStr.replaceAll("\\s+", "").toLowerCase(); // 去除空格并统一为小写
        if (hexStr.length() % 2 != 0) {
            hexStr = "0" + hexStr; // 补前导零
        }
        byte[] bytes = new byte[hexStr.length() / 2];
        for (int i = 0; i < bytes.length; i++) {
            int high = Character.digit(hexStr.charAt(i * 2), 16) << 4;
            int low = Character.digit(hexStr.charAt(i * 2 + 1), 16);
            bytes[i] = (byte) (high | low);
        }
        return bytes;
    }
}
