package com.js.hszpt.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 证照回调请求参数
 */
@Data
@ApiModel(value = "证照回调请求参数")
public class CertificateCallbackRequest {

    /**
     * 证照ID
     */
    @ApiModelProperty(value = "证照ID", required = true, example = "CERT123456789")
    private String certificateId;

    /**
     * 回调状态码：0-成功，1-失败
     */
    @ApiModelProperty(value = "回调状态码：0-成功，1-失败", required = true, example = "0")
    private String code;

    /**
     * 回调消息
     */
    @ApiModelProperty(value = "回调消息", required = true, example = "证照生成成功")
    private String msg;
} 