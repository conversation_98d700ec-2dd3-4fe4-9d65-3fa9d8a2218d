package com.js.hszpt.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.sql.Timestamp;
import java.util.Date;

@Data
public class CertificateIntranetQueryQueryVo {
    // ID
    private String id;

    private String certificateId;

    // 申请编号
    private String applyNo;

    // 事务名称
    private String affairName;

    private String certificateStatus;

    // 申请人姓名
    private String applicantName;

    // 申请日期
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") // 用于Spring MVC绑定
    @JsonFormat(pattern = "yyyy-MM-dd")    // 用于JSON序列化
    private Timestamp applyDate;

    // 船舶名称
    private String shipName;

    // IMO船舶识别号
    private String shipImo;

    // 证书名称
    private String certificateName;

    // 证书类型
    private String certificateType;

    // 证书类型代码
    private String certificateTypeCode;

    // 签发机关
    private String issuOrgNameCn;

    // 证书编号
    private String certificateNum;

    // 证书状态
    private String statusFlag;

    // 颁发日期
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") // 用于Spring MVC绑定
    @JsonFormat(pattern = "yyyy-MM-dd")    // 用于JSON序列化
    private Date issueDate;

    // 生效日期(表：有效期日期-开始)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") // 用于Spring MVC绑定
    @JsonFormat(pattern = "yyyy-MM-dd")    // 用于JSON序列化
    private Date effectDate;

    // 截止日期
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") // 用于Spring MVC绑定
    @JsonFormat(pattern = "yyyy-MM-dd")    // 用于JSON序列化
    private Date expireDate;

    //持证人姓名
    private String holderName;

    // 持有人身份标识号码
    private String holderIdentityNumber;

    // 申请类型
    private String applyType;

    // 证书印刷号
    private String certPrintNo;

    // 许可证号
    private String licenseNum;

    // 受理机构代码
    private String acceptOrgCode3;

    // 受理机构
    private String acceptOrgName;

    // 受理日期
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") // 用于Spring MVC绑定
    @JsonFormat(pattern = "yyyy-MM-dd")    // 用于JSON序列化
    private Date acceptDate;

    // 审批日期
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") // 用于Spring MVC绑定
    @JsonFormat(pattern = "yyyy-MM-dd")    // 用于JSON序列化
    private Date apprDate;

    // 审批机构代码
    private String apprOrgCode3;

    // 审批机构
    private String apprOrgName;

    // 状态
    private String createStatus;
}
