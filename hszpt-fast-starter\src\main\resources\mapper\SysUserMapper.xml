<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.js.hszpt.mapper.SysUserDao">
    <!-- 工作流用户列表查询 -->
<!--    <select id="selectUserListForWorkflow" resultType="com.js.hszpt.vo.SysUserVo">-->
<!--        SELECT-->
<!--        "USER_ID",-->
<!--        "NAME",-->
<!--        "DEPARTMENT_NAME",-->
<!--        "DEPT_NAME",-->
<!--        "CERT_NO"-->
<!--        FROM-->
<!--        "hszpt"."SYS_USER"-->
<!--        WHERE-->
<!--        "DEPT_ID" = #{dto.deptId}-->
<!--        <if test="dto.name != null and dto.name != ''">-->
<!--            AND (-->
<!--            <foreach collection="dto.name.split(',')" item="item" separator=" OR ">-->
<!--                NAME LIKE '%' || #{item} || '%'-->
<!--            </foreach>-->
<!--            )-->
<!--        </if>-->
<!--        <if test="dto.departmentName != null and dto.departmentName != ''">-->
<!--            AND DEPARTMENT_NAME = #{dto.departmentName}-->
<!--        </if>-->
<!--    </select>-->
<!--    <select id="selectUserListForWorkflow" resultType="com.js.hszpt.vo.SysUserVo">-->
<!--        SELECT-->
<!--        "USER_ID",-->
<!--        "NAME",-->
<!--        "DEPARTMENT_NAME",-->
<!--        "DEPT_NAME",-->
<!--        "CERT_NO",-->
<!--        "USERNAME"-->
<!--        FROM-->
<!--        dzzz_sys_user-->
<!--        WHERE-->
<!--        "DEPT_ID" = #{dto.deptId}-->
<!--        <if test="dto.name != null and dto.name != ''">-->
<!--            AND  NAME LIKE '%' || #{dto.name} || '%'-->
<!--        </if>-->
<!--        <if test="dto.departmentName != null and dto.departmentName != ''">-->
<!--            AND DEPARTMENT_NAME = #{dto.departmentName}-->
<!--        </if>-->
<!--    </select>-->

    <select id="selectUserListForWorkflow" resultType="com.js.hszpt.vo.SysUserVo">
        SELECT
        "USER_ID",
        "NAME",
        "DEPARTMENT_NAME",
        "DEPT_NAME",
        "CERT_NO",
        "USERNAME"
        FROM
        dzzz_sys_user
        WHERE
        "DEPT_ID" = #{dto.deptId}
        <choose>
            <!-- 当传入部门名称时，按传入值查询 -->
            <when test="dto.departmentName != null and dto.departmentName != ''">
                AND DEPARTMENT_NAME = #{dto.departmentName}
            </when>
            <!-- 当未传入部门名称时，查询三个指定部门 -->
            <otherwise>
                AND DEPARTMENT_NAME IN (
                '法规规范处（执法督察处）',
                '法规规范处',
                '政策法规处'
                )
            </otherwise>
        </choose>
        <if test="dto.name != null and dto.name != ''">
            AND "NAME" LIKE '%' || #{dto.name} || '%'
        </if>
    </select>

</mapper>