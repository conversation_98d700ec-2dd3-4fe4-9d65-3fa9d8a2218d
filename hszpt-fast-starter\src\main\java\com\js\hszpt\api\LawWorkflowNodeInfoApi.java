package com.js.hszpt.api;


import com.alibaba.fastjson.JSONArray;
import com.js.hszpt.entity.LawWorkflowNodeInfo;
import com.js.hszpt.service.LawWorkflowNodeInfoService;
 
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.js.core.api.BaseApiPlus;
import com.js.core.common.utils.ResultUtil;
import com.js.core.common.vo.PageVo;
import com.js.core.common.vo.Result;
import com.js.core.common.vo.SearchVo;
import com.js.annotation.SystemLog;
import com.js.enums.LogType;
import com.js.hszpt.vo.LawWorkflowNodeInfoVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;


 /**
 * 
 * @ClassName: LawWorkflowNodeInfoApi  
 * @Description:TODO(工作流模板节点表接口)   
 * @author:  System Generation  
 *
 */
@Slf4j
@RestController
@Api(description = "工作流模板节点表接口")
@RequestMapping("/lawWorkflowNodeInfo")
public class LawWorkflowNodeInfoApi extends BaseApiPlus<LawWorkflowNodeInfoService,LawWorkflowNodeInfo,String>{

	@SystemLog(description = "工作流模板节点表-分页查询", type = LogType.OPERATION)
	@RequestMapping(value = "/getPage", method = RequestMethod.GET)
	@ApiOperation(value = "分页查询")
	public Result<Page<LawWorkflowNodeInfo>> getPage(@ModelAttribute LawWorkflowNodeInfo param, @ModelAttribute SearchVo searchVo, @ModelAttribute PageVo pageVo) {
		Page<LawWorkflowNodeInfo> page = this.baseService.findByCondition(param, searchVo, pageVo);
		return ResultUtil.data(page);
	}
	
	@SystemLog(description = "工作流模板节点表-列表查询", type = LogType.OPERATION)
	@RequestMapping(value = "/getList", method = RequestMethod.GET)
	@ApiOperation(value = "列表查询")
	public Result<List<LawWorkflowNodeInfo>> getList(@ModelAttribute LawWorkflowNodeInfo param, @ModelAttribute SearchVo searchVo) {
		List<LawWorkflowNodeInfo> list = this.baseService.findByCondition(param, searchVo);
		return ResultUtil.data(list);
	}

	 @ApiOperation("获取节点详情信息")
	 @GetMapping("/selectNodeInfoDetails")
	 public Result<List<LawWorkflowNodeInfoVo>> selectNodeInfLawWorkflowNodeInfoVooDetails(@RequestParam(value = "lawWorkflowNodeInfoId") String nodeInfoId) {
		 try {
			 List<LawWorkflowNodeInfoVo> lawWorkflowNodeInfoVos = this.baseService.selectNodeInfoDetails(nodeInfoId);
			 String result = JSONArray.toJSONString(lawWorkflowNodeInfoVos);
			 log.info("获取节点详情信息{}"+ result);
			 return ResultUtil.data(lawWorkflowNodeInfoVos);
		 } catch (Exception e) {
			 log.error("获取节点详情信息失败", e);
			 return ResultUtil.error("获取节点详情信息失败：" + e.getMessage());
		 }
	 }

}
