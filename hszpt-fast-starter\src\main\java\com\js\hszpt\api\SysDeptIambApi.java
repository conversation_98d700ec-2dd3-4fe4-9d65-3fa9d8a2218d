package com.js.hszpt.api;


import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.js.api.entity.User;
import com.js.hszpt.entity.SysDeptIamb;
import com.js.hszpt.entity.SysDeptInfoResponse;
import com.js.hszpt.entity.SysUser;
import com.js.hszpt.mapper.SysUserDao;
import com.js.hszpt.service.SysDeptIambService;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.js.core.api.BaseApiPlus;
import com.js.core.common.utils.ResultUtil;
import com.js.core.common.vo.PageVo;
import com.js.core.common.vo.Result;
import com.js.core.common.vo.SearchVo;
import com.js.annotation.SystemLog;
import com.js.enums.LogType;
import com.js.hszpt.service.SysUserService;
import com.js.hszpt.until.SessionUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.List;


/**
 *
 * @ClassName: SysDeptIambApi
 * @Description:TODO(组织信息表接口)
 * @author:  System Generation
 *
 */
@Slf4j
@RestController
@Api(description = "组织信息表接口")
@RequestMapping("/sysDeptIamb")
public class SysDeptIambApi extends BaseApiPlus<SysDeptIambService,SysDeptIamb,String> {

	@Autowired
	private SysDeptIambService sysDeptIambService;
	@Autowired
	private SysUserService sysUserService;

	@SystemLog(description = "组织信息表-分页查询", type = LogType.OPERATION)
	@RequestMapping(value = "/getPage", method = RequestMethod.GET)
	@ApiOperation(value = "分页查询")
	public Result<Page<SysDeptIamb>> getPage(@ModelAttribute SysDeptIamb param, @ModelAttribute SearchVo searchVo, @ModelAttribute PageVo pageVo) {
		Page<SysDeptIamb> page = this.baseService.findByCondition(param, searchVo, pageVo);
		return ResultUtil.data(page);
	}

	@SystemLog(description = "组织信息表-列表查询", type = LogType.OPERATION)
	@RequestMapping(value = "/getList", method = RequestMethod.GET)
	@ApiOperation(value = "列表查询")
	public Result<List<SysDeptIamb>> getList(@ModelAttribute SysDeptIamb param, @ModelAttribute SearchVo searchVo) {
		List<SysDeptIamb> list = this.baseService.findByCondition(param, searchVo);
		return ResultUtil.data(list);
	}

	/**
	 * 获取当前用户所属机构的层级
	 */
	@GetMapping("/getDeptLevel")
	public SysDeptIamb getDeptLevel() {
		try {
			// 获取部门信息
			SysDeptIamb dept = sysDeptIambService.getDeptLevel();
			return dept;

		} catch (Exception e) {
			log.error("获取部门层级信息失败", e);
			return null;
		}
	}


	/**
	 * 获取机构下的部门
	 *
	 * @param deptId
	 * @return
	 */
	@GetMapping("/deptSonOrgTree")
	public Result<List<SysDeptInfoResponse>> deptSonOrgTree(@RequestParam("deptId") String deptId) {
		try {
			log.info("当前机构ID: {}", deptId);
			List<SysDeptInfoResponse> sysDeptInfoResponses=sysDeptIambService.getSonOrgTree(deptId);
			return ResultUtil.data(sysDeptInfoResponses);

		} catch (Exception e) {
			log.error("获取部门层级信息失败", e);
			return ResultUtil.error("获取部门层级信息失败：" + e.getMessage());
		}
	}

	/**
	 *
	 * @param benCode 当前登录人的部门code
	 * @return
	 */
	@GetMapping("/current")
	@ApiOperation("获取当前登录的部门信息")
	public Result<List> getCurrentDeptInfo(@RequestParam("benCode")String benCode) {
		try {
//			 log.info("当前机构ID: {}", deptId);
			String benCode1=benCode+"%";
			List deptList = sysDeptIambService.getCurrentDeptInfo(benCode1);
			return ResultUtil.data(deptList);

		} catch (Exception e) {
			log.error("获取部门层级信息失败", e);
			return ResultUtil.error("获取部门层级信息失败：" + e.getMessage());
		}
	}

	@SystemLog(description = "获取机构统一社会信用代码", type = LogType.OPERATION)
	@RequestMapping(value = "/getUscc", method = RequestMethod.GET)
	@ApiOperation(value = "获取机构统一社会信用代码")
	public Result<String> getUscc() {
		// 获取用户机构编码
		String orgCode = sysUserService.getCurrUserOrgCode();

		if(StringUtils.isBlank(orgCode)){
			ResultUtil.error("获取用户机构信息失败.");
		}

		// 查询统一社会信用代码
		String uscc = this.baseService.getUsccByOrgCode(orgCode);

		return ResultUtil.data(uscc);
	}

    /**
     * 获取机构树形状接口
     *
     * @param orgCode 机构编码
     * @return 机构树形结构
     */
    @SystemLog(description = "获取机构树形结构", type = LogType.OPERATION)
    @GetMapping("/deptSon")
    @ApiOperation(value = "获取机构树形结构")
    public Result<List<SysDeptInfoResponse>> getSysDept(
            @RequestParam(value = "orgCode", required = true) String orgCode,String searchType) {

		try {
			log.info("获取机构树形结构，机构编码：{},层级{}", orgCode, searchType);

			// 处理机构编码
			String processedOrgCode = sysDeptIambService.processOrgCode(orgCode);
			log.info("原始orgCode: {}, 处理后orgCode: {}", orgCode, processedOrgCode);

			// 调用服务查询树形结构，始终查询下级
			List<SysDeptInfoResponse> result = sysDeptIambService.deptSon(processedOrgCode,searchType);

			return ResultUtil.data(result);

		} catch (Exception e) {
			log.error("获取机构树形结构信息失败", e);
			return ResultUtil.error("获取机构树形结构信息失败：" + e.getMessage());
		}
    }

	@ApiOperation("获取当前登录的机构信息")
	@GetMapping("/deptNow")
	public Result<List<SysDeptInfoResponse>>deptNow() {

		try {
			List<SysDeptInfoResponse> result = sysDeptIambService.deptNow();
			return ResultUtil.data(result);

		} catch (Exception e) {
			log.error("获取当前登录的机构信息失败", e);
			return ResultUtil.error("获取当前登录的机构信息失败：" + e.getMessage());
		}
	}
}
