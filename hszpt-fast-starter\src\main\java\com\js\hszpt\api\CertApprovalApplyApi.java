package com.js.hszpt.api;


import com.js.hszpt.entity.CertTypeApprovalApply;
import com.js.hszpt.service.CertTypeApprovalApplyService;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.js.core.api.BaseApiPlus;
import com.js.core.common.utils.ResultUtil;
import com.js.core.common.vo.PageVo;
import com.js.core.common.vo.Result;
import com.js.core.common.vo.SearchVo;
import com.js.annotation.SystemLog;
import com.js.enums.LogType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;import org.springframework.web.bind.annotation.*;
import java.util.List;


 /**
 *
 * @ClassName: CertApprovalApplyApi
 * @Description:TODO(证照类型目录审批申请表接口)
 * @author:  System Generation
 *
 */
@Slf4j
@RestController
@Api(description = "证照类型目录审批申请表接口")
@RequestMapping("/certApprovalApply")
public class CertApprovalApplyApi extends BaseApiPlus<CertTypeApprovalApplyService, CertTypeApprovalApply,String>{

	@SystemLog(description = "证照类型目录审批申请表-分页查询", type = LogType.OPERATION)
	@RequestMapping(value = "/getPage", method = RequestMethod.GET)
	@ApiOperation(value = "分页查询")
	public Result<Page<CertTypeApprovalApply>> getPage(@ModelAttribute CertTypeApprovalApply param, @ModelAttribute SearchVo searchVo, @ModelAttribute PageVo pageVo) {
		Page<CertTypeApprovalApply> page = this.baseService.findByCondition(param, searchVo, pageVo);
		return ResultUtil.data(page);
	}

	@SystemLog(description = "证照类型目录审批申请表-列表查询", type = LogType.OPERATION)
	@RequestMapping(value = "/getList", method = RequestMethod.GET)
	@ApiOperation(value = "列表查询")
	public Result<List<CertTypeApprovalApply>> getList(@ModelAttribute CertTypeApprovalApply param, @ModelAttribute SearchVo searchVo) {
		List<CertTypeApprovalApply> list = this.baseService.findByCondition(param, searchVo);
		return ResultUtil.data(list);
	}

    @SystemLog(description = "废止/启用重复校验", type = LogType.OPERATION)
    @RequestMapping(value = "/getCertTypeDirIdIsAbolish", method = RequestMethod.GET)
    @ApiOperation(value = "废止/启用重复校验")
    public Result<String> getCertTypeDirIdIsAbolish(
        @RequestParam String dirId,
        @RequestParam String applyType) {
        CertTypeApprovalApply apply = this.baseService.getCertTypeDirIdIsAbolish(dirId, applyType);
        if (apply == null || StringUtils.isBlank(apply.getApplyNo())) {
            //不为空 说明重复操作
            return ResultUtil.data("success");
        }
        return ResultUtil.data("error");
    }

}
