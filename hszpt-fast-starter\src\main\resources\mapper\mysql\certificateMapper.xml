<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.js.hszpt.mapper.CertificateMapper">
    <select id="certificateExternalNetworkQuery" resultType="com.js.hszpt.vo.certificateExternalNetworkQueryVo">
        select
        "ID","CERTIFICATE_ID","AFFAIR_NAME","APPLICANT_NAME","APPLY_DATE","SHIP_NAME","SHIP_NAME_EN",
        (select "ORG_NAME_EN" from SYS_DEPT_EN where "SYS_DEPT_EN"."ORG_CODE" = "ISSU_ORG_CODE2") as
        ISSU_ORG_NAME_EN,
        "CERTIFICATE_NAME","ISSU_ORG_NAME_CN","CERTIFICATE_NUM","CERTIFICATE_TYPE_CODE","HOLDER_NAME","ISSUE_DATE","EFFECT_DATE","EXPIRE_DATE","STATUS_FLAG"
        from "CTF_CERTIFICATE"
        <where>
            DEL_FLAG = '0' and CREATE_STATUS = '1' and "STATUS_FLAG" in ('1','-2')
            <if test="externalNetworkQueryDto.id != null and externalNetworkQueryDto.id != '' ">
                and ID = #{externalNetworkQueryDto.id}
            </if>
            <if test="externalNetworkQueryDto.certificateNum != null and externalNetworkQueryDto.certificateNum != '' ">
                and (CERTIFICATE_NUM = #{externalNetworkQueryDto.certificateNum} or CERTIFICATE_NUM = replace(#{externalNetworkQueryDto.certificateNum},' ',''))
            </if>
            <if test="externalNetworkQueryDto.shipName != null and externalNetworkQueryDto.shipName != '' ">
                and (SHIP_NAME = #{externalNetworkQueryDto.shipName} OR
                SHIP_NAME_EN = #{externalNetworkQueryDto.shipName})
            </if>
            <if test="externalNetworkQueryDto.certificateTypeCode != null and externalNetworkQueryDto.certificateTypeCode != '' ">
                and CERTIFICATE_TYPE_CODE = #{externalNetworkQueryDto.certificateTypeCode}
            </if>
            <!--            <if test="externalNetworkQueryDto.issuOrgCode3 != null and externalNetworkQueryDto.issuOrgCode3.size() > 0 ">-->
            <!--                and-->
            <!--                <foreach item="orgCode" index="index" collection="externalNetworkQueryDto.issuOrgCode3" open="(" separator="OR" close=")">-->
            <!--                    ISSU_ORG_CODE3 like CONCAT(#{orgCode},'%')-->
            <!--                </foreach>-->
            <!--            </if>-->
            <if test="externalNetworkQueryDto.shipImo != null and externalNetworkQueryDto.shipImo != '' ">
                and SHIP_IMO = #{externalNetworkQueryDto.shipImo}
            </if>
            <if test="externalNetworkQueryDto.shipCallSign != null and externalNetworkQueryDto.shipCallSign != '' ">
                and SHIP_CALL_SIGN = #{externalNetworkQueryDto.shipCallSign}
            </if>
            order by CREATE_DATE desc
        </where>
    </select>
    <select id="certificateIntranetQueryQuery" resultType="com.js.hszpt.vo.CertificateIntranetQueryQueryVo">
        WITH RECURSIVE type_hierarchy AS (
        -- 初始查询：从根节点开始，标记根节点编码
        SELECT CERT_TYPE_DIR_ID,
        CERTIFICATE_TYPE_CODE,
        CERTIFICATE_TYPE_NAME,
        PARENT_ID,
        CERTIFICATE_TYPE_CODE AS first_type_code, -- 根节点的祖先编码为自己
        CERTIFICATE_TYPE_NAME AS first_type_name
        FROM CERT_TYPE_DIRECTORY
        WHERE PARENT_ID = '-1' -- 根据实际根节点特征调整
        UNION ALL
        -- 递归查询：继承父节点的祖先编码
        SELECT child.CERT_TYPE_DIR_ID,
        child.CERTIFICATE_TYPE_CODE,
        child.CERTIFICATE_TYPE_NAME,
        child.PARENT_ID,
        parent.first_type_code, -- 始终继承初始根节点编码
        parent.first_type_name
        FROM CERT_TYPE_DIRECTORY child
        INNER JOIN
        type_hierarchy parent
        ON child.PARENT_ID = parent.CERT_TYPE_DIR_ID)
        SELECT
            certificate."ID",
            certificate."CERTIFICATE_ID",
            certificate."APPLY_NO",
            certificate."AFFAIR_NAME",
            certificate."APPLY_NO",
            certificate."APPLICANT_NAME",
            certificate."APPLY_DATE",
            certificate."SHIP_NAME",
            certificate."CERTIFICATE_NAME",
            (th.first_type_name || '-' || certificate."CERTIFICATE_NAME") as CERTIFICATE_TYPE,
            certificate."CERTIFICATE_TYPE_CODE",
            certificate."ISSU_ORG_NAME_CN",
            certificate."CERTIFICATE_NUM",
            certificate."STATUS_FLAG",
            certificate."ISSUE_DATE",
            certificate."EFFECT_DATE",
            certificate."EXPIRE_DATE",
            certificate."HOLDER_NAME",
            certificate."HOLDER_IDENTITY_NUMBER",
            certificate."APPLY_TYPE",
            certificate."ACCEPT_ORG_CODE3",
            (SELECT "NAME" FROM "SYS_DEPT_IAMB" SDI WHERE SDI."CODE" = certificate.ACCEPT_ORG_CODE3) AS ACCEPT_ORG_NAME,
            certificate."ACCEPT_DATE",
            certificate."APPR_DATE",
            certificate."APPR_ORG_CODE3",
            (SELECT "NAME" FROM "SYS_DEPT_IAMB" SDI WHERE SDI."CODE" = certificate.APPR_ORG_CODE3) AS APPR_ORG_NAME,
            certificate."CREATE_STATUS",
            certificate."SHIP_IMO",
            certificate."SHIP_NAME",
            certificate."CERT_PRINT_NO"
            FROM "CTF_CERTIFICATE" certificate
            INNER JOIN CTF_CERTIFICATE_TYPE cct on cct.CERTIFICATE_TYPE_CODE = certificate.CERTIFICATE_TYPE_CODE
            INNER JOIN type_hierarchy th ON cct.CERT_TYPE_DIR_CODE = th.CERTIFICATE_TYPE_CODE
        <choose>
            <when test="intranetQueryQueryDto.clientId != null and intranetQueryQueryDto.clientId != ''">
            INNER JOIN cert_app_auth auth ON (
                auth."HOLDER_NAME" = certificate."HOLDER_NAME"
                AND auth."HOLDER_IDENTITY_NUMBER" = certificate."HOLDER_IDENTITY_NUMBER"
                AND auth."CLIENT_ID" = #{intranetQueryQueryDto.clientId}
                )
            </when>
        </choose>
        <where>
            certificate."DEL_FLAG" = '0' and certificate."CREATE_STATUS" = '1' and certificate."STATUS_FLAG" in ('1','-2')
            <if test="intranetQueryQueryDto.affairName != null and intranetQueryQueryDto.affairName != ''">
                and certificate.AFFAIR_NAME in
                <foreach item="affair" index="index" collection="intranetQueryQueryDto.affairName.split(',')" open="(" separator="," close=")">
                    #{affair}
                </foreach>
            </if>
            <if test="intranetQueryQueryDto.applyNo != null and intranetQueryQueryDto.applyNo != ''">
                and certificate.APPLY_NO like CONCAT(CONCAT('%',#{intranetQueryQueryDto.applyNo}),'%')
            </if>
            <if test="intranetQueryQueryDto.applicantName != null and intranetQueryQueryDto.applicantName != ''">
                and certificate.APPLICANT_NAME like CONCAT(CONCAT('%',#{intranetQueryQueryDto.applicantName}),'%')
            </if>
            <if test="intranetQueryQueryDto.applyDateStart != null and intranetQueryQueryDto.applyDateStart != '' and intranetQueryQueryDto.applyDateEnd != null and intranetQueryQueryDto.applyDateEnd != ''">
                and certificate.APPLY_DATE between #{intranetQueryQueryDto.applyDateStart} and
                #{intranetQueryQueryDto.applyDateEnd}
            </if>
            <if test="intranetQueryQueryDto.acceptDateStart != null and intranetQueryQueryDto.acceptDateStart != '' and intranetQueryQueryDto.acceptDateEnd != null and intranetQueryQueryDto.acceptDateEnd != ''">
                and certificate.ACCEPT_DATE between #{intranetQueryQueryDto.acceptDateStart} and
                #{intranetQueryQueryDto.acceptDateEnd}
            </if>
            <if test="intranetQueryQueryDto.apprDateStart != null and intranetQueryQueryDto.apprDateStart != '' and intranetQueryQueryDto.apprDateEnd != null and intranetQueryQueryDto.apprDateEnd != ''">
                and certificate.APPR_DATE between #{intranetQueryQueryDto.apprDateStart} and
                #{intranetQueryQueryDto.apprDateEnd}
            </if>
            <if test="intranetQueryQueryDto.shipName != null and intranetQueryQueryDto.shipName != ''">
                and (certificate.SHIP_NAME like CONCAT(CONCAT('%',#{intranetQueryQueryDto.shipName}),'%') OR
                SHIP_NAME_EN like CONCAT(CONCAT('%',#{intranetQueryQueryDto.shipName}),'%'))
            </if>
            <if test="intranetQueryQueryDto.holderName != null and intranetQueryQueryDto.holderName != ''">
                and certificate.HOLDER_NAME like CONCAT(CONCAT('%',#{intranetQueryQueryDto.holderName}),'%')
            </if>
            <if test="intranetQueryQueryDto.holderIdentityNumber != null and intranetQueryQueryDto.holderIdentityNumber != ''">
                and certificate.HOLDER_IDENTITY_NUMBER like CONCAT(CONCAT('%',#{intranetQueryQueryDto.holderIdentityNumber}),'%')
            </if>
            <if test="intranetQueryQueryDto.issuOrgCode3 != null and intranetQueryQueryDto.issuOrgCode3.size() > 0 ">
                and certificate.ISSU_ORG_CODE3 in
                    <foreach item="orgCode" index="index" collection="intranetQueryQueryDto.issuOrgCode3" open="(" separator="," close=")">
                        #{orgCode}
                    </foreach>
            </if>

            <if test="intranetQueryQueryDto.onlineDepOrg != null and intranetQueryQueryDto.onlineDepOrg != '' ">
                and certificate.ISSU_ORG_CODE3 like CONCAT(#{intranetQueryQueryDto.onlineDepOrg},'%')
            </if>

            <if test="intranetQueryQueryDto.certificateTypeCode != null and intranetQueryQueryDto.certificateTypeCode != ''">
                and certificate.CERTIFICATE_TYPE_CODE in
                <foreach item="typeCode" index="index" collection="intranetQueryQueryDto.certificateTypeCode.split(',')" open="(" separator="," close=")">
                    #{typeCode}
                </foreach>
            </if>
            <if test="intranetQueryQueryDto.certificateType != null and intranetQueryQueryDto.certificateType != ''">
                and certificate.CERTIFICATE_TYPE_CODE in (
                    select CERTIFICATE_TYPE_CODE from CTF_CERTIFICATE_TYPE where CERT_TYPE_DIR_CODE in
                    (select CERTIFICATE_TYPE_CODE from CERT_TYPE_DIRECTORY where CERT_TYPE_DIR_ID in
                    <foreach item="type" index="index" collection="intranetQueryQueryDto.certificateType.split(',')" open="(" separator="," close=")">
                        #{type}
                    </foreach>
                    )
                )
            </if>
            <if test="intranetQueryQueryDto.certificateName != null and intranetQueryQueryDto.certificateName != ''">
                and certificate.CERTIFICATE_NAME = #{intranetQueryQueryDto.certificateName}
            </if>
            <if test="intranetQueryQueryDto.certificateNum != null and intranetQueryQueryDto.certificateNum != ''">
                and (certificate.CERTIFICATE_NUM like '%' || #{intranetQueryQueryDto.certificateNum} || '%' or certificate.CERTIFICATE_NUM like replace('%' || #{intranetQueryQueryDto.certificateNum} || '%',' ',''))
             </if>
            <if test="intranetQueryQueryDto.issueDateStart != null and intranetQueryQueryDto.issueDateStart != '' and intranetQueryQueryDto.issueDateEnd != null and intranetQueryQueryDto.issueDateEnd != ''">
                and certificate.ISSUE_DATE between #{intranetQueryQueryDto.issueDateStart} and
                #{intranetQueryQueryDto.issueDateEnd}
            </if>
            <if test="intranetQueryQueryDto.effectDateStart != null and intranetQueryQueryDto.effectDateStart != '' and intranetQueryQueryDto.effectDateEnd != null and intranetQueryQueryDto.effectDateEnd != ''">
                and certificate.EFFECT_DATE between #{intranetQueryQueryDto.effectDateStart} and
                #{intranetQueryQueryDto.effectDateEnd}
            </if>
            <if test="intranetQueryQueryDto.expireDateStart != null and intranetQueryQueryDto.expireDateStart != '' and intranetQueryQueryDto.expireDateEnd != null and intranetQueryQueryDto.expireDateEnd != ''">
                and certificate.EXPIRE_DATE between #{intranetQueryQueryDto.expireDateStart} and
                #{intranetQueryQueryDto.expireDateEnd}
            </if>
            <if test="intranetQueryQueryDto.statusFlag == 1 ">
                and (EXPIRE_DATE <![CDATA[>=]]> current_date)
            </if>
            <if test="intranetQueryQueryDto.statusFlag == 2 ">
                and (EXPIRE_DATE <![CDATA[<]]> current_date)
            </if>
            <if test="intranetQueryQueryDto.applyType != null and intranetQueryQueryDto.applyType != ''">
                and certificate.APPLY_TYPE = #{intranetQueryQueryDto.applyType}
            </if>
            <if test="intranetQueryQueryDto.acceptOrgCode3 != null and intranetQueryQueryDto.acceptOrgCode3.size() > 0 ">
                and
                <foreach item="orgCode" index="index" collection="intranetQueryQueryDto.acceptOrgCode3" open="("
                         separator="OR" close=")">
                    certificate.ACCEPT_ORG_CODE3 like CONCAT(#{orgCode},'%')
                </foreach>
            </if>
            <if test="intranetQueryQueryDto.apprOrgCode3 != null and intranetQueryQueryDto.apprOrgCode3.size() > 0 ">
                and
                <foreach item="orgCode" index="index" collection="intranetQueryQueryDto.apprOrgCode3" open="("
                         separator="OR" close=")">
                    certificate.APPR_ORG_CODE3 like CONCAT(#{orgCode},'%')
                </foreach>
            </if>
            <if test="intranetQueryQueryDto.shipImo != null and intranetQueryQueryDto.shipImo != '' ">
                and LOWER(certificate.SHIP_IMO) like CONCAT(CONCAT('%',LOWER(#{intranetQueryQueryDto.shipImo})),'%')
            </if>
            <if test="intranetQueryQueryDto.shipCallSign != null and intranetQueryQueryDto.shipCallSign != '' ">
                and LOWER(certificate.SHIP_CALL_SIGN) like CONCAT(CONCAT('%',LOWER(#{intranetQueryQueryDto.shipCallSign})),'%')
            </if>
            <if test="intranetQueryQueryDto.certPrintNo != null and intranetQueryQueryDto.certPrintNo != '' ">
                and certificate.CERT_PRINT_NO like CONCAT(CONCAT('%',#{intranetQueryQueryDto.certPrintNo}),'%')
            </if>
        </where>
        order by certificate."APPLY_NO" desc, certificate."CREATE_DATE"  desc;
    </select>
</mapper>
