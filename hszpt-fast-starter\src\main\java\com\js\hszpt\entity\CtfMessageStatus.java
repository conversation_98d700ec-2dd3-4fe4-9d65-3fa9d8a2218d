package com.js.hszpt.entity;

import com.js.core.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.Builder;
import java.util.Date;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import com.js.util.SnowFlakeUtil;


/**
 * 
 * @ClassName:  MessageStatus   
 * @Description:TODO(消息状态表)   
 * @author:   System Generation 
 */
@Data

@TableName("CTF_MESSAGE_STATUS")
@ApiModel(value = "消息状态表")
public class CtfMessageStatus extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "MESSAGE_STATUS_ID", type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value = "主键ID")
    private String id = String.valueOf(SnowFlakeUtil.nextId());

    /**
     * 公告ID
     */
    @TableField("NOTICE_ID")
    @ApiModelProperty(value = "公告ID")
    private String noticeId;

    /**
     * 单位编码
     */
    @TableField("DEPARTMENT_CODE")
    @ApiModelProperty(value = "单位编码")
    private String departmentCode;

    /**
     * 用户ID
     */
    @TableField("USER_ID")
    @ApiModelProperty(value = "用户ID")
    private String userId;

    /**
     * 状态:0-未读;1-已读
     */
    @TableField("STATUS")
    @ApiModelProperty(value = "状态:0-未读;1-已读")
    private String status;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createOperId;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_DATE", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String modifyOperId;

    /**
     * 修改时间
     */
    @TableField(value = "MODIFY_DATE", fill = FieldFill.UPDATE)
    @ApiModelProperty(value = "修改时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyDate;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 扩展字段1
     */
    @ApiModelProperty(value = "扩展字段1")
    private String col1;

    /**
     * 扩展字段2
     */
    @ApiModelProperty(value = "扩展字段2")
    private String col2;

    /**
     * 扩展字段3
     */
    @ApiModelProperty(value = "扩展字段3")
    private String col3;

    /**
     * 扩展字段4
     */
    @ApiModelProperty(value = "扩展字段4")
    private String col4;

    /**
     * 扩展字段5
     */
    @ApiModelProperty(value = "扩展字段5")
    private String col5;

    /**
     * 消息内容
     */
    @ApiModelProperty(value = "消息内容")
    private String message;

    /**
     * 推送方向,1:前台;2:后台;3:4A推送
     */
    @ApiModelProperty(value = "推送方向,1:前台;2:后台;3:4A推送")
    private String sendType;

    /**
     * 处理状态,0:未处理;1:处理
     */
    @ApiModelProperty(value = "处理状态,0:未处理;1:处理")
    private String dealStatus;

    /**
     * 锁定状态,0:未锁;1:已锁定
     */
    @ApiModelProperty(value = "锁定状态,0:未锁;1:已锁定")
    private String lockStatus;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    private String mobileInt4;

    /**
     * 发送时间
     */
    @ApiModelProperty(value = "发送时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sendTime;

    /**
     * 消息类型1:通知2:站内消息3:短信
     */
    @ApiModelProperty(value = "消息类型1:通知2:站内消息3:短信")
    private String messageType;

    /**
     * 系统名称KEY（具体值在字典中配置，type=APP_VALUE）,值为 UUID 32位
     */
    @ApiModelProperty(value = "系统名称KEY（具体值在字典中配置，type=APP_VALUE）,值为 UUID 32位")
    private String appKey;

}