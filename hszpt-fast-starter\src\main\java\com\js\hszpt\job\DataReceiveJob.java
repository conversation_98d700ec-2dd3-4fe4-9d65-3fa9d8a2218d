package com.js.hszpt.job;

import cn.hutool.json.JSONUtil;
import com.js.hszpt.properties.DataReceiveJobProperties;
import com.js.hszpt.service.DataReceptionTaskService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 数据接收定时任务
 */
@Slf4j
@Component
public class DataReceiveJob implements Job {

    @Autowired
    private DataReceptionTaskService dataReceptionTaskService;

    @Override
    public void execute(JobExecutionContext context) {
        try {
            JobDataMap jobDataMap = context.getJobDetail().getJobDataMap();
            String jobDataMapString = jobDataMap.getString("dataReceiveJobProperties");
            DataReceiveJobProperties dataReceiveJobProperties = JSONUtil.toBean(jobDataMapString, DataReceiveJobProperties.class);
            if (dataReceiveJobProperties == null) {
                return;
            }
            // 判断是否开启
            if (!dataReceiveJobProperties.isEnable()) {
                return;
            }
            String taskName = dataReceiveJobProperties.getTaskName();
            log.info("【数据接收】定时任务开始执行:{}", taskName);
            dataReceptionTaskService.receptionTask(taskName);
            log.info("【数据接收】定时任务执行结束:{}", taskName);
        } catch (Exception e) {
            log.error("【数据接收】定时任务执行异常:{}", e.getMessage());
        }
    }
}
