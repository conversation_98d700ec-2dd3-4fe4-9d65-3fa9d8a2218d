package com.js.hszpt.builder;

import cn.hutool.core.util.ReflectUtil;
import com.js.hszpt.dto.DebrisCertDto;
import com.js.hszpt.entity.Certificate;
import com.js.hszpt.entity.CertificateAttribute;
import com.js.hszpt.vo.CertificateVo;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 电子证照照面信息转换生成
 */
public interface CertificateInfoBuilder {

    /**
     * 电子证照 属性名-》属性值
     * @return 电子证照信息map
     */
    Map<String, String> certInfo();

    /**
     * 电子证照 属性值-》属性名
     * @return 电子证照信息map
     */
    Map<String, String> certReverseInfo();

    /**
     * 电子证照照面信息转换生成
     * @param certificateVo 电子证照信息
     * @return 照面信息key -》 value
     */
    Map<String,String> buildCertificateAttribute(CertificateVo certificateVo);

    /**
     * 公共接口电子证照照面信息生成
     * @param clazz 请求实体
     * @return 电子证照照面信息集合
     */
    default List<CertificateAttribute> buildCertificateAttribute(Class<? extends Certificate> clazz){
        // 获取证照属性map
        Map<String, String> map = this.certReverseInfo();
        // 获取证照属性
        List<CertificateAttribute> certificateAttributeList = new ArrayList<>();
        map.forEach((k,v)->{
            CertificateAttribute certificateAttribute = new CertificateAttribute();
            Field field = ReflectUtil.getField(clazz, k);
            if (field == null) {
                return ;
            }
            certificateAttribute.setAttributeName(v);
            certificateAttribute.setAttributeColumnName(field.getName());
            certificateAttribute.setAttributeValue(k);
            certificateAttributeList.add(certificateAttribute);
        });
        return certificateAttributeList;
    }
}
