package com.js.hszpt.utils.sm4;


public class EncryptUtil {
	/**
	 * sm4加密
	 * @param text
	 * @return
	 */
	public static String sm4Encrypt(String text,String secretKey) {
		 if (text == null || text.length() == 0) {
		 return text;
		 }
		 return SM4Utils.encryptData_ECB(text,secretKey);
	}


	/**
	 * sm4解密
	 * @param text
	 * @return
	 */
	public static String sm4Decrypt(String text,String secretKey) {
		 if (text == null || text.length() == 0) {
		 return text;
		 }
		 return SM4Utils.decryptData_ECB(text,secretKey);
	}


	public static void main(String[] args) {
		System.out.println(sm4Decrypt("HzNL4enyCXnWrf0FytGQuA==","4370780c9a8c43e5"));//false
	}








}
