package com.js.hszpt.entity;

import com.js.core.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.js.util.SnowFlakeUtil;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.util.Date;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;


/**
 * 
 * @ClassName:  CertTypeApprovalApply
 * @Description:TODO(证照类型目录审批申请表)   
 * @author:   System Generation 
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("CERT_TYPE_APPROVAL_APPLY")
@ApiModel(value = "证照类型目录审批申请表")
public class CertTypeApprovalApply extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "CERT_TYPE_APPR_APPLY_ID", type = IdType.ASSIGN_UUID)
    @ApiModelProperty("主键ID")
    private String id = String.valueOf(SnowFlakeUtil.nextId());

    /**
     * 申请编号，按规则生成
     */
    @TableField("APPLY_NO")
    @ApiModelProperty(value = "申请编号，按规则生成")
    private String applyNo;

    /**
     * 证照类型目录ID
     */
    @TableField("CERT_TYPE_DIR_ID")
    @ApiModelProperty(value = "证照类型目录ID")
    private String certTypeDirId;

    /**
     * 工作流实例ID
     */
    @TableField("EXECUTION_ID")
    @ApiModelProperty(value = "工作流实例ID")
    private String executionId;

    /**
     * 申请类型：1-新增 2-废止 3-启用
     */
    @TableField("APPLY_TYPE")
    @ApiModelProperty(value = "申请类型：1-新增 2-废止 3-启用")
    private String applyType;

    /**
     * 申请机构代码
     */
    @TableField("APPLY_ORG_CODE")
    @ApiModelProperty(value = "申请机构代码")
    private String applyOrgCode;

    /**
     * 申请机构名称
     */
    @TableField("APPLY_ORG_NAME")
    @ApiModelProperty(value = "申请机构名称")
    private String applyOrgName;

    /**
     * 受理机构代码
     */
    @TableField("ACCEPT_ORG_CODE")
    @ApiModelProperty(value = "受理机构代码")
    private String acceptOrgCode;

    /**
     * 受理机构名称
     */
    @TableField("ACCEPT_ORG_NAME")
    @ApiModelProperty(value = "受理机构名称")
    private String acceptOrgName;

    /**
     * 审批机构代码
     */
    @TableField("APPROVAL_ORG_CODE")
    @ApiModelProperty(value = "审批机构代码")
    private String approvalOrgCode;

    /**
     * 审批机构名称
     */
    @TableField("APPROVAL_ORG_NAME")
    @ApiModelProperty(value = "审批机构名称")
    private String approvalOrgName;

    /**
     * 发布机构代码
     */
    @TableField("PUBLISH_ORG_CODE")
    @ApiModelProperty(value = "发布机构代码")
    private String publishOrgCode;

    /**
     * 发布机构名称
     */
    @TableField("PUBLISH_ORG_NAME")
    @ApiModelProperty(value = "发布机构名称")
    private String publishOrgName;

    /**
     * 申请人ID
     */
    @TableField("APPLICANT_ID")
    @ApiModelProperty(value = "申请人ID")
    private String applicantId;

    /**
     * 申请人姓名
     */
    @TableField("APPLICANT_NAME")
    @ApiModelProperty(value = "申请人姓名")
    private String applicantName;

    /**
     * 申请原因说明
     */
    @TableField("APPLY_REASON")
    @ApiModelProperty(value = "申请原因说明")
    private String applyReason;

    /**
     * 申请时间
     */
    @TableField(value = "APPLY_TIME", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "申请时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date applyTime;

    /**
     * 受理时间
     */
    @TableField(value = "ACCEPT_TIME", fill = FieldFill.UPDATE)
    @ApiModelProperty(value = "受理时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date acceptTime;

    /**
     * 审批时间
     */
    @TableField(value = "APPROVAL_TIME", fill = FieldFill.UPDATE)
    @ApiModelProperty(value = "审批时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date approvalTime;

    /**
     * 发布时间
     */
    @TableField(value = "PUBLISH_TIME", fill = FieldFill.UPDATE)
    @ApiModelProperty(value = "发布时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date publishTime;

    /**
     * 当前节点：1-申请 2-受理 3-审核 4-审批
     */
    @TableField("CURRENT_NODE")
    @ApiModelProperty(value = "当前节点：1-申请 2-受理 3-审核 4-审批")
    private String currentNode;

    /**
     * 当前处理人ID
     */
    @TableField("CURRENT_HANDLER_ID")
    @ApiModelProperty(value = "当前处理人ID")
    private String currentHandlerId;

    /**
     * 当前处理人姓名
     */
    @TableField("CURRENT_HANDLER_NAME")
    @ApiModelProperty(value = "当前处理人姓名")
    private String currentHandlerName;

    /**
     * 下一节点处理人ID
     */
    @TableField("NEXT_HANDLER_ID")
    @ApiModelProperty(value = "下一节点处理人ID")
    private String nextHandlerId;

    /**
     * 下一节点处理人姓名
     */
    @TableField("NEXT_HANDLER_NAME")
    @ApiModelProperty(value = "下一节点处理人姓名")
    private String nextHandlerName;

    /**
     * 状态：1-待审核 2-审核通过 3-审核不通过 4-审核退回 5-撤回 6-待审批 7-审批通过 8-审批不通过 9-审批退回
     */
    @TableField("STATUS")
    @ApiModelProperty(value = "状态：1-待审核 2-审核通过 3-审核不通过 4-审核退回 5-撤回 6-待审批 7-审批通过 8-审批不通过 9-审批退回")
    private String status;

    /**
     * 删除标志：0-正常，1-已删除
     */
    @TableField("DEL_FLAG")
    @ApiModelProperty(value = "删除标志：0-正常，1-已删除")
    private String delFlag;

    @TableField("ISSUE_STATUS")
    @ApiModelProperty(value = "下发状态，1-未下发 2-已下发 3-已废止")
    private String issueStatus;
}