package com.js.hszpt.api;


import com.alibaba.fastjson.JSONArray;
import com.js.hszpt.entity.LawWorkflowNodeSet;
import com.js.hszpt.service.LawWorkflowNodeSetService;
 
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.js.core.api.BaseApiPlus;
import com.js.core.common.utils.ResultUtil;
import com.js.core.common.vo.PageVo;
import com.js.core.common.vo.Result;
import com.js.core.common.vo.SearchVo;
import com.js.annotation.SystemLog;
import com.js.enums.LogType;
import com.js.hszpt.vo.LawWorkflowNodeSetVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;


 /**
 * 
 * @ClassName: LawWorkflowNodeSetApi  
 * @Description:TODO(工作流模板配置表接口)   
 * @author:  System Generation  
 *
 */
@Slf4j
@RestController
@Api(description = "工作流模板配置表接口")
@RequestMapping("/lawWorkflowNodeSet")
public class LawWorkflowNodeSetApi extends BaseApiPlus<LawWorkflowNodeSetService,LawWorkflowNodeSet,String>{
	 private LawWorkflowNodeSetService lawWorkflowNodeSetService;
	@SystemLog(description = "工作流模板配置表-分页查询", type = LogType.OPERATION)
	@RequestMapping(value = "/getPage", method = RequestMethod.GET)
	@ApiOperation(value = "分页查询")
	public Result<Page<LawWorkflowNodeSet>> getPage(@ModelAttribute LawWorkflowNodeSet param, @ModelAttribute SearchVo searchVo, @ModelAttribute PageVo pageVo) {
		Page<LawWorkflowNodeSet> page = this.baseService.findByCondition(param, searchVo, pageVo);
		return ResultUtil.data(page);
	}
	
	@SystemLog(description = "工作流模板配置表-列表查询", type = LogType.OPERATION)
	@RequestMapping(value = "/getList", method = RequestMethod.GET)
	@ApiOperation(value = "列表查询")
	public Result<List<LawWorkflowNodeSet>> getList(@ModelAttribute LawWorkflowNodeSet param, @ModelAttribute SearchVo searchVo) {
		List<LawWorkflowNodeSet> list = this.baseService.findByCondition(param, searchVo);
		return ResultUtil.data(list);
	}

	 @ApiOperation("获取流程模板")
	 @GetMapping("/selectJgWorkflowNodeSet")
	 public Result<List<LawWorkflowNodeSetVo>> selectJgWorkflowNodeSet() {
		 try {
			 List<LawWorkflowNodeSetVo> lawWorkflowNodeSetVos = this.baseService.selectJgWorkflowNodeSet();
			 String result = JSONArray.toJSONString(lawWorkflowNodeSetVos);
			 log.info("查询工作流模板节点: {}", result);
			 return ResultUtil.data(lawWorkflowNodeSetVos);
		 } catch (Exception e) {
			 log.error("获取流程模板失败", e);
			 return ResultUtil.error("获取流程模板失败：" + e.getMessage());
		 }
	 }
}
