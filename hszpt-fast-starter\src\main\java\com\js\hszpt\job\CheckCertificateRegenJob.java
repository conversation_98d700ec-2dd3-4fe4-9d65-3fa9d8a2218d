package com.js.hszpt.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.js.hszpt.entity.CertificateDataRegen;
import com.js.hszpt.service.CertificateDataRegenService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeoutException;
import javax.annotation.PreDestroy;

@Slf4j
@Component
public class CheckCertificateRegenJob {

    @Autowired
    private CertificateDataRegenService certificateDataRegenService;

    @Autowired
    private ObjectMapper objectMapper;

    @Value("${certificate.downloadPhotoKey}")
    private String downloadPhotoKey;

    @Value("${certificate.downloadPhotoUrl}")
    private String downloadPhotoUrl;

    @Value("${job.checkCertificate.enabled:false}")
    private boolean enable;

    // 照片处理结果失败
    private final static String PHOTO_MESSAGE_ABNORMAL_STATUS = "11";
    // 照片处理结果正常
    private final static String PHOTO_MESSAGE_NORMAL_STATUS = "3";
    // 照片处理结果优先级正常
    private final static String PHOTO_MESSAGE_NORMAL_PRIORITY_STATUS = "4";

    // 添加线程池配置
    private final ExecutorService executorService = new ThreadPoolExecutor(
            10, // 核心线程数
            10, // 最大线程数
            60L, // 空闲线程存活时间
            TimeUnit.SECONDS, // 时间单位
            new LinkedBlockingQueue<>(100), // 工作队列
            r -> {
                Thread thread = new Thread(r);
                thread.setName("certificate-check-pool-" + thread.getId());
                return thread;
            }, // 线程工厂
            new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略
    );

    @Scheduled(cron = "${job.checkCertificate.cron}")
    public void execute() {
        if (!enable) {
            return;
        }

        long start = System.currentTimeMillis();
        log.info("【检查照片】开始检查:{}", start);

        // 获取未处理的证照列表
        List<CertificateDataRegen> certificateDataRegenList = certificateDataRegenService.getUnCheckCertificate();
        if (CollUtil.isEmpty(certificateDataRegenList)) {
            log.warn("【检查照片】未获取到未处理的证照");
            return ;
        }
        // 使用原子计数器记录处理结果
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failCount = new AtomicInteger(0);
        for (CertificateDataRegen certificateDataRegen : certificateDataRegenList) {
            // 使用线程池并行处理证照照片
            final String certificateId = certificateDataRegen.getCertificateId();
            executorService.submit(() -> {
                try {
                    log.debug("【检查照片】开始处理证照:{}", certificateId);
                    String status = checkErrorPhotoCertificate(certificateDataRegen);
                    certificateDataRegen.setCreateStatus(status);
                    certificateDataRegen.setRegenTime(DateUtil.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
                    successCount.incrementAndGet();
                    log.debug("【检查照片】处理完成证照:{}, 状态:{}", certificateId, status);
                } catch (Exception e) {
                    certificateDataRegen.setCreateStatus(PHOTO_MESSAGE_ABNORMAL_STATUS);
                    certificateDataRegen.setRegenTime(DateUtil.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
                    failCount.incrementAndGet();
                    log.error("【检查照片】处理证照异常:{}", certificateId, e);
                }finally {
                    certificateDataRegenService.updateById(certificateDataRegen);
                }
            });
        }
        log.info("【证照重新生成定时任务】执行完成，成功：{}，失败：{}",
                successCount.get(), failCount.get());
        log.info("【检查照片】执行完成,耗时：{}ms", System.currentTimeMillis() - start);
    }

    private String checkErrorPhotoCertificate(CertificateDataRegen certificateDataRegen) {
        try {
            // 照面信息获取照片字段
            List<CertificateDataRegen.Surface> surfaceList = objectMapper.readValue(
                    certificateDataRegen.getSurfaceData(),
                    new TypeReference<List<CertificateDataRegen.Surface>>() {
                    });
            if (CollUtil.isEmpty(surfaceList)) {
                log.error("【检查照片】未获取到证照照面信息:{}", certificateDataRegen.getCertificateId());
                return PHOTO_MESSAGE_ABNORMAL_STATUS;
            }

            // 使用同步方式处理照片检查，因为已经在线程池中执行
            List<CertificateDataRegen.Surface> checkList = surfaceList.stream()
                    .filter(this::checkPhoto)
                    .collect(Collectors.toList());

            // 照片信息异常状态改为11
            if (CollUtil.isEmpty(checkList)) {
                log.error("【检查照片】未获取到照片信息或获取照片信息失败:{}", certificateDataRegen.getCertificateId());
                return PHOTO_MESSAGE_ABNORMAL_STATUS;
            }
            // 照片信息正常状态改为 3 或 4
            boolean isPriority = StrUtil.equals("2", certificateDataRegen.getDataType())
                    || StrUtil.equals("6", certificateDataRegen.getCreateStatus());
            return isPriority ? PHOTO_MESSAGE_NORMAL_PRIORITY_STATUS : PHOTO_MESSAGE_NORMAL_STATUS;
        } catch (JsonProcessingException e) {
            log.error("【检查照片】JSON转换异常:{}", certificateDataRegen.getCertificateId(), e);
            e.printStackTrace();
            return PHOTO_MESSAGE_ABNORMAL_STATUS;
        }
    }

    /**
     * 检查照片信息
     * 
     * @param surface 照面信息
     * @return true: 照片信息正常 false: 照片信息异常
     */
    private boolean checkPhoto(CertificateDataRegen.Surface surface) {
        if (surface == null) {
            log.error("【检查照片】照片信息为空");
            return false;
        }
        // 不是照片信息
        String oldFieldName = surface.getName();
        if (!downloadPhotoKey.contains(oldFieldName)
                || StrUtil.isBlank(surface.getValue())) {
            return false;
        }

        String photoValue = surface.getValue();
        if (photoValue.startsWith("data:") || isBase64(photoValue)) {
            log.info("【检查照片】符合base64编码");
            return true;
        }
        // 构建完整的照片下载URL
        String photoUrl = downloadPhotoUrl;
        if (!photoUrl.endsWith("/") && !surface.getValue().startsWith("/")) {
            photoUrl += "/";
        }
        photoUrl += surface.getValue();
        log.info("【检查照片】开始下载照片，URL：{}", photoUrl);
        // 下载照片并转换为base64
        String base64Photo = downloadImageAsBase64(photoUrl);
        // 照片是否能正常下载
        if (StrUtil.isBlank(base64Photo)) {
            log.error("【检查照片】照片下载失败，请检查URL是否正确");
            return false;
        }
        return true;
    }

    private String downloadImageAsBase64(String imageUrl) {
        HttpURLConnection connection = null;
        InputStream inputStream = null;
        ByteArrayOutputStream outputStream = null;

        try {
            // 建立连接
            URL url = new URL(imageUrl);
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(10000);
            connection.setReadTimeout(10000);

            // 获取响应码
            int responseCode = connection.getResponseCode();
            if (responseCode != HttpURLConnection.HTTP_OK) {
                log.error("【检查照片】下载照片失败，响应码：{}", responseCode);
                return null;
            }

            // 读取图片数据
            inputStream = connection.getInputStream();
            outputStream = new ByteArrayOutputStream();

            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }

            // 转换为Base64
            byte[] imageBytes = outputStream.toByteArray();
            return Base64.getEncoder().encodeToString(imageBytes);

        } catch (Exception e) {
            log.error("【检查照片】下载图片并转换为Base64异常", e);
            return null;
        } finally {
            // 关闭资源
            try {
                if (outputStream != null) {
                    outputStream.close();
                }
                if (inputStream != null) {
                    inputStream.close();
                }
                if (connection != null) {
                    connection.disconnect();
                }
            } catch (Exception e) {
                log.error("【检查照片】关闭资源异常", e);
            }
        }
    }

    private boolean isBase64(String str) {
        if (StrUtil.isBlank(str) || str.length() % 4 != 0) {
            return false;
        }
        try {
            Base64.getDecoder().decode(str); // ✅ 实际尝试解码
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }

    /**
     * 应用关闭时关闭线程池
     */
    @PreDestroy
    public void shutdown() {
        log.info("【检查照片】开始关闭线程池");
        executorService.shutdown();
        try {
            // 等待线程池中的任务执行完毕
            if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                log.warn("【检查照片】线程池未在指定时间内完全关闭，尝试强制关闭");
                executorService.shutdownNow();
                if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                    log.error("【检查照片】线程池强制关闭失败");
                }
            }
            log.info("【检查照片】线程池已关闭");
        } catch (InterruptedException e) {
            log.error("【检查照片】关闭线程池被中断", e);
            executorService.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
}
