package com.js.hszpt.api;


import com.js.api.entity.OauthClientDetails;
import com.js.auth.config.security.SecurityUser;
import com.js.auth.config.security.SecurityUserDetails;
import com.js.auth.entity.TokenDTO;
import com.js.auth.service.UacAuthService;
import com.js.core.common.vo.Result;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 * @ClassName: VhcAuthApi
 * @Description:TODO(鉴权接口)
 * @author:  chenjs
 *
 */
@Slf4j
@RestController
@RequestMapping("/cert")
public class CertAuthApi {

    @Autowired
    private UacAuthService uacAuthService;

    @RequestMapping(value = "/auth/clientLogin", method = RequestMethod.POST)
    @ApiOperation(value = "第三方应用登录接口")
    public Result oauthToken(@RequestParam String clientId, @RequestParam String clientSecret) {
        //构建返回对象
        if (StringUtils.isEmpty(clientId) || StringUtils.isEmpty(clientSecret)) {
            return Result.failed(500, "请填写应用clientId或clientSecret");
        }
        String[] clientInfos = new String[]{clientId , clientSecret};
        OauthClientDetails oauthClientDetails = uacAuthService.verifyClient(clientInfos);
        if(null == oauthClientDetails) {
            return Result.failed(500, "请填写应用clientId或clientSecret");
        }

        SecurityUser securityUser = new SecurityUser();
        securityUser.setUserName(oauthClientDetails.getClientId());
        securityUser.setClientId(oauthClientDetails.getClientId());
        securityUser.setClientType(oauthClientDetails.getClientType());
        securityUser.setSsoParam(securityUser.getSsoParam());
        securityUser.setClientPermissions(securityUser.getClientType());
        securityUser.setSso(true);
        securityUser.setStatus(0);

        SecurityUserDetails userDetail = new SecurityUserDetails(securityUser);
        TokenDTO tokenDTO = uacAuthService.creareToken(userDetail);
        if(null == tokenDTO) {
            return Result.failed(500, "第三方创建token异常");
        }
        return Result.success(tokenDTO);
    }
}
