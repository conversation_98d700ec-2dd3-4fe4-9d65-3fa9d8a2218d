package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("APPLY_SHIP_INFO")
public class ApplyShipInfo implements Serializable {

    @TableId(value = "APPLY_SHIP_INFO_ID")
    private String applyShipInfoId;

    @TableField("WINDOW_APPLY_ID")
    private String windowApplyId;

    @TableField("SHIP_ID")
    private String shipId;

    @TableField("SHIP_IMO")
    private String shipImo;

    @TableField("MMSI")
    private String mmsi;

    @TableField("V_VESSEL_NAME")
    private String vesselName;

    @TableField("V_SHIP_INS_REG_NO")
    private String shipInsRegNo;

    @TableField("SHIP_FIRSTREG_NO")
    private String shipFirstRegNo;

    @TableField("SHIP_CARD_NO")
    private String shipCardNo;

    @TableField("V_REGISTRY_PORT")
    private String registryPort;

    @TableField("V_SHIP_TYPE_NAME")
    private String shipTypeName;

    @TableField("V_NAVIGATION_AREA_NAME")
    private String navigationAreaName;

    @TableField("D_SHIP_COMPLETION_DATE")
    private String shipCompletionDate;

    @TableField("D_CONVERSION_DATE")
    private String conversionDate;

    @TableField("V_SHIP_OWNER")
    private String shipOwner;

    @TableField("V_SHIP_OPERATOR")
    private String shipOperator;

    @TableField("I_GROSS_TONNAGE")
    private String grossTonnage;

    @TableField("I_NET_TONNAGE")
    private String netTonnage;

    @TableField("N_MAINENGINE_POWER")
    private String mainEnginePower;

    @TableField("N_VESSEL_LENGTH")
    private String vesselLength;

    @TableField("N_MOLDED_BREADTH")
    private String moldedBreadth;

    @TableField("N_MOLDED_DEPTH")
    private String moldedDepth;

    @TableField("SHIP_INSPECT_NO")
    private String shipInspectNo;

    @TableField("ORG_CODE")
    private String orgCode;

    @TableField("NATION_NAME_CN")
    private String nationNameCn;

    @TableField("APPLY_SHIP_STATUS")
    private Character applyShipStatus;

    @TableField("SHIP_NAME_EN")
    private String shipNameEn;

    @TableField("SHIP_NAME_CN")
    private String shipNameCn;

    @TableField("SHIP_CALL_SIGN")
    private String shipCallSign;

    @TableField("SHIP_ROUTE_CODE")
    private String shipRouteCode;

    @TableField("V_SHIP_OWNER_EN")
    private String shipOwnerEn;

    @TableField("V_SHIP_OWNER_ADDRESS_EN")
    private String shipOwnerAddressEn;

    @TableField("V_SHIP_OWNER_ADDRESS")
    private String shipOwnerAddress;

    @TableField("DWT")
    private String dwt;

    @TableField("EDIT_COLUMN")
    private String editColumn;

    @TableField("GENERAL_SORT_CODE")
    private String generalSortCode;

    @TableField("SHIP_BUILT_ADDR_CN")
    private String shipBuiltAddrCn;

    @TableField("SHIP_ROUTE_CODES")
    private String shipRouteCodes;

    @TableField("OFFSHORE_PLATFORM")
    private String offshorePlatform;

    @TableField("V_REGISTRY_PORT_EN")
    private String registryPortEn;

    @TableField("V_SHIP_TYPE_NAME_EN")
    private String shipTypeNameEn;

    @TableField("SHIP_MANAGER")
    private String shipManager;
}
