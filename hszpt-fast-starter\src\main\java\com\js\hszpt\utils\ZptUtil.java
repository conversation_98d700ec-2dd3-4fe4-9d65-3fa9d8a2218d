package com.js.hszpt.utils;

import cn.hutool.core.util.URLUtil;
import cn.hutool.http.HttpException;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import lombok.extern.slf4j.Slf4j;

import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URL;
import javax.imageio.ImageIO;
import java.util.Base64;
import java.util.Map;

import static cn.hutool.http.HttpStatus.HTTP_OK;

@Slf4j
public class ZptUtil {

    // 超时时间
    private static final int TIME_OUT = 10000;

    /**
     * 获取数据
     * @param url 接口地址
     * @param paramMap 请求参数
     * @return 响应报文
     */
    public static String getDataReceived(String url, Map<String, Object> paramMap) {
        HttpResponse response = null;
        try {
            // 拼接请求参数
            String query = URLUtil.buildQuery(paramMap, null);
            url = url + "?" + query;
            response = HttpUtil.createGet(url)
                    .timeout(TIME_OUT)
                    .execute();
            if (response.getStatus() != HTTP_OK) {
                log.error("【子平台】请求地址:{}，请求失败:{}",url, response.getStatus());
                return "";
            }
            String body = response.body();
            log.debug("【子平台】请求地址:{}，接收到数据:{}",url,body);
            return body;
        } catch (HttpException e) {
            log.error("【子平台】查询数据异常:{}", e.getMessage());
            return "";
        } finally {
            // 🔑 关键修复：确保HttpResponse被正确关闭
            if (response != null) {
                response.close();
            }
        }
    }

    /**
     * 通过图片地址，获取base64字符串
     * @param imageUrl
     * @return
     */
    public static String convertImageUrlToBase64(String imageUrl) {
        try {
            log.info("获取图片base64字符串，图片地址：{}", imageUrl);
            // 从URL下载图片
            URL url = new URL(imageUrl);
            BufferedImage image = ImageIO.read(url);

            // 将图片写入字节数组输出流
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(image, "jpg", baos); // 注意：这里指定了图片格式，可以是jpg, png等
            byte[] imageBytes = baos.toByteArray();

            // 使用Base64编码字节数组
            String base64String = Base64.getEncoder().encodeToString(imageBytes);

            // 关闭字节数组输出流
            baos.close();

            return base64String;
        } catch (IOException e) {
            e.printStackTrace();
            log.error("获取图片base64字符串异常:{}", e.getMessage());
            return null;
        }
    }

    /**
     * 部门机构编码特殊处理
     * 1. 如果5-6位不等于02，则orgCode等于1-2位
     * 2. 如果9-10位不等于02，则orgCode等于1-8位
     * 3. 如果5-6位等于99，则直接返回原始编码
     *
     * @param orgCode 原始机构编码
     * @return 处理后的机构编码
     */
    public static String processOrgCode(String orgCode) {
        if (orgCode == null || orgCode.isEmpty()) {
            return orgCode;
        }

        // 新增条件1：第5-6位等于99时直接返回
        if (orgCode.length() >= 6) {
            String pos5to6 = orgCode.substring(4, 6);
            if ("99".equals(pos5to6)) {
                return orgCode;
            }
        }

        // 检查长度是否足够
        if (orgCode.length() >= 6) {
            // 获取第5-6位
            String pos5to6 = orgCode.substring(4, 6);
            if (!"02".equals(pos5to6)) {
                // 如果5-6位不等于02，则返回1-2位
                return orgCode.substring(0, 2);
            }
        }

        // 检查长度是否足够
        if (orgCode.length() >= 10) {
            // 获取第9-10位
            String pos9to10 = orgCode.substring(8, 10);
            if (!"02".equals(pos9to10)) {
                // 如果9-10位不等于02，则返回1-8位
                return orgCode.substring(0, 8);
            }
        }

        // 如果不满足上述条件，返回原始orgCode
        return orgCode;
    }
}
