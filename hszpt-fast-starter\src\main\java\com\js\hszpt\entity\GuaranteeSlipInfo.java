package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.sql.Date;


@Data
@TableName("GUARANTEE_SLIP_INFO")
public class GuaranteeSlipInfo implements Serializable {

    @TableId
    private String guaranteeSlipInfoId;

    private String windowApplyId;
    private String guaranteeSlipCode;
    private String effectiveDate;
    private String effectiveHour;
    private String deadLine;
    private String deadLineHour;
    private String dateType;
    private String safetyMechanismName;
    private String address;
    private String createOperId;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date createDate;
    private String modifyOperId;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date modifyDate;
    private String certificateName;
    private String remark;
    private String safetyMechanismEnglishName;
    private String englishAddress;
    private String insuranceCoverage;
    private String contact;
    private String contactNumber;
}
