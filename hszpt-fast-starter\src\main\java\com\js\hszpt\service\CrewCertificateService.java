package com.js.hszpt.service;

import cn.hutool.core.bean.BeanUtil;import cn.hutool.core.date.DateTime;import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.core.common.vo.Result;
import com.js.hszpt.dto.CrewCertificateQueryDto;
import com.js.hszpt.dto.IntranetCrewQueryDto;
import com.js.hszpt.enmus.CertificateCreateStatus;
import com.js.hszpt.entity.Certificate;
import com.js.hszpt.entity.CrewCertificateData;
import com.js.hszpt.entity.VerificationDisplayConfig;import com.js.hszpt.mapper.crew.CrewCertificateMapper;
import com.js.hszpt.vo.CertificateIntranetCrewQueryVo;
import com.js.hszpt.vo.CrewCertificateQueryVo;
import com.js.hszpt.vo.TrainingProjectVo;import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;import java.util.*;

@Slf4j
@Service
@DS("dzzzdwdz")
public class CrewCertificateService extends ServiceImpl<CrewCertificateMapper, CrewCertificateData> {

//    /**
//     * 查询船员证书信息
//     *
//     * @param queryDto 查询条件
//     * @param page 分页参数
//     * @return 分页查询结果
//     */
//    public Result<IPage<CrewCertificateQueryVo>> queryCrewCertificates(CrewCertificateQueryDto queryDto, Page page) {
//        try {
//            log.info("海事一网通办查询船员证书信息，查询条件：{}", queryDto);
//            IPage<CrewCertificateQueryVo> result = baseMapper.selectCrewCertificates(page, queryDto);
//            log.info("查询船员证书信息完成，总记录数：{}", result.getTotal());
//            return Result.success(result);
//        } catch (Exception e) {
//            log.error("查询船员证书信息异常：", e);
//            return Result.failed("查询船员证书信息失败：" + e.getMessage());
//        }
//    }


     /**
          * 查询船员证书信息
          *
          * @param queryDto 查询条件
          * @param page 分页参数
          * @return 分页查询结果
          */
         public Result<IPage<CrewCertificateQueryVo>> queryCrewCertificates(CrewCertificateQueryDto queryDto, Page page) {
             try {
                 log.info("海事一网通办查询船员证书信息，查询条件：{}", queryDto);
                 //如果 queryDto.getCertStartDate(),queryDto.getCertEndDate()都不为空，则按照证照有效期开始时间和结束时间进行查询
                 //queryDto.getCertStartDate(),queryDto.getCertEndDate()的值规范是 yyyy-MM-dd,
                 // 比如"certStartDate": "2025-03-01", "certEndDate": "2025-04-01",
                 // 使用DateUtil.endOfDay()处理结束日期，将时间设置为23:59:59，确保包含当天的所有时间点
                 if (StrUtil.isNotBlank(queryDto.getCertStartDate()) && StrUtil.isNotBlank(queryDto.getCertEndDate())) {
                             Date certStart = DateUtil.parseDate(queryDto.getCertStartDate());
                             Date certEnd = DateUtil.endOfDay(DateUtil.parseDate(queryDto.getCertEndDate())); // 包含结束日期的最后一刻
                             queryDto.setCertStartDate(DateUtil.format(certStart, "yyyy-MM-dd HH:mm:ss"));
                             queryDto.setCertEndDate(DateUtil.format(certEnd, "yyyy-MM-dd HH:mm:ss"));
                 }
                 IPage<CrewCertificateQueryVo> result = baseMapper.selectCrewCertificates(page, queryDto);

                 // 遍历结果，处理内河船舶船员培训合格证的培训项目，同时转换为Map
                 for (CrewCertificateQueryVo record : result.getRecords()) {
                     // 处理内河船舶船员培训合格证的培训项目
                     if ("内河船舶船员培训合格证".equals(record.getCertificateName())) {
                         // 查询培训项目
                         TrainingProjectVo trainingProjectVo = baseMapper.queryTrainingProjects(record.getDataId());
                         BeanUtil.copyProperties(trainingProjectVo, record);
                     }

                     // 使用反射将对象转换为Map
                     Map<String, Object> recordMap = new HashMap<>();
                     Class<?> clazz = record.getClass();
                     Field[] fields = clazz.getDeclaredFields();
                     for (Field field : fields) {
                         field.setAccessible(true);
                         try {
                             // 排除dataMap字段，避免循环引用
                             if (!"dataMap".equals(field.getName())) {
                                 recordMap.put(field.getName(), field.get(record));
                             }
                         } catch (IllegalAccessException e) {
                             log.error("转换对象到Map异常", e);
                         }
                     }

                     // 根据证书类型和展示来源查询展示配置
                     if (queryDto.getDisplaySource() != null && record.getCertificateTypeCode() != null) {
                         // 查询展示配置
                         List<VerificationDisplayConfig> configs = baseMapper.queryVerificationDisplayConfigs(
                                 record.getCertificateType(), queryDto.getDisplaySource());

                         // 处理展示配置
                         if (configs != null && !configs.isEmpty()) {
                             List<Map<String, Object>> displayFields = new ArrayList<>();
                             configs.forEach(config -> {
                                 displayFields.add(getMap(recordMap, config, record.getCertificateId()));
                             });
                             // 将展示字段列表添加到记录中
                             record.setDisplayFields(displayFields);
                         }
                     }
                 }

                 log.info("查询船员证书信息完成，总记录数：{}", result.getTotal());

                 return Result.success(result);
             } catch (Exception e) {
                 log.error("查询船员证书信息异常：", e);
                 return Result.failed("查询船员证书信息失败：" + e.getMessage());
             }
         }


    /**
     * 内网船员证书查询
     * @param intranetCrewQueryDto 查询条件
     * @param page 分页参数
     * @return 查询结果
     */
    public IPage<CertificateIntranetCrewQueryVo> certificateIntranetCrewQueryAuth(IntranetCrewQueryDto intranetCrewQueryDto, Page page) {
        log.info("智慧海事内网船员证书查询，参数：{}", intranetCrewQueryDto);

        // 参数校验
        if (intranetCrewQueryDto == null) {
            throw new RuntimeException("查询参数不能为空");
        }

//        if (StrUtil.isBlank(intranetCrewQueryDto.getCertificateTypeCode())) {
//            throw new RuntimeException("证书类型代码不能为空");
//        }

        try {
            // 处理证书编号格式
            if (StrUtil.isNotBlank(intranetCrewQueryDto.getCertificateNum())) {
                String certificateNum = intranetCrewQueryDto.getCertificateNum().replaceAll(" ", "");
                char[] chars = certificateNum.toCharArray();
                StringBuilder stringBuilder = new StringBuilder();
                for (int i = 0; i < chars.length; i++) {
                    if (i > 1 && (chars[i - 1] + "").matches("^[a-zA-Z\\s,.!?;:'\"-]+$")
                        && (chars[i] + "").matches("^\\d+$")) {
                        stringBuilder.append(" ").append(chars[i]);
                    } else {
                        stringBuilder.append(chars[i]);
                    }
                }
                intranetCrewQueryDto.setCertificateNum(stringBuilder.toString());
            }

            // 执行查询
            IPage<CertificateIntranetCrewQueryVo> resultPage = baseMapper.certificateIntranetCrewQueryAuth(page, intranetCrewQueryDto);

            log.info("内网船员证书查询完成，共查询到 {} 条记录", resultPage.getTotal());
            return resultPage;

        } catch (Exception e) {
            log.error("内网船员证书查询异常", e);
            throw new RuntimeException("查询失败：" + e.getMessage());
        }
    }



    /**
         * 根据配置和对象映射生成一个Map，用于存储证书信息。
         *
         * @param objMap 证书对象映射
         * @param config 验证显示配置
         * @param certificateId 证书ID
         * @return 包含证书信息的Map
         */
        public Map<String, Object> getMap(Map<String, Object> objMap, VerificationDisplayConfig config, String certificateId) {
            Map<String, Object> hashMap = new HashMap<>();
            // 取中文
            if (StringUtils.isNotBlank(config.getChineseDataDefaultValue())) {// 有默认值
                hashMap.put("label", config.getChineseLabelName());
                hashMap.put("value", config.getChineseDataDefaultValue());
            } else {// 无默认值则从照面表找
                if ("statusFlag".equals(config.getChineseDataFieldName())) {// 有效值单独处理
                    String status;
                    Date issueDate = (Date) objMap.get("issueDate");
                    Date expireDate = (Date) objMap.get("expireDate");
                    Date date = new Date();
                    if (date.after(issueDate) && date.before(expireDate)) {
                        status = "有效";
                    } else {
                        status = "无效";
                    }
                    hashMap.put("label", config.getChineseLabelName());
                    hashMap.put("value", status);
                } else {
                    hashMap.put("label", config.getChineseLabelName());
                    hashMap.put("value", objMap.get(config.getChineseDataFieldName()));
                }
            }
            // 取英文
            if (StringUtils.isNotBlank(config.getEnglishDataDefaultValue())) {
                hashMap.put("labelEn", config.getEnglishLabelName());
                hashMap.put("valueEn", config.getEnglishDataDefaultValue());
            } else {
                Object obj = objMap.get(config.getEnglishDataFieldName());
                if (ObjectUtil.isNotEmpty(obj)) {// 如果找得到就取出来，没有就要手动转换了，例如有些字段是没有英文的，需要手动转。
                    hashMap.put("labelEn", config.getEnglishLabelName());
                    hashMap.put("valueEn", obj);
                } else {// 不需要转换,返回中文值
                    hashMap.put("labelEn", config.getEnglishLabelName());
                    hashMap.put("valueEn", null);// ?
                }
            }
            // 中文证书不展示英文字段了
            if ("statusFlag".equals(config.getChineseDataFieldName())
                    && StrUtil.isNotBlank(config.getEnglishLabelName())) {
                if ("有效".equals(hashMap.get("value"))) {
                    hashMap.put("valueEn", "Effective");
                } else {
                    hashMap.put("valueEn", "Invalid");
                }
            }
            hashMap.put("highlight", config.getHighlight());
            return hashMap;
        }

}
