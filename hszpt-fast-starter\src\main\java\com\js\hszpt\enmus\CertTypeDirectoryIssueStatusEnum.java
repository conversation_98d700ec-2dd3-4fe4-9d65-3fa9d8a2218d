package com.js.hszpt.enmus;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 证照类型目录下发状态枚举
 */
@Getter
@AllArgsConstructor
public enum CertTypeDirectoryIssueStatusEnum {
    
    NOT_ISSUED("1", "未下发"),
    ISSUED("2", "已下发"),
    ABOLISHED("3", "已废止");
    
    private final String code;
    private final String desc;
    
    /**
     * 根据code获取枚举
     */
    public static CertTypeDirectoryIssueStatusEnum getByCode(String code) {
        for (CertTypeDirectoryIssueStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
} 