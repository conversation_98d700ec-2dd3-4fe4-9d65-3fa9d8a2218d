package com.js.hszpt.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class CertificateVerifyVo {
    // 证书名称
    private String certificateName;
    // 发证机关名称
    private String issuOrgNameCn;
    // 证书印刷号
    private String certPrintNo;
    // 签发日期(yyyyMMddhhmiss)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") // 用于Spring MVC绑定
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")    // 用于JSON序列化
    private String issueDate;
    // 有效期日期-开始(yyyyMMddhhmiss)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") // 用于Spring MVC绑定
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")    // 用于JSON序列化
    private String effectDate;
    // 有效期日期-结束(yyyyMMddhhmiss)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") // 用于Spring MVC绑定
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")    // 用于JSON序列化
    private Date expireDate;
    // 持有人姓名
    private String holderName;
    // 船舶名称
    private String shipName;
    // 证照使用状态:1-已登记；3-注销
    private String statusFlag;

    //事项名称
    private String affairName;

    //申请人
    private String applicantName;

    //申请日期
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") // 用于Spring MVC绑定
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")    // 用于JSON序列化
    private Date applyDate;

    //申请来源
    private String applyType;

    //受理机构
    private String acceptOrgCode;

    //受理日期
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") // 用于Spring MVC绑定
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")    // 用于JSON序列化
    private Date acceptDate;

    //审批机构
    private String apprOrgCode;

    //审批日期
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") // 用于Spring MVC绑定
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")    // 用于JSON序列化
    private Date apprDate;


}

