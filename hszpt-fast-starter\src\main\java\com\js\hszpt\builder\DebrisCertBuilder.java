package com.js.hszpt.builder;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.js.hszpt.entity.BizDgrOpic;
import com.js.hszpt.entity.ShipInfoAll;
import com.js.hszpt.utils.*;
import com.js.hszpt.vo.CertificateVo;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

import static com.js.hszpt.constants.OilDamageInsuranceCertConstant.OIL_DAMAGE_INSURANCE_CERT_MAP;
import static com.js.hszpt.constants.OilDamageInsuranceCertConstant.OIL_DAMAGE_INSURANCE_REVERSET_MAP;

@Slf4j
public class DebrisCertBuilder implements CertificateInfoBuilder{


    @Override
    public Map<String, String> certInfo() {
        return OIL_DAMAGE_INSURANCE_CERT_MAP;
    }

    @Override
    public Map<String, String> certReverseInfo() {
        return OIL_DAMAGE_INSURANCE_REVERSET_MAP;
    }

    @Override
    public Map<String, String> buildCertificateAttribute(CertificateVo certificateVo) {
        boolean flag = true;
        BizDgrOpic bizDgrOpic = certificateVo.getBizDgrOpic();
        ShipInfoAll shipInfoAll = certificateVo.getShipInfoAll();
        // 1：国内航线；2：国际/港澳航线
        if (bizDgrOpic != null && StrUtil.isNotBlank(bizDgrOpic.getShipRouteCode())) {
            flag = "2".equals(bizDgrOpic.getShipRouteCode());
        }else if (shipInfoAll != null && StrUtil.isNotBlank(shipInfoAll.getShipRouteCode())){
            flag = "2".equals(shipInfoAll.getShipRouteCode());
        }else {
            log.warn("【电子证照信息】航线信息为空，请检查数据！");
            return null;
        }

        // 获取保险信息
        Map<String, String> insuranceDateInfoMap = AffairInsuranceUtil.getInsuranceDateInfoMap(certificateVo.getBizAffairInsurance(),
                certificateVo.getBizEgApprove(),flag);
        // 获取船舶信息
        Map<String, String> dgrOicInfoMap = bizDgrOpic !=null ?
                DgrOicUtil.getDgrOicInfoMap(bizDgrOpic, certificateVo.getDictEgPort(),certificateVo.getCertShipOwnership())
                : ShipInfoUtil.getShipInfoMap(shipInfoAll, certificateVo.getDictEgPort(),certificateVo.getCertShipOwnership());
        // 获取机构信息
        Map<String, String> egOrgMap = EgOrgUtil.getEgOrgMap(certificateVo.getSysDeptEn(), flag);
        // 获取签证官信息
        Map<String, String> officerInfoMap = OfficerInfoUtil.getOfficerInfoMap(certificateVo.getBizEgCertCheck(), certificateVo.getZwApplyOfficerInfo(),flag);
        // 生成证书对应的map值
        return MapUtil.builder(new HashMap<String,String>())
                .putAll(insuranceDateInfoMap)
                .putAll(dgrOicInfoMap)
                .putAll(egOrgMap)
                .putAll(officerInfoMap)
                .build();
    }
}
