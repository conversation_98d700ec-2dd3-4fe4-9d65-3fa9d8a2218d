package com.js.hszpt.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.core.common.utils.ResultUtil;
import com.js.core.common.vo.Result;
import com.js.hszpt.constants.OilDamageInsuranceCertConstant;
import com.js.hszpt.constants.OilPollutionWarrantyConstant;
import com.js.hszpt.dto.CertificateDto;
import com.js.hszpt.entity.Certificate;
import com.js.hszpt.entity.CertificateAttribute;
import com.js.hszpt.mapper.CertificateMapper;
import com.js.hszpt.service.CertificateAttributeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.*;

@Slf4j
@Service
public class GenerateCertificateService extends ServiceImpl<CertificateMapper, Certificate> {

    @Autowired
    private CertificateAttributeService certificateAttributeService;
    
    /**
     * 证书名称和证书类型编码映射
     */
    private static final Map<String, String> CERTIFICATE_TYPE_MAP = new HashMap<>();
    /**
     * 证书名称和照面属性映射
     */
    private static final Map<String, Map<String, String>> CERTIFICATE_NAME_ATTR_MAP = new HashMap<>();
    
    /**
     * 有效的证书类型列表
     */
    private static final List<String> VALID_CERTIFICATE_TYPES = Arrays.asList(
        "残骸清除责任保险或其他财务保证证书",
        "非持久性油类污染损害民事责任保险或其他财务保证证书",
        "燃油污染损害民事责任保险或其他财务保证证书（国际）",
        "燃油污染损害民事责任保险或其他财务保证证书（国内）",
        "油污损害民事责任保险或其他财务保证证书（国内）",
        "油污损害民事责任保险或其他财务保证证书（国际）"
    );
    
    /**
     * 初始化证书类型映射
     */
    @PostConstruct
    public void init() {
        CERTIFICATE_TYPE_MAP.put("油污损害民事责任保险或其他财务保证证书", "0100");
        CERTIFICATE_TYPE_MAP.put("燃油污染损害民事责任保险或其他财务保证证书", "0101");
        CERTIFICATE_TYPE_MAP.put("非持久性油类污染损害民事责任保险或其他财务保证证书", "0102");
        CERTIFICATE_TYPE_MAP.put("残骸清除责任保险或其他财务保证证书", "0103");
        CERTIFICATE_NAME_ATTR_MAP.put("油污损害民事责任保险或其他财务保证证书（国内）", OilPollutionWarrantyConstant.NON_PERSISTENT_REVERSE);
        CERTIFICATE_NAME_ATTR_MAP.put("油污损害民事责任保险或其他财务保证证书（国际）", OilPollutionWarrantyConstant.OIL_DAMAGE_REVERSE);
        CERTIFICATE_NAME_ATTR_MAP.put("燃油污染损害民事责任保险或其他财务保证证书（国内）", OilPollutionWarrantyConstant.NON_PERSISTENT_REVERSE);
        CERTIFICATE_NAME_ATTR_MAP.put("燃油污染损害民事责任保险或其他财务保证证书（国际）", OilPollutionWarrantyConstant.FUEL_POLLUTION_REVERSE);
        CERTIFICATE_NAME_ATTR_MAP.put("非持久性油类污染损害民事责任保险或其他财务保证证书", OilPollutionWarrantyConstant.NON_PERSISTENT_REVERSE);
        CERTIFICATE_NAME_ATTR_MAP.put("残骸清除责任保险或其他财务保证证书", OilDamageInsuranceCertConstant.OIL_DAMAGE_INSURANCE_REVERSET_MAP);
    }

    /**
     * 保存证书信息和照面属性
     *
     * @param certificateDto 证书数据传输对象
     * @return 保存结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<?> saveCertificateAndAttribute(CertificateDto certificateDto) {
        log.info("【外部推送证照数据】开始处理证照数据保存请求，证照名称：{}", certificateDto != null ? certificateDto.getCertificateName() : "空");
        
        if (certificateDto == null) {
            log.error("【外部推送证照数据】证书信息为空，保存失败");
            return ResultUtil.error("证书信息为空");
        }
        // 1. 校验主表信息
        boolean isDebrisCert = StrUtil.equals("残骸清除责任保险或其他财务保证证书", certificateDto.getCertificateName());
        log.info("【外部推送证照数据】开始校验证照主表信息，证照名称：{}，证照类型：{}，证照类型编码：{}",
                certificateDto.getCertificateName(), certificateDto.getCertificateType(), certificateDto.getCertificateTypeCode());
        if (!validateCertificateInfo(certificateDto, isDebrisCert)) {
            log.error("【外部推送证照数据】证照主表信息校验不通过，证照名称：{}", certificateDto.getCertificateName());
            return ResultUtil.error("证照主表信息校验不通过");
        }
        // 2. 校验照面信息
        log.info("【外部推送证照数据】开始校验证照照面信息，证照名称：{}，照面数据条数：{}",
                certificateDto.getCertificateName(), 
                certificateDto.getSurfaceData() != null ? certificateDto.getSurfaceData().size() : 0);
        if (!validateSurfaceData(certificateDto)) {
            log.error("【外部推送证照数据】证照照面信息校验不通过，证照名称：{}", certificateDto.getCertificateName());
            return ResultUtil.error("证照照面信息校验不通过");
        }
        // 3. 保存主表数据
        log.info("【外部推送证照数据】开始保存证照主表信息，证照名称：{}", certificateDto.getCertificateName());
        Certificate certificate = saveCertificateInfo(certificateDto, isDebrisCert);
        if (certificate == null) {
            log.error("【外部推送证照数据】保存证照主表信息失败，证照名称：{}", certificateDto.getCertificateName());
            return ResultUtil.error("保存证书信息失败");
        }
        // 4. 保存照面信息
        String id = certificate.getId();
        log.info("【外部推送证照数据】开始保存证照照面信息，证照ID：{}", id);
        if (!saveSurfaceData(certificateDto, id)) {
            log.error("【外部推送证照数据】保存证照照面信息失败，证照ID：{}", id);
            return ResultUtil.error("保存照面信息失败");
        }
        
        log.info("【外部推送证照数据】保存证书信息和照面属性成功，证书ID：{}，证书名称：{}", id, certificateDto.getCertificateName());
        return ResultUtil.data(id, "保存证书信息和照面属性成功");
    }
    
    /**
     * 校验主表信息
     *
     * @param certificateDto 证书数据传输对象
     * @param isDebrisCert 是否为残骸清除责任保险证书
     * @return 校验结果
     */
    private boolean validateCertificateInfo(CertificateDto certificateDto, boolean isDebrisCert) {
        String certificateName = certificateDto.getCertificateName();
        if (StrUtil.isBlank(certificateName)) {
            log.error("【外部推送证照数据】证书名称为空");
            return false;
        }
        // 校验证书类型
        String certificateType = certificateDto.getCertificateType();
        if (StrUtil.isBlank(certificateType)) {
            log.error("【外部推送证照数据】证书类型为空");
            return false;
        }
        // 校验证书类型是否在有效范围内
        if (!VALID_CERTIFICATE_TYPES.contains(certificateType)) {
            log.error("【外部推送证照数据】证书类型不在有效范围内：{}", certificateType);
            return false;
        }
        // 校验证书类型编码
        String certificateTypeCode = certificateDto.getCertificateTypeCode();
        String expectedTypeCode = CERTIFICATE_TYPE_MAP.get(certificateName);
        if (expectedTypeCode == null) {
            log.error("【外部推送证照数据】未知的证书名称：{}", certificateName);
            return false;
        }
        // 验证证书类型编码
        if (!expectedTypeCode.equals(certificateTypeCode)) {
            log.error("【外部推送证照数据】证书类型编码不匹配，预期：{}，实际：{}", expectedTypeCode, certificateTypeCode);
            return false;
        }

        if (isDebrisCert) {
            log.info("【外部推送证照数据】残骸清除责任保险证书校验通过");
            return true;
        }
        
        // 如果不是残骸清除责任保险证书，需要检查证书编号是否重复
        String certificateNum = certificateDto.getCertificateNum();
        if (StrUtil.isBlank(certificateNum)) {
            log.error("【外部推送证照数据】证书编号为空");
            return false;
        }
        // 查询是否存在相同证书编号的记录
        LambdaQueryWrapper<Certificate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Certificate::getCertificateNum, certificateNum);
        int count = this.count(queryWrapper);
        if (count > 0) {
            log.error("【外部推送证照数据】证照编号重复，保存失败，证照编号：{}", certificateNum);
            return false;
        }
        
        log.info("【外部推送证照数据】证照主表信息校验通过，证照名称：{}", certificateName);
        return true;
    }
    
    /**
     * 校验照面信息
     *
     * @param certificateDto 证书数据传输对象
     * @return 校验结果
     */
    private boolean validateSurfaceData(CertificateDto certificateDto) {
        List<Map<String, String>> surfaceData = certificateDto.getSurfaceData();
        if (CollUtil.isEmpty(surfaceData)) {
            log.error("【外部推送证照数据】证照照面信息值为空");
            return false;
        }
        // 校验照面信息是否有对应的映射
        String certificateType = certificateDto.getCertificateType();
        Map<String, String> attributeColumnMap = CERTIFICATE_NAME_ATTR_MAP.get(certificateType);
        // 校验照面信息字段是否有效
        for (Map<String, String> data : surfaceData) {
            String attributeName = data.get("name");
            if (!attributeColumnMap.containsKey(attributeName)) {
                log.error("【外部推送证照数据】照面信息属性名没有对应的字段映射：{}", attributeName);
                return false;
            }
        }
        
        log.info("【外部推送证照数据】证照照面信息校验通过，照面数据条数：{}", surfaceData.size());
        return true;
    }
    
    /**
     * 保存证书信息
     *
     * @param certificateDto 证书数据传输对象
     * @param isDebrisCert 是否为残骸清除责任保险证书
     * @return 保存的证书对象
     */
    private Certificate saveCertificateInfo(CertificateDto certificateDto, boolean isDebrisCert) {
        Certificate certificate = new Certificate();
        BeanUtil.copyProperties(certificateDto, certificate);
        // 如果是残骸清除责任保险证书，设置证书编号为'-'
        if (isDebrisCert) {
            certificate.setCertificateNum("-");
            log.info("【外部推送证照数据】残骸清除责任保险证书，设置证书编号为'-'");
        }
        // 设置数据来源（2-外部推送）
        certificate.setDataType("2");
        // 设置创建状态（0-待生成）
        certificate.setCreateStatus("0");
        // 设置状态标志（1-已登记）
        certificate.setStatusFlag("1");
        // 设置创建时间
        certificate.setCreateDate(new Date());
        // 保存证书信息
        boolean saveResult = this.save(certificate);
        if (!saveResult) {
            log.error("【外部推送证照数据】保存证书信息失败");
            return null;
        }
        
        log.info("【外部推送证照数据】保存证照主表信息成功，证照ID：{}", certificate.getId());
        return certificate;
    }
    
    /**
     * 保存照面信息
     *
     * @param certificateDto 证书数据传输对象
     * @param certificateId 证书ID
     * @return 保存结果
     */
    private boolean saveSurfaceData(CertificateDto certificateDto, String certificateId) {
        List<Map<String, String>> surfaceData = certificateDto.getSurfaceData();
        List<CertificateAttribute> attributeList = new ArrayList<>();
        // 获取照面信息字段映射名称
        Map<String, String> attributeColumnMap = CERTIFICATE_NAME_ATTR_MAP.get(certificateDto.getCertificateType());
        
        for (Map<String, String> data : surfaceData) {
            String name = data.get("name");
            String attributeValue = data.get("value");
            String attributeColumnName = attributeColumnMap.get(name);
            CertificateAttribute attribute = new CertificateAttribute();
            attribute.setCertificateId(certificateId);
            attribute.setAttributeName(attributeColumnName);
            attribute.setAttributeValue(attributeValue);
            attribute.setAttributeColumnName(name);
            attribute.setCreateDate(new Date());
            attributeList.add(attribute);
        }

        if(attributeList.isEmpty()) {
            log.error("【外部推送证照数据】照面信息为空");
            return false;
        }

        boolean saveAttributeResult = certificateAttributeService.saveBatch(attributeList);
        if (!saveAttributeResult) {
            log.error("【外部推送证照数据】保存证书照面属性失败");
            return false;
        }

        log.info("【外部推送证照数据】保存证照照面信息成功，证照ID：{}，照面数据条数：{}", certificateId, attributeList.size());
        return true;
    }
}
