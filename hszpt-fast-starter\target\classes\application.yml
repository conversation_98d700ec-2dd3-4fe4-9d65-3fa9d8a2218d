spring:
  application:
    name: hszpt
  profiles:
    active: dev
    #active: prod #线上配置文件

# HTTP透传服务相关配置
proxy:
  # 通用代理目标URL，当通过/proxy/**路径访问或未指定特定服务URL时使用
  # 例如：访问/proxy/api/users会被转发到http://example.com:8080/api/users
  target-url: http://example.com:8080
  
  # 是否启用鉴权，设置为true时会在请求头中添加Authorization: Bearer {auth-token}
  auth-enabled: false
  
  # 鉴权令牌，当auth-enabled为true时使用
  # 用于访问需要认证的API
  auth-token: your-auth-token
  
  # 船员证书查询服务URL（内网）
  # 用于/certificate/certificateIntranetCrewQuery接口的透传调用
  crew-certificate-url: http://127.0.0.1:8285/api/crew/certificates/intranet/query
  
  # 船员证书查询服务URL（外网）
  # 用于/certificate/crewCertificateQuery接口的透传调用
  crew-certificate-external-url: http://127.0.0.1:8285/api/crew/certificates/query
  
  # 证照签发统计服务URL
  # 用于/certificate/statistics/*接口的透传调用
  certificate-statistics-url: http://127.0.0.1:8285/api/certificate/statistics
  
  # 证照使用统计服务URL
  # 用于/certificate/usage/statistics/*接口的透传调用
  certificate-usage-statistics-url: http://127.0.0.1:8285/api/certificate/usage/statistics
  
  # 证书机构查询服务URL
  # 用于/certificate/queryCertOrgs接口的透传调用
  cert-org-query-url: http://127.0.0.1:8285/api/certificate/queryCertOrgs