package com.js.hszpt.service;

import com.js.hszpt.mapper.CertAppAuthDao;
import com.js.hszpt.entity.CertAppAuth;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.core.common.utils.PageUtil;
import com.js.core.common.vo.PageVo;
import com.js.core.common.vo.SearchVo;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
 
/**
 * 
 * @ClassName:  CertAppAuthService    
 * @Description:TODO(APPID授权信息表接口实现)   
 * @author:  System Generation
 *
 */
@Slf4j
@Service
public class CertAppAuthService extends ServiceImpl<CertAppAuthDao,CertAppAuth> {
	/**
	 * 多条件分页查询
	 * @param param
	 * @param searchVo
	 * @param pageVo
	 * @return
	 */
	public Page<CertAppAuth> findByCondition(CertAppAuth param, SearchVo searchVo, PageVo pageVo) {
		Page<CertAppAuth> page = PageUtil.initMpPage(pageVo);
		QueryWrapper<CertAppAuth> queryWrapper = this.getCondition(param,searchVo);
		return this.page(page, queryWrapper);
	}
	
	/**
	 * 
	 * @Title: findByCondition   
	 * @Description: TODO(多条件列表查询)   
	 * @param param
	 * @param searchVo
	 * @return  List<CertAppAuth>      
	 * @throws
	 */
	public List<CertAppAuth> findByCondition(CertAppAuth param, SearchVo searchVo){
		QueryWrapper<CertAppAuth> queryWrapper = this.getCondition(param, searchVo);
		//添加排序字段
		queryWrapper.orderByAsc("create_time");
		
		return this.list(queryWrapper);
	}
	
	
	/**
	 * 
	 * @Title: getCondition   
	 * @Description: TODO(构建自定义查询条件)   
	 * @param param
	 * @param searchVo
	 * @return  QueryWrapper<CertAppAuth>      
	 * @throws
	 */
	private QueryWrapper<CertAppAuth> getCondition(CertAppAuth param, SearchVo searchVo){
		QueryWrapper<CertAppAuth> queryWrapper = new QueryWrapper<CertAppAuth>();
		//请根据实际字段自行扩展
		 
		// 创建时间
		if (StrUtil.isNotBlank(searchVo.getStartDate()) && StrUtil.isNotBlank(searchVo.getEndDate())) {
			Date start = DateUtil.parse(searchVo.getStartDate());
			Date end = DateUtil.parse(searchVo.getEndDate());
			queryWrapper.between("create_time",start, DateUtil.endOfDay(end));
		}
		return queryWrapper;
		
	}
}