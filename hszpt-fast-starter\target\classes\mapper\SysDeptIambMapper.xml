<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.js.hszpt.mapper.SysDeptIambDao">

    <!-- 获取子机构树 -->
<!--    <select id="getSonOrgTree" resultType="com.js.hszpt.entity.SysDeptInfoResponse">-->
<!--        SELECT-->
<!--            DEPT_ID as deptId,-->
<!--            PARENT_ID as parentId,-->
<!--            NAME as name,-->
<!--            SORT as sort,-->
<!--            CREATE_TIME as createTime,-->
<!--            UPDATE_TIME as updateTime,-->
<!--            DEL_FLAG as delFlag-->
<!--        FROM SYS_DEPT_IAMB-->
<!--        WHERE PARENT_ID = #{deptId}-->
<!--          AND DEL_FLAG = '0'-->
<!--        ORDER BY SORT-->
<!--    </select>-->
    <select id="getSonOrgTree" resultType="com.js.hszpt.entity.SysDeptInfoResponse">
        SELECT *
        FROM (
                 SELECT
                     dept.dept_id AS tree_id,
                     dept.dept_id,
                     dept.name,
                     dept.parent_id,
                     dept.code,
                     CASE
                         WHEN (SELECT COUNT(1) FROM sys_dept deptSon WHERE deptSon.PARENT_ID = dept.DEPT_ID) > 0
                         THEN true
                         ELSE false
                     END AS is_More
                 FROM
                     sys_dept dept
                 WHERE
                     dept.parent_id = #{deptId}
                    AND dept.GOV_LEVEL &lt; 3
                 UNION ALL
                 SELECT
                     dept.dept_id || 't1' AS tree_id,
                     dept.dept_id,
                     dept.name,
                     dept.parent_id,
                     dept.code || 'bj' AS code,
                     false AS is_More
                 FROM
                     sys_dept dept
                 WHERE
                     dept.dept_id = #{deptId}
                        AND dept.GOV_LEVEL &lt; 3
             ) AS combined
        ORDER BY
            LENGTH(code), code
    </select>

    <select id="deptSon" resultType="com.js.hszpt.vo.SysDeptInfoResponse">
        select *
        from (select dept."DEPT_ID" tree_id,
        dept.*,
        (select count(1)
        from "sys_dept_iamb" deptSon
        where deptSon."PARENT_ID" = dept."DEPT_ID" and "GOV_LEVEL" &lt;= '3') is_More
        from (select dept."DEPT_ID", dept.name, dept."PARENT_ID", dept."CODE"
        from "sys_dept_iamb" dept
        <where>
            <if test="orgCode != null and orgCode != ''">
                and dept."code" = #{orgCode}
            </if>
            <if test="orgCode != null and orgCode != ''">
                and dept."code" = #{orgCode}
            </if>
        </where>
        ) dept) as "tid.*iM"
        order by "CODE"
    </select>

    <!-- 根据机构编码查询机构 -->
    <select id="findDeptByOrgCode" resultType="com.js.hszpt.entity.SysDeptIamb">
        SELECT * FROM sys_dept
        WHERE code = #{orgCode}
          AND del_flag = '0'
    </select>

    <!-- 根据父级ID查询子机构 -->
    <select id="findDeptByParentId" resultType="com.js.hszpt.entity.SysDeptIamb">
        SELECT * FROM sys_dept
        WHERE parent_id = #{parentId}
          AND del_flag = '0'
        ORDER BY sort
    </select>

    <!-- 一次性查询所有相关机构（最多3级）- 非递归版本 -->
    <select id="findAllDescendants" resultType="com.js.hszpt.entity.SysDeptIamb">
        -- 查询第1级子机构
        SELECT * FROM sys_dept
        WHERE parent_id = #{rootId} AND del_flag = '0'

        UNION ALL

        -- 查询第2级子机构
        SELECT c.*
        FROM sys_dept c
                 JOIN sys_dept p ON c.parent_id = p.dept_id
        WHERE p.parent_id = #{rootId}
          AND c.del_flag = '0'
          AND p.del_flag = '0'

        UNION ALL

        -- 查询第3级子机构
        SELECT c.*
        FROM sys_dept c
                 JOIN sys_dept p1 ON c.parent_id = p1.dept_id
                 JOIN sys_dept p2 ON p1.parent_id = p2.dept_id
        WHERE p2.parent_id = #{rootId}
          AND c.del_flag = '0'
          AND p1.del_flag = '0'
          AND p2.del_flag = '0'

        ORDER BY code, sort
    </select>

    <select id="deptNow" resultType="com.js.hszpt.vo.SysDeptInfoResponse">
        SELECT
            dept.dept_id AS tree_id,
            dept.*,
            (SELECT COUNT(1)
             FROM sys_dept_iamb deptSon
             WHERE deptSon.PARENT_ID = dept.DEPT_ID) AS is_More
        FROM (
                 SELECT dept.*
                 FROM sys_dept_iamb dept
                 WHERE dept.code = #{deptId}
             ) dept
    </select>

    <!-- 查询去重后的name -->
    <select id="getCurrentDeptInfo" resultType="com.js.hszpt.entity.SysDeptIamb">
        SELECT *
        FROM (
                 SELECT
                     a.*,
                     ROW_NUMBER() OVER (PARTITION BY name ORDER BY code) AS rn
                 FROM sys_dept_iamb a
                 WHERE code ILIKE #{benCode}
              AND name IN ('法规规范处（执法督察处）', '法规规范处', '政策法规处')
             ) AS subquery
        WHERE rn = 1;
    </select>

    <select id="deptSonApp" resultType="com.js.hszpt.vo.SysDeptInfoResponse">
            select *
            from (select dept."DEPT_ID" tree_id,
            dept.*,
            (select count(1)
            from "SYS_DEPT" deptSon
            where deptSon."PARENT_ID" = dept."DEPT_ID" and "GOV_LEVEL" &lt;= '3') is_More
            from (select dept."DEPT_ID", dept.name, dept."PARENT_ID", dept."CODE"
            from "SYS_DEPT" dept
            <where>
                <if test="orgCode != null and orgCode != '' and flag == 1">
                    and dept."DEPT_ID" = #{orgCode}
                </if>
                <if test="orgCode != null and orgCode != '' and flag == 2">
                    and dept.parent_id = #{orgCode}
                </if>
            </where>
            ) dept) as "tid.*iM"
            order by "CODE"
        </select>
</mapper>
