package com.js.hszpt.dto;

import lombok.Data;

/**
 * 证照签发按年统计结果DTO
 */
@Data
public class CertificateIssueTimeTypeStatisticsDTO {

    /**
     * 机构编码
     */
    private String orgCode;

    /**
     * 机构类型
     */
    private String orgType;

    /**
     * 机构名称
     */
    private String orgName;
    
    /**
     * 年份
     */
    private String year;

    /**
     * 时间维度
     */
    private String timePoint;

    /**
     * 签发数量
     */
    private Long issueCount;

}