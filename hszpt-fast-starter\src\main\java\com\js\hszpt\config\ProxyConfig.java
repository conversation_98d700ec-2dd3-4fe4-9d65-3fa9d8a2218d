package com.js.hszpt.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * HTTP透传服务配置类
 */
@Component
@ConfigurationProperties(prefix = "proxy")
public class ProxyConfig {

    /**
     * 目标服务器URL
     */
    private String targetUrl;

    /**
     * 是否启用鉴权
     */
    private boolean authEnabled;

    /**
     * 鉴权令牌
     */
    private String authToken;

    private String appKey;


    private String appSecret;

    private String ssoType;

    public String getAppKey() {
    return appKey;
    }public void setAppKey(String appKey) {
        this.appKey = appKey;
    }public String getAppSecret() {
        return appSecret;
    }public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }public String getSsoType() {
        return ssoType;
    }public void setSsoType(String ssoType) {
        this.ssoType = ssoType;
    }

    public String getTargetUrl() {
        return targetUrl;
    }

    public void setTargetUrl(String targetUrl) {
        this.targetUrl = targetUrl;
    }

    public boolean isAuthEnabled() {
        return authEnabled;
    }

    public void setAuthEnabled(boolean authEnabled) {
        this.authEnabled = authEnabled;
    }

    public String getAuthToken() {
        return authToken;
    }

    public void setAuthToken(String authToken) {
        this.authToken = authToken;
    }
}
