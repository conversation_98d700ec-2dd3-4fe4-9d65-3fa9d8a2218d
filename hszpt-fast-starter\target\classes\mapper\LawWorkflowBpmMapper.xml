<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.js.hszpt.mapper.LawWorkflowBpmDao">

    <select id="queryCertTypeDirectoryTodoList" resultType="com.js.hszpt.vo.CertTypeDirectoryTodoVO">
        SELECT * FROM (
        -- 待办数据查询 (显示"处理"按钮)
        SELECT
        a.APPLY_NO as applyNo,
        a.APPLY_ORG_NAME as publish_org_name,
        a.APPLY_TYPE as applyType,
        (select title from t_dict_data
            where dict_id =
                  (select t_dict.id from t_dict where type = 'CERT_APPLY_TYPE')
              and value = a.APPLY_TYPE) as apply_type_name,
        a.ACCEPT_TIME as acceptTime,
        a.APPROVAL_TIME as approvalTime,
        a.PUBLISH_TIME as publishTime,
        d.CERTIFICATE_TYPE_NAME as certificate_type_name,
        d.CERTIFICATE_TYPE_CODE as certificate_type_code,
        d.CERTIFICATE_DEFINE_AUTHORITY_NAME as create_org_name,
        (SELECT STRING_AGG("CERTIFICATE_NAME", ',') FROM "CTF_CERTIFICATE_TYPE"
        WHERE "CERT_TYPE_DIR_CODE" = d."CERTIFICATE_TYPE_CODE"
        AND "CERT_TYPE_DIR_CODE" IS NOT NULL) as certificate_name,
        d.RELATED_ITEM_NAME as related_item_name,
        d.RELATED_ITEM_CODE as related_item_code,
        d.CERTIFICATE_HOLDER_CATEGORY as certificateHolderCategory,
        (select title from t_dict_data
            where dict_id =
                  (select t_dict.id from t_dict where type = 'CERTIFICATE_HOLDER_CATEGORY')
              and value = d.CERTIFICATE_HOLDER_CATEGORY) as certificate_holder_category_name,
        d.VALIDITY_RANGE as validity_range,
        d.APPROVAL_STATUS as approval_status,
        (select title from t_dict_data
            where dict_id =
                  (select t_dict.id from t_dict where type = 'approvalStatus')
              and value = d.APPROVAL_STATUS) as approval_status_name,
        d.ISSUE_STATUS as issue_status,
        (select title from t_dict_data
            where dict_id =
                  (select t_dict.id from t_dict where type = 'ISSUE_STATUS')
              and value = d.ISSUE_STATUS) as issue_status_name,
        d.ISSUE_DATE as issue_date,
        a.ACCEPT_ORG_NAME as accept_org_name,
        a.APPROVAL_ORG_NAME as approval_org_name,
        d.CREATE_BY as createBy,
        d.CREATE_TIME as createTime,
        d.UPDATE_BY as updateBy,
        d.UPDATE_TIME as updateTime,
        a.CURRENT_NODE as current_node,
        a.STATUS as status,
        (select title from t_dict_data
            where dict_id =
                  (select t_dict.id from t_dict where type = 'CTF_APPROVAL_STATUS')
              and value = a.STATUS) as status_name,
        d.CERT_TYPE_DIR_ID as certTypeDirId,
        a.APPLICANT_ID as certApprApplyId,
        NULL as certTypeApprovalId,
        w.EXECUTION_ID as executionId,
        w.WORKFLOW_BPM_ID as workflowBpmId,
        a.cert_type_appr_apply_id as certTypeApprApplyId,
        '1' as showProcessButton, -- 显示"处理"按钮标识
        '0' as showViewButton -- 不显示"查看"按钮标识
        FROM LAW_WORKFLOW_BPM w
        LEFT JOIN CERT_TYPE_APPROVAL_APPLY a ON w.EXECUTION_ID = a.EXECUTION_ID
        LEFT JOIN CERT_TYPE_DIRECTORY d ON a.CERT_TYPE_DIR_ID = d.CERT_TYPE_DIR_ID
        WHERE d.DEL_FLAG = '0'
        AND a.DEL_FLAG = '0'
        AND w.USER_CODE = #{currentUserId}

        UNION

        -- 已办数据查询 (显示"查看"按钮)
        SELECT
        a.APPLY_NO as applyNo,
        a.APPLY_ORG_NAME as publish_org_name,
        a.APPLY_TYPE as applyType,
        (select title from t_dict_data
            where dict_id =
                  (select t_dict.id from t_dict where type = 'CERT_APPLY_TYPE')
              and value = a.APPLY_TYPE) as apply_type_name,
        a.ACCEPT_TIME as acceptTime,
        a.APPROVAL_TIME as approvalTime,
        a.PUBLISH_TIME as publishTime,
        d.CERTIFICATE_TYPE_NAME as certificate_type_name,
        d.CERTIFICATE_TYPE_CODE as certificate_type_code,
        d.CERTIFICATE_DEFINE_AUTHORITY_NAME as create_org_name,
        (SELECT STRING_AGG("CERTIFICATE_NAME", ',') FROM "CTF_CERTIFICATE_TYPE"
        WHERE "CERT_TYPE_DIR_CODE" = d."CERTIFICATE_TYPE_CODE"
        AND "CERT_TYPE_DIR_CODE" IS NOT NULL) as certificate_name,
        d.RELATED_ITEM_NAME as related_item_name,
        d.RELATED_ITEM_CODE as related_item_code,
        d.CERTIFICATE_HOLDER_CATEGORY as certificateHolderCategory,
        (select title from t_dict_data
            where dict_id =
                  (select t_dict.id from t_dict where type = 'CERTIFICATE_HOLDER_CATEGORY')
              and value = d.CERTIFICATE_HOLDER_CATEGORY) as certificate_holder_category_name,
        d.VALIDITY_RANGE as validity_range,
        d.APPROVAL_STATUS as approval_status,
        (select title from t_dict_data
            where dict_id =
                  (select t_dict.id from t_dict where type = 'approvalStatus')
              and value = d.APPROVAL_STATUS) as approval_status_name,
        d.ISSUE_STATUS as issue_status,
        (select title from t_dict_data
            where dict_id =
                  (select t_dict.id from t_dict where type = 'ISSUE_STATUS')
              and value = d.ISSUE_STATUS) as issue_status_name,
        d.ISSUE_DATE as issue_date,
        a.ACCEPT_ORG_NAME as accept_org_name,
        a.APPROVAL_ORG_NAME as approval_org_name,
        ta.CREATE_BY as createBy,
        ta.CREATE_TIME as createTime,
        ta.UPDATE_BY as updateBy,
        ta.UPDATE_TIME as updateTime,
        a.CURRENT_NODE as current_node,
        a.STATUS as status,
        (select title from t_dict_data
            where dict_id =
                  (select t_dict.id from t_dict where type = 'CTF_APPROVAL_STATUS')
              and value = a.STATUS) as status_name,
        d.CERT_TYPE_DIR_ID as certTypeDirId,
        a.APPLICANT_ID as certApprApplyId,
        a.CERT_TYPE_APPR_APPLY_ID as certTypeApprovalId,
        a.EXECUTION_ID as executionId,
        NULL as workflowBpmId,
        a.cert_type_appr_apply_id as certTypeApprApplyId,
        '0' as showProcessButton, -- 不显示"处理"按钮标识
        '1' as showViewButton -- 显示"查看"按钮标识
        FROM (select * from CERT_TYPE_APPROVAL where handle_status = '1') ta
        LEFT JOIN CERT_TYPE_APPROVAL_APPLY a ON ta.CERT_TYPE_APPR_APPLY_ID = a.CERT_TYPE_APPR_APPLY_ID
        LEFT JOIN CERT_TYPE_DIRECTORY d ON a.CERT_TYPE_DIR_ID = d.CERT_TYPE_DIR_ID
        WHERE ta.DEL_FLAG = '0'
        AND d.DEL_FLAG = '0'
        AND a.DEL_FLAG = '0'
        AND (ta.CREATE_BY = #{currentUsername} or ta.UPDATE_BY = #{currentUsername} OR ta.APPLICANT_ID = #{currentUserId})
        ) t
        WHERE 1=1
        <if test="query.certTypeId != null and query.certTypeId != ''">
            and t.certTypeDirId in
            <foreach collection="query.certTypeId.split(',')" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="query.certTypeCode != null and query.certTypeCode != ''">
            AND t.certificate_type_code LIKE concat('%',#{query.certTypeCode},'%')
        </if>
        <if test="query.parentId != null and query.parentId != ''">
            AND t.certificate_type_code IN
            <foreach collection="query.parentId.split(',')" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="query.certName != null and query.certName != ''">
            AND t.certificate_type_code IN
            <foreach collection="query.certName.split(',')" item="name" open="(" separator="," close=")">
                #{name}
            </foreach>
        </if>
        <if test="query.orgName != null and query.orgName != ''">
            AND t.create_org_name IN
            <foreach collection="query.orgName.split(',')" item="org" open="(" separator="," close=")">
                #{org}
            </foreach>
        </if>
        <if test="query.acceptOrgCode != null">
            AND t.accept_org_name IN
            <foreach collection="query.acceptOrgCode.spilt(',')" item="org" open="(" separator="," close=")">
                #{org}
            </foreach>
        </if>
        <if test="query.publishOrgCode != null">
            AND t.publish_org_name IN
            <foreach collection="query.publishOrgCode.spilt(',')" item="org" open="(" separator="," close=")">
                #{org}
            </foreach>
        </if>
        <if test="query.approvalOrgCode != null">
            AND t.approval_org_name IN
            <foreach collection="query.approvalOrgCode.spilt(',')" item="org" open="(" separator="," close=")">
                #{org}
            </foreach>
        </if>
        <if test="query.affairName != null and query.affairName != ''">
            AND t.related_item_name in
            <foreach collection="query.affairName.split(',')" item="name" open="(" separator="," close=")">
                #{name}
            </foreach>
        </if>
        <if test="query.affairNo != null and query.affairNo != ''">
            AND t.related_item_code LIKE '%'||#{query.affairNo}||'%'
        </if>
        <if test="query.certificateHolderCategory != null and query.certificateHolderCategory != ''">
            AND t.certificateHolderCategory in
            <foreach collection="query.certificateHolderCategory.split(',')" item="type" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
        <if test="query.validPeriod != null and query.validPeriod != ''">
            AND t.validity_range in
            <foreach collection="query.validPeriod.split(',')" item="period" open="(" separator="," close=")">
                #{period}
            </foreach>
        </if>
        <if test="query.applyType != null and query.applyType != ''">
            AND t.applyType = #{query.applyType}
        </if>
        <if test="query.applyNo != null and query.applyNo != ''">
            AND t.applyNo = #{query.applyNo}
        </if>
        <if test="query.approvalStatus != null and query.approvalStatus != ''">
            AND t.approval_status = #{query.approvalStatus}
        </if>
        <if test="query.issueStatus != null and query.issueStatus != ''">
            AND t.issue_status = #{query.issueStatus}
        </if>
        <if test="query.acceptTimeStart != null and query.acceptTimeEnd != null">
            AND t.acceptTime BETWEEN #{query.acceptTimeStart} AND #{query.acceptTimeEnd}
        </if>
        <if test="query.approvalTimeStart != null and query.approvalTimeEnd != null">
            AND t.approvalTime BETWEEN #{query.approvalTimeStart} AND #{query.approvalTimeEnd}
        </if>
        <if test="query.issueDateStart != null and query.issueDateEnd != null">
            AND t.issue_date BETWEEN #{query.issueDateStart} AND #{query.issueDateEnd}
        </if>
        <if test="query.createTimeStart != null and query.createTimeEnd != null">
            AND t.createTime BETWEEN #{query.createTimeStart} AND #{query.createTimeEnd}
        </if>
        ORDER BY t.createTime DESC
    </select>
</mapper>