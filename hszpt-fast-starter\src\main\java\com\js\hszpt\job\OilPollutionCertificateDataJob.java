package com.js.hszpt.job;

import com.js.hszpt.config.OilPollutionCertificateDataConfig;
import com.js.hszpt.enmus.AffairApplyCleanStatus;
import com.js.hszpt.enmus.ModelType;
import com.js.hszpt.entity.BizAffairApply;
import com.js.hszpt.service.BizAffairApplyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 数据清洗定时任务
 */
@Component
public class OilPollutionCertificateDataJob {
    @Value("${job.OilPollutionCertificateData.enabled:false}")
    private boolean enabled;

    @Autowired
    private BizAffairApplyService bizAffairApplyService;

    @Autowired
    private OilPollutionCertificateDataConfig oilPollutionCertificateDataConfig;

    // 数据清洗
    @Scheduled(cron = "${job.OilPollutionCertificateData.cron}")
    public void execute() {
        if (!enabled) {
            return;
        }
        List<BizAffairApply> bizAffairApplyList = bizAffairApplyService.getApply(ModelType.JTB15020, AffairApplyCleanStatus.UN_CREATE);
        bizAffairApplyList.forEach(bizAffairApply -> oilPollutionCertificateDataConfig.createCertificate(bizAffairApply));
    }
}
