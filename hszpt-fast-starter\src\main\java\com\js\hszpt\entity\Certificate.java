package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableField;import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@TableName("CTF_CERTIFICATE")
@ApiModel(value = "证照基本信息表")
public class Certificate {

    // 主键
    @TableId
    private String id;

    // 电子证照唯一ID，生成证照后获得
    private String certificateId;

    // 证书名称
    private String certificateName;

    /**
     * 证照类型:"社会团体法人登记证书"，调电子证照接口使用
     */
    private String certificateType;

    // 证书印刷号
    private String certPrintNo;

    // 关联到证照分类表的证照分类编码
    private String certificateTypeCode;

    // 发证机关名称
    private String issuOrgNameCn;

    // 发证机关代码:2.0版本
    private String issuOrgCode2;

    // 发证机关代码:3.0版本
    private String issuOrgCode3;

    // 持有人姓名
    private String holderName;

    // 持有人身份标识号码，例如身份证号、统一社会信用代码
    private String holderIdentityNumber;

    // 签发日期(yyyyMMddhhmiss)
    @DateTimeFormat(pattern = "yyyy-MM-dd") // 用于Spring MVC绑定
    @JsonFormat(pattern = "yyyy-MM-dd")    // 用于JSON序列化
    private Date issueDate;

    // 有效期日期-开始(yyyyMMddhhmiss)
    @DateTimeFormat(pattern = "yyyy-MM-dd") // 用于Spring MVC绑定
    @JsonFormat(pattern = "yyyy-MM-dd")    // 用于JSON序列化
    private Date effectDate;

    // 有效期日期-结束(yyyyMMddhhmiss)
    @DateTimeFormat(pattern = "yyyy-MM-dd") // 用于Spring MVC绑定
    @JsonFormat(pattern = "yyyy-MM-dd")    // 用于JSON序列化
    private Date expireDate;

    // 证照使用状态:1-已登记；3-注销
    private String statusFlag;

    // 电子证照生成状态:0-待生成 1-已生成 2-生成失败
    private String createStatus;

    // 申请ID/编号，冗余该证书关联的唯一业务编号
    private String applyId;

    // 船舶识别号 永久识别中国籍船舶的唯一编码
    private String shipId;

    // 船舶名称
    private String shipName;

    // 创建人
    private String createOperId;

    // 创建时间
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") // 用于Spring MVC绑定
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")    // 用于JSON序列化
    private Date createDate;

    // 修改人
    private String modifyOperId;

    // 修改时间（注意字段名拼写错误已修正）
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") // 用于Spring MVC绑定
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")    // 用于JSON序列化
    private Date modifyDate;

    // 逻辑删除标记(0--正常 1--删除)
    @TableLogic(value = "0", delval = "1")
    private String delFlag;

    // 证书编号
    private String certificateNum;

    //事项名称
    private String affairName;

    //申请人
    private String applicantName;

    //申请日期
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") // 用于Spring MVC绑定
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")    // 用于JSON序列化
    private Date applyDate;

    //申请来源
    private String applyType;

    //受理机构编码2.0
    private String acceptOrgCode2;

    //受理日期
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") // 用于Spring MVC绑定
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")    // 用于JSON序列化
    private Date acceptDate;

    //审批机构编码2.0
    private String apprOrgCode2;

    //审批日期
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") // 用于Spring MVC绑定
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")    // 用于JSON序列化
    private Date apprDate;

    // IMO
    private String shipImo;

    // 船舶呼号
    private String shipCallSign;

    // 船舶英文名
    private String shipNameEn;

    // 申请编号
    private String applyNo;

    // 申请人类型
    private String proType;

    // 受理机构编码3.0
    private String acceptOrgCode3;

    // 审批机构编码3.0
    private String apprOrgCode3;

    //MMSI
    private String shipMmsi;

    // 数据来源：1-系统自动生成 2-外部推送
    private String dataType;
}
