package com.js.hszpt.entity;

import com.js.core.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.Builder;
import java.util.Date;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;


/**
 * 
 * @ClassName:  OfficerInfoCert   
 * @Description:TODO()   
 * @author:   System Generation 
 */
@Data

@TableName("officer_info_cert")
@ApiModel(value = "")
public class OfficerInfoCert extends BaseEntity {

    private static final long serialVersionUID = 1L;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String officerName;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String department;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String provDeptName;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String orgCode;


}