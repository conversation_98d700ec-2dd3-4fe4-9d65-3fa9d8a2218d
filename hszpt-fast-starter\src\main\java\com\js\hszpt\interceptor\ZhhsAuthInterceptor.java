package com.js.hszpt.interceptor;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.js.common.entity.CurrentUser;
import com.js.hszpt.utils.ZptUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 智慧海事登录认证拦截器
 * 从请求头获取authorization_zhhs信息并验证
 */
@Component
@Slf4j
public class ZhhsAuthInterceptor implements HandlerInterceptor {

    // Redis缓存前缀
    private static final String REDIS_KEY_PREFIX = "zhhs_auth:";
    // 缓存有效期（分钟）
    private static final long CACHE_EXPIRATION_MINUTES = 60;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Value("${api.login-check.url:http://198.10.51.2:8090/main/getLoginUser}")
    private String loginCheckUrl;

    @Value("${spring.profiles.active:prod}")
    private String active;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 从请求头获取authorization_zhhs (尝试多种可能的header名称)
        String authToken = request.getHeader("Authorization-zhhs");
        
        // 增加请求头调试信息，查看所有请求头
//        log.info("请求的所有头信息：");
//        java.util.Enumeration<String> headerNames = request.getHeaderNames();
//        while (headerNames.hasMoreElements()) {
//            String headerName = headerNames.nextElement();
//            log.info("Header: {} = {}", headerName, request.getHeader(headerName));
//        }
        
        // 如果没获取到，尝试其他可能的大小写或格式
        if (StrUtil.isEmpty(authToken)) {
            authToken = request.getHeader("authorization-zhhs");
        }
        
        log.info("从请求头获取智慧海事token信息：{}", authToken);
        if (StrUtil.equalsAny(active, "dev", "local")) {
            authToken = "eyJhbGciOiJIUzUxMiJ9";
        }

        // 如果token为空，则不进行处理（可能由业务层处理未登录情况）
        if (StrUtil.isEmpty(authToken)) {
            return true;
        }
        
        // 查找Redis中是否已有该token的用户信息
        String redisKey = REDIS_KEY_PREFIX + authToken;
        CurrentUser currentUser = (CurrentUser) redisTemplate.opsForValue().get(redisKey);
        
        if (currentUser == null) {
            // Redis中不存在，调用验证方法获取用户信息
            currentUser = validateZhhsLogin(authToken);
            
            // 如果验证成功，将用户信息存入Redis
            if (currentUser != null) {
                redisTemplate.opsForValue().set(redisKey, currentUser, CACHE_EXPIRATION_MINUTES, TimeUnit.MINUTES);
            }
        }
        
        // 将用户信息存入request属性，供后续请求使用
        if (currentUser != null) {
            request.setAttribute("zhhs_user_info", currentUser);
        }
        
        return true;
    }
    
    /**
     * 验证智慧海事登录token
     * @param authToken 从请求头获取的authorization_zhhs值
     * @return 用户信息对象，验证失败返回null
     */
    private CurrentUser validateZhhsLogin(String authToken) {
        try {
            if (StrUtil.isEmpty(authToken)) {
                log.error("智慧海事登录验证失败：认证令牌为空");
                return null;
            }
            
            // 调用智慧海事认证接口
            HttpResponse response = null;
            String body = null;
            if (StrUtil.equalsAny(active, "dev", "local")) {
                body = "{\n" +
                        "    \"success\": true,\n" +
                        "    \"code\": 200,\n" +
                        "    \"message\": \"请求成功\",\n" +
                        "    \"data\": {\n" +
                        "        \"id\": \"1746181419554467842\",\n" +
                        "        \"account\": \"Sg9H+argvktadcUEoQA1F+g/63lxxnh4/to5bWhzRbQ=\",\n" +
                        "        \"nickName\": null,\n" +
                        "        \"name\": \"郑文厚\",\n" +
                        "        \"avatar\": null,\n" +
                        "        \"birthday\": null,\n" +
                        "        \"sex\": 3,\n" +
                        "        \"email\": null,\n" +
                        "        \"phone\": \"***********\",\n" +
                        "        \"tel\": null,\n" +
                        "        \"adminType\": 2,\n" +
                        "        \"lastLoginIp\": \"**************\",\n" +
                        "        \"lastLoginTime\": \"2025-04-26 09:43:57\",\n" +
                        "        \"lastLoginAddress\": null,\n" +
                        "        \"lastLoginBrowser\": \"curl/7.71.1\",\n" +
                        "        \"lastLoginOs\": \"-\",\n" +
                        "        \"code\": \"************0505\",\n" +
                        "        \"onlineDepOrg\": \"01020201\",\n" +
                        "        \"depName\": \"船舶动态监控中心项目管理科\",\n" +
                        "        \"level\": 3,\n" +
                        "        \"mapLevel\": 11,\n" +
                        "        \"certNo\": \"Sg9H+argvktadcUEoQA1F+g/63lxxnh4/to5bWhzRbQ=\",\n" +
                        "        \"dataScopes\": null,\n" +
                        "        \"tenants\": null,\n" +
                        "        \"enabled\": true,\n" +
                        "        \"password\": null,\n" +
                        "        \"username\": \"Sg9H+argvktadcUEoQA1F+g/63lxxnh4/to5bWhzRbQ=\",\n" +
                        "        \"accountNonExpired\": true,\n" +
                        "        \"accountNonLocked\": true,\n" +
                        "        \"credentialsNonExpired\": true,\n" +
                        "        \"authorities\": [\n" +
                        "            {\n" +
                        "                \"authority\": \"普通用户\"\n" +
                        "            }\n" +
                        "        ]\n" +
                        "    },\n" +
                        "    \"total\": null\n" +
                        "}";
            }else{
                response = HttpRequest.get(loginCheckUrl)
                    .header("Authorization", "Bearer " + authToken)
                    .timeout(10000)
                    .execute();
                if (!response.isOk()) {
                    log.error("智慧海事登录验证接口调用失败, 状态码: " + response.getStatus());
                    return null;
                }
                log.info("智慧海事登录接口返回信息：{}", response);
                body = response.body();
            }

            JSONObject result = JSONObject.parseObject(body);
            
            if (result == null || result.getInteger("code") != 200) {
                log.error("智慧海事登录验证失败, 响应内容: " + body);
                return null;
            }
            
            // 解析返回的用户信息
            JSONObject userData = result.getJSONObject("data");
            if (userData == null) {
                log.error("智慧海事登录验证失败: 返回的用户数据为空");
                return null;
            }
            
            // 构建用户信息对象
            CurrentUser currentUser = new CurrentUser();
            currentUser.setId(userData.getString("id"));
            currentUser.setUsername(userData.getString("name")); //account 是加密的，按name存储
            currentUser.setNickName(userData.getString("name"));
            currentUser.setDepartmentTitle(userData.getString("depName"));
            currentUser.setDepartmentCode(userData.getString("code"));
            currentUser.setOrgLevel(StrUtil.toString(userData.getInteger("level")));

            //处理智慧海事机构存储部门编码问题
            String orgCode = userData.getString("onlineDepOrg");
            log.info("处理前智慧海事用户机构编码：{}", orgCode);
            orgCode = ZptUtil.processOrgCode(orgCode);
            log.info("处理后智慧海事用户机构编码：{}", orgCode);
            currentUser.setDataOrgId(orgCode);

            log.info("智慧海事登录验证成功，用户信息姓名: {}，机构编码：{}，部门编码：{}",
                    currentUser.getNickName(), currentUser.getDataOrgId(), currentUser.getDepartmentCode());
            return currentUser;
            
        } catch (Exception e) {
            log.error("智慧海事登录验证异常: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
} 