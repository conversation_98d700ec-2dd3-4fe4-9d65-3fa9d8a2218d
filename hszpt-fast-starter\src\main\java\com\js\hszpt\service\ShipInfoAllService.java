package com.js.hszpt.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.hszpt.entity.ShipInfoAll;
import com.js.hszpt.mapper.ShipInfoAllMapper;
import org.springframework.stereotype.Service;


@Service
public class ShipInfoAllService extends ServiceImpl<ShipInfoAllMapper, ShipInfoAll> {

    public ShipInfoAll getShipInfoByShipId(String shipId) {
        QueryWrapper<ShipInfoAll> queryWrapper = Wrappers.<ShipInfoAll>query();
        queryWrapper.lambda()
                .eq(ShipInfoAll::getShipId,shipId)
                .eq(ShipInfoAll::getStatusFlagCode,"1")
                .last("LIMIT 1");
        return this.getOne(queryWrapper,false);
    }
}
