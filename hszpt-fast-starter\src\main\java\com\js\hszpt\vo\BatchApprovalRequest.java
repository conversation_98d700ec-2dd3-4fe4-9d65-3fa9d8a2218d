package com.js.hszpt.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@ApiModel("批量审批请求对象")
public class BatchApprovalRequest {
    @NotNull(message = "办件ID列表不能为空")
    @ApiModelProperty("办件ID列表")
    private List<String> certTypeApprApplyIds;

    @NotNull(message = "审批信息不能为空")
    @ApiModelProperty("审批信息")
    private CertTypeApprovalSubmitVO submitVO;
} 