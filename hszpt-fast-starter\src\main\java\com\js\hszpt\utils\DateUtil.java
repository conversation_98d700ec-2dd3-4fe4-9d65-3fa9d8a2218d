package com.js.hszpt.utils;

import com.js.hszpt.constants.Common;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

public class DateUtil {
    /**
     * 获取某个时间往前往后n的日期集合
     * date ==null 默认是系统当前时间  否则以date为时间起点
     * n > 0 往后
     * n = 0 当前时间
     * n < 0 往前
     * @param date
     * @param n
     * @return
     */
    public static List<String> getBeforeOrAfterDate(Date date, int n) {
        List<String> list = new ArrayList<>();
        Calendar c = Calendar.getInstance();
        Date today = new Date();
        if (null != date) {
            today = date;
        }
        c.setTime(today);
        if (n > 0) {
            for (int i = 0; i <= n; i++) {
                c.add(Calendar.DATE, i);
                list.add(new SimpleDateFormat("yyyyMMdd").format(c.getTime()));
                c.setTime(today);
            }
        } else if (n < 0) {
            for (int i = n; i <= 0; i++) {
                c.add(Calendar.DATE, i);
                list.add(new SimpleDateFormat("yyyyMMdd").format(c.getTime()));
                c.setTime(today);
            }
        } else {
            c.add(Calendar.DATE, 0);
            list.add(new SimpleDateFormat("yyyyMMdd").format(c.getTime()));
            c.setTime(today);
        }
        return list;
    }

    public static int getDaySub(String beginDateStr, String endDateStr) {
        long day = 0;
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Date beginDate;
        Date endDate;
        try {
            beginDate = format.parse(beginDateStr);
            endDate = format.parse(endDateStr);
            day = (endDate.getTime() - beginDate.getTime()) / (24 * 60 * 60 * 1000);
        } catch (ParseException e) {
            return (int) day;
        }
        return (int) day;
    }

    /**
     * 字符串时间转中文时间
     * 2020-01-01-------二〇二〇年〇一月〇一日
     */
    public static String getDateCn(String dateString) {
        String result = "";
        String[] cnDate = new String[]{"零", "一", "二", "三", "四", "五", "六", "七", "八", "九"};
        String ten = "十";
        String[] dateStr = dateString.split("-");
        for (int i = 0; i < dateStr.length; i++) {
            for (int j = 0; j < dateStr[i].length(); j++) {
                String charStr = dateStr[i];
                String str = String.valueOf(charStr.charAt(j));
                if (charStr.length() == 2) {
                    if (charStr.equals("10")) {
                        result += ten;
                        break;
                    } else {
                        if (j == 0) {
                            if (charStr.charAt(j) == '1')
                                result += ten;
                            else if (charStr.charAt(j) == '0')
                                result += "";
                            else
                                result += cnDate[Integer.parseInt(str)] + ten;
                        }
                        if (j == 1) {
                            if (charStr.charAt(j) == '0')
                                result += "";
                            else
                                result += cnDate[Integer.parseInt(str)];
                        }
                    }
                } else {
                    result += cnDate[Integer.parseInt(str)];
                }
            }
            if (i == 0) {
                result += "年";
                continue;
            }
            if (i == 1) {
                result += "月";
                continue;
            }
            if (i == 2) {
                result += "日";
                continue;
            }
        }
        return result;
    }

    /**
     * 字符串小时转中文时间
     * 12 -----十二时
     */
    public static String getHourCn(String hourString) {
        String result = "";
        String[] cnDate = new String[]{"零", "一", "二", "三", "四", "五", "六", "七", "八", "九"};
        String ten = "十";
        if ("午时".equals(hourString)) {
            return hourString;
        }
        char[] chars = hourString.toCharArray();
        for (int i = 0; i < chars.length; i++) {
            String str = String.valueOf(chars[i]);
            char charOne = chars[i];
            if (hourString.equals("10")) {
                result += ten;
                break;
            } else if (hourString.equals("00")) {
                result += "零时";
                break;
            } else {
                if (chars.length == 2) {
                    if (i == 0) {
                        if (charOne == '1') {
                            result += ten;
                        } else if (charOne == '0') {
                            result += "";
                        } else {
                            result += cnDate[Integer.parseInt(str)] + ten;
                        }
                    }
                    if (i == 1) {
                        if (charOne == '0') {
                            result += "";
                        } else {
                            result += cnDate[Integer.parseInt(str)];
                        }
                    }
                } else if (chars.length == 1) {
                    if (charOne == '0') {
                        result += "零";
                    } else {
                        result += cnDate[Integer.parseInt(str)];
                    }
                    result += "时";
                    return result;
                }
            }
            if (i > 0) {
                result += "时";
            }
        }
        return result;
    }


    /**
     * 北京时间转格林威治时间
     * @return
     */
    public static Date UTC8ToGMT(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.HOUR_OF_DAY, -8);
        return cal.getTime();
    }

    /**
     * 字符串北京时间转字符串中文格林威治时间
     * @return
     */
    public static String UTC8ToGMTString(String dateStirng) {
        String result = null;
        try {
            Date date = Common.DateFormat.SDF.parse(dateStirng);
            //转格林威治时间
            Date gDate = UTC8ToGMT(date);
            //格林威治时间转字符串时间
            String gDateString = Common.DateFormat.SDF.format(gDate);
            //转中文时间
            result = getDateCn(gDateString);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * GMT格式字符串
     * 传入：2020-01-01 12
     * 输出：Sun 02 Feb 12:00:00 GMT 1992
     * @param hourString
     * @return
     * @throws ParseException
     */
    public static String getGMTEn(String hourString) {
        //获取时间最后用做判断
        String start = hourString;
        if (hourString.contains("午时")) {
            String[] split = hourString.split(" ");
            hourString = split[0] + " 12:00:00";
        } else {
            hourString += ":00:00";
        }
        String result = null;
        SimpleDateFormat SDF1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = null;
        try {
            date = SDF1.parse(hourString);
            SimpleDateFormat sdf = new SimpleDateFormat("EEE dd MMM HH:mm:ss 'GMT' yyyy", Locale.ENGLISH);
            sdf.setTimeZone(TimeZone.getTimeZone("GMT+8")); // 设置时区为GMT
            result = sdf.format(date.getTime());
        } catch (ParseException e) {
            e.printStackTrace();
        }
        if (start.contains("午时")) {
            result = result.replaceAll("12:00:00", "NOON");
        }

        return result;
    }

    /**
     * GMT格式字符串
     * 传入：2020-01-01 12
     * 输出：Sun 02 Feb 12:00:00 GMT 1992
     */
    public static String getDateEn(String date, String dateType) {
        String dateEn = null;

        String[] split = date.split(" ");
        String[] dateSplit = split[0].split("-");
        String year = dateSplit[0];
        String month = dateSplit[1];
        month = changeNumberToChar(month);
        String day = dateSplit[2];
        //String day = null;
        String hour = "";
        if (split.length > 1) {
            String[] hourSplit = split[1].split("");
            if (null != hourSplit) {
                hour = split[1];
                String hour1 = hourSplit[0];
                if (hour1.equals("0")) {
                    hour = hourSplit[1];
                }
                if (dateType.equals("1")) {
                    hour = date.contains("午时") ? "12:00" + " " : hour + ":00" + " ";
                }
                if (dateType.equals("2")) {
                    hour = date.contains("午时") ? "NOON" + " GMT" + " " : hour + ":00" + " GMT" + " ";
                }
            }
        }
        dateEn = hour + day + " " + month + ".," + year;

        return dateEn;
    }

    public static List<String> MONTH_LIST = Arrays.asList("", "JAN", "FEB", "MAR",
            "APR", "MAY", "JUN", "JUL", "AUG", "SEP", "OCT", "NOV", "DEC");

    public static String changeNumberToChar(String releaseVersion) {

        if (null == releaseVersion) {
            throw new IllegalArgumentException("ReleaseVersion cannot be null");
        }
        String strNumber = releaseVersion.substring(0, 2);
        String strMonth = null;
        try {
            strMonth = MONTH_LIST.get(Integer.parseInt(strNumber));
        } catch (NumberFormatException e) {
            e.printStackTrace();
            System.out.println("Parse month error........");
        }
        return strMonth + releaseVersion.substring(2);
    }

}
