package com.js.hszpt.entity;

import com.js.core.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.Builder;
import java.util.Date;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;


/**
 * 
 * @ClassName:  CtfCertificateAttribute   
 * @Description:TODO(证照照面属性信息表)   
 * @author:   System Generation 
 */
@Data

@TableName("ctf_certificate_attribute")
@ApiModel(value = "证照照面属性信息表")
public class CtfCertificateAttribute extends BaseEntity {

    private static final long serialVersionUID = 1L;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String certificateAttributeId;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String certificateId;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String attributeName;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String attributeColumnName;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String attributeValue;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;



}