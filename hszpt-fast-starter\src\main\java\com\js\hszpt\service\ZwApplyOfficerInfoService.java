package com.js.hszpt.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.hszpt.entity.ZwApplyOfficerInfo;
import com.js.hszpt.mapper.ZwApplyOfficerInfoMapper;
import org.springframework.stereotype.Service;

@Service
public class ZwApplyOfficerInfoService extends ServiceImpl<ZwApplyOfficerInfoMapper, ZwApplyOfficerInfo> {

    public ZwApplyOfficerInfo getZwApplyOfficerInfoByWindowApplyId(String windowApplyId) {
        QueryWrapper<ZwApplyOfficerInfo> queryWrapper = Wrappers.query();
        queryWrapper.lambda()
                .eq(ZwApplyOfficerInfo::getWindowApplyId, windowApplyId);
        return this.getOne(queryWrapper, false);
    }
}
