package com.js.hszpt.api;


import cn.hutool.core.util.StrUtil;
import com.js.common.entity.CurrentUser;
import com.js.hszpt.entity.CertTypeBatchOperation;
import com.js.hszpt.service.CertTypeBatchOperationService;
import com.js.hszpt.service.SysUserService;
import com.js.hszpt.vo.CertTypeApprovalSubmitVO;
import com.js.hszpt.vo.BatchApprovalRequest;
import com.js.hszpt.service.CertTypeApprovalService;
 
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.js.core.api.BaseApiPlus;
import com.js.core.common.utils.ResultUtil;
import com.js.core.common.vo.PageVo;
import com.js.core.common.vo.Result;
import com.js.core.common.vo.SearchVo;
import com.js.annotation.SystemLog;
import com.js.enums.LogType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import java.util.List;


 /**
 * 
 * @ClassName: CertTypeBatchOperationApi  
 * @Description:TODO(批量操作记录表接口)   
 * @author:  System Generation  
 *
 */
@Slf4j
@RestController
@Api(description = "批量操作记录表接口")
@RequestMapping("/certTypeBatchOperation")
public class CertTypeBatchOperationApi extends BaseApiPlus<CertTypeBatchOperationService,CertTypeBatchOperation,String>{

	@Autowired
	private CertTypeApprovalService certTypeApprovalService;

	@Autowired
	private SysUserService sysUserService;

	@SystemLog(description = "批量操作记录表-分页查询", type = LogType.OPERATION)
	@RequestMapping(value = "/getPage", method = RequestMethod.GET)
	@ApiOperation(value = "分页查询")
	public Result<Page<CertTypeBatchOperation>> getPage(@ModelAttribute CertTypeBatchOperation param, @ModelAttribute SearchVo searchVo, @ModelAttribute PageVo pageVo) {
		Page<CertTypeBatchOperation> page = this.baseService.findByCondition(param, searchVo, pageVo);
		return ResultUtil.data(page);
	}
	
	@SystemLog(description = "批量操作记录表-列表查询", type = LogType.OPERATION)
	@RequestMapping(value = "/getList", method = RequestMethod.GET)
	@ApiOperation(value = "列表查询")
	public Result<List<CertTypeBatchOperation>> getList(@ModelAttribute CertTypeBatchOperation param, @ModelAttribute SearchVo searchVo) {
		List<CertTypeBatchOperation> list = this.baseService.findByCondition(param, searchVo);
		return ResultUtil.data(list);
	}
	
	@SystemLog(description = "证照类型目录-批量审批", type = LogType.OPERATION)
	@PostMapping(value = "/batchApproval", consumes = "application/json")
	@ApiOperation(value = "批量审批")
	public Result<Void> batchApproval(@RequestBody @Validated BatchApprovalRequest request) {
		log.info("开始批量审批证照类型目录，参数：{}", request);

		try {
			baseService.processBatchApproval(request.getCertTypeApprApplyIds(), request.getSubmitVO());
			return ResultUtil.success("批量审批处理成功");
		} catch (Exception e) {
			log.error("批量审批处理失败", e);
			return ResultUtil.error("批量审批失败：" + e.getMessage());
		}
	}
}
