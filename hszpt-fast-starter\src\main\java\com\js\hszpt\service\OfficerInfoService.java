package com.js.hszpt.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.hszpt.entity.OfficerInfo;
import com.js.hszpt.mapper.OfficerInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class OfficerInfoService extends ServiceImpl<OfficerInfoMapper, OfficerInfo> {

    public OfficerInfo getOfficerInfoByOrgCode(String orgCode) {
        QueryWrapper<OfficerInfo> queryWrapper = Wrappers.<OfficerInfo>query();
        queryWrapper.lambda()
                .eq(OfficerInfo::getOrgCode,orgCode)
                .last("LIMIT 1");
        return this.getOne(queryWrapper,false);
    }
}
