package com.js.hszpt.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

@Data
@ApiModel("证照类型目录列表返回对象")
public class CertTypeDirectoryListVO {

    @ApiModelProperty("申请编号")
    private String applyNo;

    @ApiModelProperty("申请类型")
    private String applyType;

    @ApiModelProperty("申请类型名称")
    private String applyTypeName;

    @ApiModelProperty("证照类型父级ID")
    private String parentId;

    @ApiModelProperty("发布机构")
    private String publishOrgName;

    @ApiModelProperty("发布时间")
    private Date publishTime;

    @ApiModelProperty("证照类型")
    private String certificateTypeName;

    @ApiModelProperty("证照类型代码")
    private String certificateTypeCode;

    @ApiModelProperty("证照名称")
    private String certificateName;

    @ApiModelProperty("证照定义机构")
    private String createOrgName;

    @ApiModelProperty("证照定义机构代码")
    private String createOrgCode;

    @ApiModelProperty("关联事项名称")
    private String relatedItemName;

    @ApiModelProperty("关联事项代码")
    private String relatedItemCode;

    @ApiModelProperty("持证主体类别")
    private String certificateHolderCategory;

    @ApiModelProperty("持证主体类别名称")
    private String certificateHolderCategoryName;

    @ApiModelProperty("有效期限")
    private String validityRange;

    @ApiModelProperty("审批状态")
    private String approvalStatus;

    @ApiModelProperty("审批状态名称")
    private String approvalStatusName;

    @ApiModelProperty("审批时间")
    private Date approvalTime;

    @ApiModelProperty("下发状态")
    private String issueStatus;

    @ApiModelProperty("下发状态名称")
    private String issueStatusName;

    @ApiModelProperty("下发日期")
    private Date issueDate;

    @ApiModelProperty("受理机构")
    private String acceptOrgName;

    @ApiModelProperty("受理日期")
    private Date acceptTime;

    @ApiModelProperty("审批机构")
    private String approvalOrgName;

    @ApiModelProperty("创建人员")
    private String createBy;

    @ApiModelProperty("创建日期")
    private Date createTime;

    @ApiModelProperty("更新人员")
    private String updateBy;

    @ApiModelProperty("更新日期")
    private Date updateTime;

    @ApiModelProperty(value = "当前操作节点", hidden = true)
    private String currentNode;

    @ApiModelProperty(value = "证照类型目录主键ID", hidden = true)
    private String certTypeDirId;

    @ApiModelProperty(value = "证照类型目录审批申请表主键ID", hidden = true)
    private String certApprApplyId;

    @ApiModelProperty(value = "证照类型目录审核审批表主键ID", hidden = true)
    private String certTypeApprovalId;

    @ApiModelProperty("状态：1-待审核 2-审核通过 3-审核不通过 4-审核退回 5-撤回 6-待审批 7-审批通过 8-审批不通过 9-审批退回")
    private String status;

    @ApiModelProperty("当前操作人名称")
    private String operatorName;

    @ApiModelProperty("当前处理人名称")
    private String currentHandlerName;

    @ApiModelProperty("操作按钮权限")
    private ButtonPermissionVO buttonPermission;
}
