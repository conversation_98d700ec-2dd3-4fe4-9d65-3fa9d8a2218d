package com.js.hszpt.service;

import com.js.common.entity.CurrentUser;
import com.js.core.common.utils.ResultUtil;
import com.js.hszpt.entity.CertTypeApprovalApply;
import com.js.hszpt.entity.SysDeptIamb;
import com.js.hszpt.mapper.CertTypeBatchOperationDao;
import com.js.hszpt.entity.CertTypeBatchOperation;
import com.js.hszpt.entity.CertTypeBatchOperationDetail;
import com.js.hszpt.service.CertTypeApprovalService;
import com.js.hszpt.vo.CertTypeApprovalSubmitVO;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.core.common.utils.PageUtil;
import com.js.core.common.vo.PageVo;
import com.js.core.common.vo.SearchVo;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 
 * @ClassName:  CertTypeBatchOperationService    
 * @Description:TODO(批量操作记录表接口实现)   
 * @author:  System Generation
 *
 */
@Slf4j
@Service
public class CertTypeBatchOperationService extends ServiceImpl<CertTypeBatchOperationDao, CertTypeBatchOperation> {

	@Autowired
	private CertTypeApprovalService certTypeApprovalService;
	
	@Autowired
	private CertTypeBatchOperationDetailService batchOperationDetailService;
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private SysDeptIambService sysDeptIambService;
    @Autowired
    private CertTypeDirectoryService certTypeDirectoryService;
    @Autowired
    private CertTypeApprovalApplyService certTypeApprovalApplyService;

	/**
	 * 多条件分页查询
	 * @param param
	 * @param searchVo
	 * @param pageVo
	 * @return
	 */
	public Page<CertTypeBatchOperation> findByCondition(CertTypeBatchOperation param, SearchVo searchVo, PageVo pageVo) {
		Page<CertTypeBatchOperation> page = PageUtil.initMpPage(pageVo);
		QueryWrapper<CertTypeBatchOperation> queryWrapper = this.getCondition(param,searchVo);
		return this.page(page, queryWrapper);
	}
	
	/**
	 * 
	 * @Title: findByCondition   
	 * @Description: TODO(多条件列表查询)   
	 * @param param
	 * @param searchVo
	 * @return  List<CertTypeBatchOperation>      
	 * @throws
	 */
	public List<CertTypeBatchOperation> findByCondition(CertTypeBatchOperation param, SearchVo searchVo){
		QueryWrapper<CertTypeBatchOperation> queryWrapper = this.getCondition(param, searchVo);
		//添加排序字段
		queryWrapper.orderByAsc("create_time");
		
		return this.list(queryWrapper);
	}
	
	
	/**
	 * 
	 * @Title: getCondition   
	 * @Description: TODO(构建自定义查询条件)   
	 * @param param
	 * @param searchVo
	 * @return  QueryWrapper<CertTypeBatchOperation>      
	 * @throws
	 */
	private QueryWrapper<CertTypeBatchOperation> getCondition(CertTypeBatchOperation param, SearchVo searchVo){
		QueryWrapper<CertTypeBatchOperation> queryWrapper = new QueryWrapper<CertTypeBatchOperation>();
		//请根据实际字段自行扩展
		 
		// 创建时间
		if (StrUtil.isNotBlank(searchVo.getStartDate()) && StrUtil.isNotBlank(searchVo.getEndDate())) {
			Date start = DateUtil.parse(searchVo.getStartDate());
			Date end = DateUtil.parse(searchVo.getEndDate());
			queryWrapper.between("create_time",start, DateUtil.endOfDay(end));
		}
		return queryWrapper;
		
	}

	public void processBatchApproval(List<String> certTypeApprApplyIds, CertTypeApprovalSubmitVO submitVO) {
		log.info("开始处理批量审批，目录ID列表：{}，审批信息：{}", certTypeApprApplyIds, submitVO);

		// 1. 创建批量操作记录
		CertTypeBatchOperation batchOperation = new CertTypeBatchOperation();
		batchOperation.setOperationType(submitVO.getApprovalResult());
		batchOperation.setRecordCount(certTypeApprApplyIds.size());
		// 批量操作结果：0-处理中，1-全部成功，2-部分失败
		batchOperation.setOperationResult("0");
		batchOperation.setSuccessCount(0);
		batchOperation.setFailCount(0);
		batchOperation.setDelFlag("0");
		this.save(batchOperation);

		//todo 通过智慧海事接口获取
		CurrentUser user = sysUserService.getCurrUser();
		String currentUserId = user.getId();
		String currentUserName = user.getUsername();
		String currentOrgCode = sysUserService.getCurrUserOrgCode();

		SysDeptIamb dept = sysDeptIambService.getByOrgCode(currentOrgCode);
		if (null == dept) {
			throw new RuntimeException("获取用户机构信息失败");
		}

		String currentOrgName = dept.getName();
		String orgLevel = dept.getGovLevel();

		log.info("当前用户信息: 用户ID={}, 用户名={}, 机构编码={}, 机构名称={}, 机构级别={}",
				currentUserId, currentUserName, currentOrgCode, currentOrgName, orgLevel);

		// 设置操作用户信息
		submitVO.setOperatorId(currentUserId);
		submitVO.setOperatorName(currentUserName);
		submitVO.setOrgCode(currentOrgCode);
		submitVO.setOrgName(currentOrgName);
		submitVO.setOrgLevel(orgLevel);
		// 获取对应的办件记录
		List<CertTypeApprovalApply> certTypeApprovalApplyList = certTypeApprovalApplyService.listByIds(certTypeApprApplyIds);
		// 2. 遍历处理每条记录
		certTypeApprovalApplyList.forEach(certTypeApprovalApply -> {
			CertTypeBatchOperationDetail detail = new CertTypeBatchOperationDetail();
			detail.setBatchId(batchOperation.getId());
			detail.setCertTypeDirId(certTypeApprovalApply.getCertTypeDirId());
			detail.setOperationTime(new Date());

			try {
				// 复用单条审批处理逻辑
				submitVO.setCertTypeApprApplyId(certTypeApprovalApply.getId());
				certTypeApprovalService.submitApproval(submitVO);

				// 记录成功
				detail.setOperationResult("1");
				batchOperation.setSuccessCount(batchOperation.getSuccessCount() + 1);
			} catch (Exception e) {
				// 记录失败
				detail.setOperationResult("2");
				detail.setFailReason(e.getMessage());
				batchOperation.setFailCount(batchOperation.getFailCount() + 1);
				log.error("处理证照类型目录审批失败，ID：{}，原因：{}", certTypeApprovalApply.getCertTypeDirId(), e.getMessage());
			}

			// 保存明细记录
			batchOperationDetailService.save(detail);
		});
		// 3. 更新批量操作记录的结果
		batchOperation.setOperationResult(batchOperation.getFailCount() == 0 ? "1" : "2");
		batchOperation.setRemarks(String.format("总计：%d，成功：%d，失败：%d", 
			batchOperation.getRecordCount(),
			batchOperation.getSuccessCount(),
			batchOperation.getFailCount()));
		this.updateById(batchOperation);

		log.info("批量审批处理完成，结果：{}", batchOperation.getRemarks());
	}
}