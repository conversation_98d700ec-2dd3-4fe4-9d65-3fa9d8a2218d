package com.js.hszpt.entity;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * date 2022/9/7
 * @version 1.0
 */
@Data
public class SysDeptInfoResponse {


    private String deptId;
    /**
     * 部门名称
     */
    private String name;

    /**
     * 排序
     */
    private Long sort;

    /**
     * 是否删除  -1：已删除  0：正常
     */
    private String delFlag;

    /**
     * 父级id
     */
    private String parentId;

    /**
     * 层级
     */
    private String govLevel;

    /**
     * 编码
     */
    private String code;

    /**
     * 是否展示 1是 2否
     */
    private String isShow;

    /**
     * 大厅展示 1
     */
    private String hallShow;

    /**
     * 统一社会信用代码
     */
    private String creditCode;

    /**
     * 办理时间
     */
    private String handleDate;

    /**
     * 办理地点
     */
    private String approveAddress;

    /**
     * 咨询方式
     */
    private String consultType;

    /**
     * 监督投诉方式
     */
    private String superviseType;

    /**
     * 是否中心1:是
     */
    private String isCenter;

    /**
     * 部门办公联系电话
     */
    private String deptPhone;

    /**
     * 实施编码
     */
    private String taskCode;

    /**
     * parent_code
     */
    private String parentCode;

    /**
     * dept_type
     */
    private String deptType;

    /**
     * 首字母缩写
     */
    private String nameAbbr;

    /**
     * 机构标识代码类
     */
    private String codesetId;

    /**
     * 0:虚拟组织 1:真实组织
     */
    private String orgTag;

    /**
     * 组织机构简称
     */
    private String orgCcname;

    /**
     * 组织机构描述
     */
    private String orgDesc;

    /**
     * 组织机构代码
     */
    private String orgCertifit;

    /**
     * 机构类型
     */
    private String zsOrgType;

    /**
     * 老机构id
     */
    private String oldDeptId;

    /**
     * 区域编码唯一标识
     */
    private String areaId;

    /**
     * 4a机构层级
     */
    private String grade;

    private Boolean isMore;

    /**
     * 节点树Id
     */
    private String treeId;

    private List<SysDeptInfoResponse> children;

}
