package com.js.hszpt.dto;

import lombok.Data;
import java.util.List;

/**
 * 外部证照查询参数
 */
@Data
public class CertificateExternalQueryDto {
    private String id;
    //事项名称
    private String affairName;
    //申请编号
    private String applyNo;
    //申请人/申请单位
    private String applicantName;
    //申请日期
    private String applyDateStart;
    private String applyDateEnd;
    //受理日期
    private String acceptDateStart;
    private String acceptDateEnd;
    //审批日期
    private String apprDateStart;
    private String apprDateEnd;
    //证照开始时间
    private String certStartDate;
    //证照结束时间
    private String certEndDate;
    //船舶名称
    private String shipName;
    //持证人姓名（必填）
    private String holderName;
    // 持有人身份标识号码（必填）
    private String holderIdentityNumber;
    //签发机关
    private List<String> issuOrgCode3;
    //当前登录用户机构
    private String onlineDepOrg;
    // 证书名称
    private String certificateName;
    // 证书名称列表
    private List<String> certificateNames;
    // 证书类型
    private String certificateType;
    //证书编号
    private String certificateNum;
    //截止日期
    private String expireDateStart;
    private String expireDateEnd;
    //证书状态（0-全部；1-有效； 2-无效）
    private String statusFlag;
    //IMO船舶识别号
    private String shipImo;
    //船舶编号或呼号
    private String shipCallSign;
    // 证照类型
    private List<String> certTypes;

    //船舶名称集合，逗号拼接
    private String shipNames;

    private String displaySource;
}
