package com.js.hszpt.job;

import cn.hutool.core.io.FileUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.js.hszpt.enmus.DataStorageTaskStatus;
import com.js.hszpt.entity.DataStorageTask;

import com.js.hszpt.service.DataStorageTaskService;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

@Slf4j
@Component
public class DataStorageJob {

    @Value("${job.dataStorage.enabled:false}")
    private boolean enable;

    @Autowired
    private DataStorageTaskService dataStorageTaskService;


    /**
     * 定时任务，读取状态为1的文件保存到数据库种
     */
    @Scheduled(cron = "${job.dataStorage.bizDgrOpic.cron}")
    public void dataStorageBizDgrOpic() {
        dataStorageByTaskName("biz_dgr_opic");
    }

    @Scheduled(cron = "${job.dataStorage.bizEgCertCheck.cron}")
    public void dataStorageBizEgCertCheck() {
        dataStorageByTaskName("biz_eg_cert_check");
    }

    @Scheduled(cron = "${job.dataStorage.bizEgSendCert.cron}")
    public void dataStorageBizEgSendCert() {
        dataStorageByTaskName("biz_eg_send_cert");
    }

    @Scheduled(cron = "${job.dataStorage.bizAffairInsurance.cron}")
    public void dataStorageBizAffairInsurance() {
        dataStorageByTaskName("biz_affair_insurance");
    }

    @Scheduled(cron = "${job.dataStorage.bizAffairApply.cron}")
    public void dataStorageBizAffairApply() {
        dataStorageByTaskName("biz_affair_apply");
    }

    @Scheduled(cron = "${job.dataStorage.bizEgApprove.cron}")
    public void dataStorageBizEgApprove() {
        dataStorageByTaskName("biz_eg_approve");
    }

    @Scheduled(cron = "${job.dataStorage.dictEgOrg.cron}")
    public void dataStorageDictEgOrg() {
        dataStorageByTaskName("dict_eg_org");
    }

    @Scheduled(cron = "${job.dataStorage.dictEgPort.cron}")
    public void dataStorageDictEgPort() {
        dataStorageByTaskName("dict_eg_port");
    }

    @Scheduled(cron = "${job.dataStorage.dictEgPublic.cron}")
    public void dataStorageDictEgPublic() {
        dataStorageByTaskName("dict_eg_public");
    }

    @Scheduled(cron = "${job.dataStorage.dictEgUser.cron}")
    public void dataStorageDictEgUser() {
        dataStorageByTaskName("dict_eg_user");
    }

    @Scheduled(cron = "${job.dataStorage.DictYthOrgMapping.cron}")
    public void dataStorageDictYthOrgMapping() {
        dataStorageByTaskName("dict_yth_org_mapping");
    }
    @Scheduled(cron = "${job.dataStorage.bizEgApplyInfo.cron}")
    public void dataStorageBizEgApplyInfo() {
        dataStorageByTaskName("biz_eg_apply_info");
    }
    @Scheduled(cron = "${job.dataStorage.certShipOwnership.cron}")
    public void dataStorageCertShipOwnership() {
        dataStorageByTaskName("cert_ship_ownership");
    }
    @Scheduled(cron = "${job.dataStorage.shipInfoAll.cron}")
    public void dataStorageShipInfoAll() {
        dataStorageByTaskName("ship_info_all");
    }

    // 新增 biz_eg_accept 定时任务
//    @Scheduled(cron = "${job.dataStorage.bizEgAccept.cron}")
    @Scheduled(cron = "${job.dataStorage.bizEgAccept.cron}")
    public void dataStorageBizEgAccept() {
        dataStorageByTaskName("biz_eg_accept");
    }

    @Scheduled(cron = "${job.dataStorage.officerInfo.cron}")
    public void dataStorageOfficerInfo() {
        dataStorageByTaskName("officer_info");
    }

    @Scheduled(cron = "${job.dataStorage.applyOfficerInfo.cron}")
    public void dataStorageApplyOfficerInfo() {
        dataStorageByTaskName("apply_officer_info");
    }

    @Scheduled(cron = "${job.dataStorage.ctfCertificateDataRegen.cron}")
    public void dataStorageCtfCertificateDataRegen() {
        dataStorageByTaskName("ctf_certificate_data_regen");
    }

    public void dataStorageByTaskName(String taskName) {
        if (!enable) {
            return;
        }
        log.info("---------------------{}定时任务**数据存储**开始执行:{}------------------------------", taskName,LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        long start = System.currentTimeMillis();
        List<DataStorageTask> list = dataStorageTaskService
                .list(new LambdaQueryWrapper<DataStorageTask>()
                        .eq(DataStorageTask::getStorageStatus, DataStorageTaskStatus.RECEIVED.getCode())
                        .eq(DataStorageTask::getTaskName, taskName));
        for (DataStorageTask dataStorageTask : list) {
            //批量保存
            String targetFolderPath = dataStorageTask.getTargetFolderPath();
            // 获取文件名称类型
            List<String> fileNames = FileUtil.listFileNames(targetFolderPath);
            long count = fileNames.stream().filter(m -> m.contains(".temp")).count();
            boolean result = true;
            if (fileNames.isEmpty()) {//判断未见是否为空
                log.error("{}文件夹内没有需要待读取的文件", targetFolderPath);
            } else if (count > 0) {//含有 temp文件
                log.error("{}文件夹内含有{}个.temp文件", targetFolderPath, count);
            } else {
                log.info("{}文件夹内含有{}个正式文件", targetFolderPath, fileNames.size());
                boolean b = dataStorageTaskService.taskDataStorage(dataStorageTask.getTaskName(), targetFolderPath);
                if (b) {//保存无异常，删除文件夹
                    File file = FileUtil.file(targetFolderPath);
                    if (FileUtil.isDirEmpty(file)) {//空文件,删除
                        if (FileUtil.del(targetFolderPath)) {//删除文件成功
                            log.info("新增{}任务数据成功，已删除文件：{}", dataStorageTask.getTaskName(), targetFolderPath);
                        } else {
                            log.error("-------{}文件删除失败-------", targetFolderPath);
                            result = false;
                        }
                    } else {
                        log.error("-------{}文件夹内还有文件未删除，请查检查是否数据保存异常-------", targetFolderPath);
                        result = false;
                    }
                } else {
                    log.error("-------{}任务写入数据库失败-------", dataStorageTask.getTaskName());
                }
            }
            dataStorageTask.setStorageStatus(result ? DataStorageTaskStatus.SAVE_SUCCESS.getCode() : DataStorageTaskStatus.SAVE_FAIL.getCode());
            dataStorageTask.setModityDate(new Date());
            dataStorageTaskService.saveOrUpdate(dataStorageTask);
        }
        log.info("{}定时任务**数据存储**结束执行,耗时:{}ms", taskName, System.currentTimeMillis() - start);
    }
}
