package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 电子证照信息汇聚处理日志表
 */
@Data
@TableName("ctf_certificate_data_regen_log")
public class CertificateDataRegenLog {

    /**
     * 证照处理日志主键
     */
    @TableId
    private String id;

    /**
     * 证照数据主键
     */
    private String dataId;

    /**
     * 证照id
     */
    private String certificateId;

    /**
     * 证照目录名称
     */
    private String catalogName;

    /**
     * 请求报文
     */
    private String requestData;

    /**
     * 响应报文
     */
    private String responseData;

    /**
     * 创建时间
     */
    private String createTime;
} 