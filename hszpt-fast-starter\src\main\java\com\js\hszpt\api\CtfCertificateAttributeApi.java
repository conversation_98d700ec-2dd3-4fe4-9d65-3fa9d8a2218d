package com.js.hszpt.api;


import com.js.hszpt.entity.CtfCertificateAttribute;
import com.js.hszpt.service.CtfCertificateAttributeService;
 
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.js.core.api.BaseApiPlus;
import com.js.core.common.utils.ResultUtil;
import com.js.core.common.vo.PageVo;
import com.js.core.common.vo.Result;
import com.js.core.common.vo.SearchVo;
import com.js.annotation.SystemLog;
import com.js.enums.LogType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import java.util.List;


 /**
 * 
 * @ClassName: CtfCertificateAttributeApi  
 * @Description:TODO(证照照面属性信息表接口)   
 * @author:  System Generation  
 *
 */
@Slf4j
@RestController
@Api(description = "证照照面属性信息表接口")
@RequestMapping("/ctfCertificateAttribute")
public class CtfCertificateAttributeApi extends BaseApiPlus<CtfCertificateAttributeService,CtfCertificateAttribute,String>{

	@SystemLog(description = "证照照面属性信息表-分页查询", type = LogType.OPERATION)
	@RequestMapping(value = "/getPage", method = RequestMethod.GET)
	@ApiOperation(value = "分页查询")
	public Result<Page<CtfCertificateAttribute>> getPage(@ModelAttribute CtfCertificateAttribute param, @ModelAttribute SearchVo searchVo, @ModelAttribute PageVo pageVo) {
		Page<CtfCertificateAttribute> page = this.baseService.findByCondition(param, searchVo, pageVo);
		return ResultUtil.data(page);
	}
	
	@SystemLog(description = "证照照面属性信息表-列表查询", type = LogType.OPERATION)
	@RequestMapping(value = "/getList", method = RequestMethod.GET)
	@ApiOperation(value = "列表查询")
	public Result<List<CtfCertificateAttribute>> getList(@ModelAttribute CtfCertificateAttribute param, @ModelAttribute SearchVo searchVo) {
		List<CtfCertificateAttribute> list = this.baseService.findByCondition(param, searchVo);
		return ResultUtil.data(list);
	}
	
}
