package com.js.hszpt.enums;

import lombok.Getter;

/**
 * 证照重生成状态枚举
 */
@Getter
public enum CertificateRegenStatus {
    
    UN_CREATE("0", "未创建"),
    SUCCESS("1", "成功"),
    FAILED("2", "失败");
    
    private final String code;
    private final String desc;
    
    CertificateRegenStatus(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    /**
     * 根据状态码获取枚举
     * @param code 状态码
     * @return 枚举
     */
    public static CertificateRegenStatus getByCode(String code) {
        for (CertificateRegenStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return UN_CREATE; // 默认返回未创建状态
    }
} 