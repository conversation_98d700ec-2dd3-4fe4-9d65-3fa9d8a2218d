package com.js.hszpt.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.js.api.entity.User;
import com.js.common.entity.CurrentUser;
import com.js.common.service.SecurityService;
import com.js.hszpt.dto.SysUserDto;
import com.js.hszpt.mapper.SysUserDao;
import com.js.hszpt.entity.SysUser;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.core.common.utils.PageUtil;
import com.js.core.common.vo.PageVo;
import com.js.core.common.vo.SearchVo;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import java.util.Date;
import java.util.List;

import com.js.hszpt.vo.SysUserVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import org.springframework.data.redis.core.RedisTemplate;

/**
 *
 * @ClassName:  SysUserService
 * @Description:TODO(用户表接口实现)
 * @author:  System Generation
 *
 */
@Slf4j
@Service
public class SysUserService extends ServiceImpl<SysUserDao,SysUser> {
	@Autowired
	private SysUserDao sysUserDao;

	@Autowired
	private SecurityService securityService;

	@Value("${spring.profiles.active:dev}")
	private String activeProfile;

	@Autowired
	private RedisTemplate<String, Object> redisTemplate;

	private static final String REDIS_KEY_PREFIX = "zhhs_auth:";

	/**
	 * 多条件分页查询
	 * @param param
	 * @param searchVo
	 * @param pageVo
	 * @return
	 */
	public Page<SysUser> findByCondition(SysUser param, SearchVo searchVo, PageVo pageVo) {
		Page<SysUser> page = PageUtil.initMpPage(pageVo);
		QueryWrapper<SysUser> queryWrapper = this.getCondition(param,searchVo);
		return this.page(page, queryWrapper);
	}

	/**
	 *
	 * @Title: findByCondition
	 * @Description: TODO(多条件列表查询)
	 * @param param
	 * @param searchVo
	 * @return  List<SysUser>
	 * @throws
	 */
	public List<SysUser> findByCondition(SysUser param, SearchVo searchVo){
		QueryWrapper<SysUser> queryWrapper = this.getCondition(param, searchVo);
		//添加排序字段
		queryWrapper.orderByAsc("create_time");

		return this.list(queryWrapper);
	}


	/**
	 *
	 * @Title: getCondition
	 * @Description: TODO(构建自定义查询条件)
	 * @param param
	 * @param searchVo
	 * @return  QueryWrapper<SysUser>
	 * @throws
	 */
	private QueryWrapper<SysUser> getCondition(SysUser param, SearchVo searchVo){
		QueryWrapper<SysUser> queryWrapper = new QueryWrapper<SysUser>();
		//请根据实际字段自行扩展

		// 创建时间
		if (StrUtil.isNotBlank(searchVo.getStartDate()) && StrUtil.isNotBlank(searchVo.getEndDate())) {
			Date start = DateUtil.parse(searchVo.getStartDate());
			Date end = DateUtil.parse(searchVo.getEndDate());
			queryWrapper.between("create_time",start, DateUtil.endOfDay(end));
		}
		return queryWrapper;

	}


	public IPage<SysUserVo> selectUserListForWorkflow(SysUserDto sysUserDto) {

		// 1. 参数校验
		if (sysUserDto == null) {
			throw new IllegalArgumentException("参数不能为空");
		}

		// 2. 初始化分页参数
		Page<SysUserVo> page = new Page<>(
				sysUserDto.getCurrentPage() != null ? sysUserDto.getCurrentPage() : 1,
				sysUserDto.getPageSize() != null ? sysUserDto.getPageSize() : 10
		);

		return sysUserDao.selectUserListForWorkflow(page,sysUserDto);
	}


	public String getCurrUsername(){
		CurrentUser currUser = this.getCurrUser();
		return currUser == null ? null : currUser.getUsername();
	}

	public String getCurrUserId(){
		CurrentUser currUser = this.getCurrUser();
		return currUser == null ? null : currUser.getId();
	}

	public String getCurrOrgCode(){
		CurrentUser currUser = this.getCurrUser();
		return currUser == null ? null : currUser.getDataOrgId();
	}
	/**
	 * 获取当前登录用户
	 * 在local和dev环境使用securityService，
	 * 在uat和prod环境优先使用智慧海事用户信息
	 * @return 当前用户信息
	 */
	public CurrentUser getCurrUser() {
		// 在开发环境中使用原有securityService获取用户
		if (StrUtil.equalsAny(activeProfile, "dev", "local")) {
			log.info("开发环境使用securityService获取用户");
			return securityService.getCurrUser();
		}
		
		// 在生产和UAT环境使用智慧海事用户信息
		try {
			// 获取当前请求
			ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
			if (attributes != null) {
				HttpServletRequest request = attributes.getRequest();
				
				// 从请求头获取智慧海事token
				String authToken = request.getHeader("authorization-zhhs");
				if (StrUtil.isNotEmpty(authToken)) {
					log.info("从请求头获取智慧海事token: {}", authToken);
					
					// 使用token从Redis获取用户信息
					String redisKey = REDIS_KEY_PREFIX + authToken;
					CurrentUser redisUser = (CurrentUser) redisTemplate.opsForValue().get(redisKey);
					if (redisUser != null) {
						log.info("从Redis获取智慧海事用户信息成功: {}，机构编码：{}，部门编码：{}",
								redisUser.getNickName(), redisUser.getDataOrgId(), redisUser.getDepartmentCode());
						return redisUser;
					} else {
						log.warn("Redis中未找到key: {}", redisKey);
					}
				} else {
					log.warn("请求头中未找到智慧海事token");
				}
			}
			
			// 如果无法从Redis获取智慧海事用户信息，回退到securityService
			log.warn("无法从Redis获取智慧海事用户信息.");
			return null;
		} catch (Exception e) {
			log.error("获取当前用户信息异常", e);
			return null;
		}
	}

	/**
	 * 获取当前登录用户机构编码
	 * 优先使用智慧海事用户信息（在uat和prod环境）
	 * @return 机构编码
	 */
	public String getCurrUserOrgCode(){
		// 获取当前用户(已经包含了环境判断逻辑)
		CurrentUser user = this.getCurrUser();
		return user != null ? user.getDataOrgId() : null;
	}


	//查询获取海事机构名称
	public String selectDeptId(String orgCode) {
		return  sysUserDao.selectDeptId(orgCode);
	}

	//查询部门编码
	public String selectDutyCode(String certNo) {
		return sysUserDao.selectDutyCode(certNo);
	}
}