package com.js.hszpt.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.hszpt.entity.BizEgApprove;
import com.js.hszpt.mapper.BizEgApproveMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.sql.Wrapper;

@Service
@Slf4j
public class BizEgApproveService extends ServiceImpl<BizEgApproveMapper, BizEgApprove> {

    public BizEgApprove getBizEgApproveByApplyId(String applyId) {
        QueryWrapper<BizEgApprove> queryWrapper = Wrappers.<BizEgApprove>query();
        queryWrapper.lambda()
                .eq(BizEgApprove::getApplyId, applyId)
                .last("LIMIT 1");
        return baseMapper.selectOne(queryWrapper);
    }
}
