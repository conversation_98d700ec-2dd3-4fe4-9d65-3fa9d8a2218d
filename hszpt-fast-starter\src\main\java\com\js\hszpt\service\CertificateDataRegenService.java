package com.js.hszpt.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.js.hszpt.enmus.*;
import com.js.hszpt.entity.Certificate;
import com.js.hszpt.entity.CertificateAttribute;
import com.js.hszpt.enums.CertificateRegenStatus;
import com.js.hszpt.properties.UrlConfig;
import com.js.hszpt.utils.encrypt.Sm4Tool;
import com.js.hszpt.vo.CertificateData;
import com.js.hszpt.utils.CertificateUtil;
import com.js.hszpt.vo.Surface;
import lombok.Data;import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.js.hszpt.entity.CertificateDataRegen;
import com.js.hszpt.mapper.CertificateDataRegenMapper;
import com.js.hszpt.entity.CertificateType;
import com.js.hszpt.mapper.CertificateTypeMapper;
import com.js.hszpt.entity.CertificateDataRegenLog;
import com.js.hszpt.mapper.CertificateDataRegenLogMapper;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;
import com.js.hszpt.vo.CrewNotificationRequest;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;import java.time.LocalDate;import java.time.format.DateTimeFormatter;import java.time.format.DateTimeParseException;import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CertificateDataRegenService extends ServiceImpl<CertificateDataRegenMapper, CertificateDataRegen> {

    @Autowired
    private ObjectMapper objectMapper;

    @Value("${qrcode.scanUrl}")
    private String scanUrl;

    @Value("${certificate.downloadPhotoUrl}")
    private String downloadPhotoUrl;
    @Value("${certificate.downloadPhotoKey}")
    private String downloadPhotoKey;

    @Autowired
    private CertificateUtil certificateUtil;

    @Autowired
    private UrlConfig urlConfig;

    @Autowired
    private CertificateTypeMapper certificateTypeMapper;

    @Autowired
    private CertificateService certificateService;

    @Autowired
    private CertificateAttributeService certificateAttributeService;

    @Autowired
    private DictYthOrgMappingService dictYthOrgMappingService;

    @Value("${spring.profiles.active:prod}")
    private String active;

    @Autowired
    private CertificateDataRegenLogMapper certificateDataRegenLogMapper;

    @Value("${certificate.qz_type}")
    private String[] qzTypeValues;

    @Value("${crew.system.notification-url}")
    private String crewSystemNotificationUrl;

    @Autowired
    @Qualifier("defaultRestTemplate")
    private RestTemplate restTemplate;

    private static final List<String> CERTIFICATE_NAMES = Arrays.asList(
        "内河船员培训许可证",
        "海船船员培训合格证书",
        "海船普通船员适任证书",
        "海船高级船员适任证书",
        "不参加航行和轮机值班海船船员适任证书",
        "引航员船员适任证书",
        "公务船船员适任证书",
        "海上非自航船舶船员适任证书",
        "小型海船适任证书",
        "船上厨师培训合格证明",
        "船上膳食服务辅助人员培训证明",
        "海船船员健康证明",
        "内河船舶船员适任证书",
        "内河船舶船员特殊培训合格证",
        "游艇驾驶证（海上）",
        "游艇驾驶证（内河）",
        "海船船员内河航线行驶资格证明",
        "特定航线江海直达船舶船员行驶资格证明培训合格证",
        "海上设施工作人员海上交通安全技能培训合格证明",
        "海船船员适任证书承认签证",
        "船员培训质量管理体系证书",
        "海员外派机构资质证书",
        "海船船员培训许可证",
        "内河船舶船员培训合格证",
        "游艇驾驶证内河",
        "游艇驾驶证海上",
        "海船船员培训合格证承认签证",
        "海船船员特免证明",
        "中华人民共和国船舶国籍证书_中文",
        "中华人民共和国船舶国籍证书_中英文",
        "中华人民共和国临时船舶国籍证书_中文",
        "中华人民共和国临时船舶国籍证书_中英文"
    );

    private static final List<String> CERTIFICATE_CODE_ORDER = Arrays.asList(
        "prefix1", "titleOftheCertificate1", "titleOftheCertificate2", "level1", "dateOfIssue1", "dateOfIssue2", "dateOfExpiry1", "dateOfExpiry2",
        "prefix2", "titleOftheCertificate3", "titleOftheCertificate4", "level2", "dateOfIssue3", "dateOfIssue4", "dateOfExpiry3", "dateOfExpiry4",
        "prefix3", "titleOftheCertificate5", "titleOftheCertificate6", "level3", "dateOfIssue5", "dateOfIssue6", "dateOfExpiry5", "dateOfExpiry6"
    );

    /**
     * 对海船船员培训合格证书的合格证书组进行排序
     * 每组包括：prefixN、titleOftheCertificate{2N-1}、titleOftheCertificate{2N}、levelN、dateOfIssue{2N-1}、dateOfIssue{2N}、dateOfExpiry{2N-1}、dateOfExpiry{2N}
     * 按照PREFIX_ORDER顺序排序，返回排序后的字段列表
     * @param surfaces 原始照面字段列表
     * @return 排序后的照面字段列表
     */
    private static final List<String> PREFIX_ORDER = Arrays.asList(
        "Z01", "Z02", "Z03", "Z04", "Z05", "Z06", "Z07", "Z08", "Z09",
        "T01", "T02", "T03", "T04", "T05", "T06", "T06-1", "T06-2", "T06-3",
        "T07", "T08-1", "T08-2", "T08-3", "T08-4", "T08-5", "T09", "T10",
        "T11", "T12", "T13", "T14"
    );

    public List<CertificateDataRegen> getUnCheckCertificate() {
        List<CertificateDataRegen> priorityList = this.list(Wrappers.<CertificateDataRegen>query()
                .lambda()
                .and(wrapper -> wrapper
                        .eq(CertificateDataRegen::getDataType, "2")
                        // 照片正常证书未生成状态为3
                        .eq(CertificateDataRegen::getCreateStatus, "0")
                )
                .or()
                .eq(CertificateDataRegen::getCreateStatus, "6")
                .orderByDesc(CertificateDataRegen::getCreateTime)
                .last("LIMIT 1000"));
        log.info("【检查照片】查询优先检查的记录数量：{}", priorityList.size());
        if (priorityList.size() < 1000) {
            int remainingLimit = 1000 - priorityList.size();
            List<CertificateDataRegen> normalList = list(new LambdaQueryWrapper<CertificateDataRegen>()
                    .eq(CertificateDataRegen::getCreateStatus, CertificateRegenStatus.UN_CREATE.getCode())
                    .orderByDesc(CertificateDataRegen::getCreateTime)
                    .last("LIMIT " + remainingLimit));
            log.info("【检查照片】补充查询状态为0的记录数量：{}", normalList.size());

            // 合并两个列表
            priorityList.addAll(normalList);
        }
        return priorityList;
    }

    public List<CertificateDataRegen> getUnCreatedCertificates() {
        // 首先查询状态为6的记录（优先处理）
//        List<CertificateDataRegen> priorityList = list(new LambdaQueryWrapper<CertificateDataRegen>()
////                .eq(CertificateDataRegen::getCreateStatus, "4")
//                .eq(CertificateDataRegen::getCreateStatus, "6")
//                .orderByDesc(CertificateDataRegen::getCreateTime)
//                .last("LIMIT 1000")
//        );

        List<CertificateDataRegen> priorityList = this.list(Wrappers.<CertificateDataRegen>query()
                .lambda()
                .and(wrapper -> wrapper
                        .eq(CertificateDataRegen::getDataType, "2")
                        // 照片正常证书未生成状态为3
                        .eq(CertificateDataRegen::getCreateStatus, "0")
                )
                .or()
                .eq(CertificateDataRegen::getCreateStatus, "6")
                .orderByDesc(CertificateDataRegen::getCreateTime)
                .last("LIMIT 1000"));

        log.info("【证照重新生成】查询状态为6的记录数量：{}", priorityList.size());

        // 如果优先处理的记录不足500条，补充查询状态为0的记录
        if (priorityList.size() < 1000) {
            int remainingLimit = 1000 - priorityList.size();
            List<CertificateDataRegen> normalList = list(new LambdaQueryWrapper<CertificateDataRegen>()
                    .eq(CertificateDataRegen::getCreateStatus, "0")
                    .or()
                    .eq(CertificateDataRegen::getCreateStatus, "3")
                    .orderByDesc(CertificateDataRegen::getCreateTime)
                    .last("LIMIT " + remainingLimit));

            log.info("【证照重新生成】补充查询状态为0的记录数量：{}", normalList.size());

            // 合并两个列表
            priorityList.addAll(normalList);
        }

        log.info("【证照重新生成】最终返回记录总数：{}", priorityList.size());
        return priorityList;
    }

    public void createCertificate(CertificateDataRegen certificate) {
        try {
            log.info("【证照重新生成】开始处理证照，certificateId：{}", certificate.getCertificateId());
            //获取证照类型
            CertificateType certificateType = certificateTypeMapper.selectOne(new QueryWrapper<CertificateType>().lambda()
                                                .eq(StrUtil.isNotBlank(certificate.getCatalogName()),CertificateType::getCertificateName,certificate.getCatalogName()));
            // 查重处理：检查是否存在相同certificate_id且create_status为1或2的数据
            LambdaQueryWrapper<CertificateDataRegen> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper
                .eq(CertificateDataRegen::getCertificateId, certificate.getCertificateId())
                .eq(CertificateDataRegen::getCertificateType, certificate.getCertificateType())
                .eq(CertificateDataRegen::getCertificateNumber, certificate.getCertificateNumber())
                .in(CertificateDataRegen::getCreateStatus, Arrays.asList("1", "2"))
                .ne(CertificateDataRegen::getDataId, certificate.getDataId()); // 排除自身

            long count = count(queryWrapper);
            //记录当前时间
            certificate.setRegenTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            //如果是船员系统推送的证照，重复情况也需要重新生成
            if (count > 0 && !"2".equals(certificate.getDataType())) {
                log.info("【证照重新生成】发现重复数据，certificateId：{}", certificate.getCertificateId());
                certificate.setCreateStatus("12"); // 设置为重复状态
                updateById(certificate);
                return;
            }

            // 解析surface_data JSON数组
            if (StrUtil.isNotBlank(certificate.getSurfaceData())) {
                List<CertificateDataRegen.Surface> surfaces = objectMapper.readValue(
                        certificate.getSurfaceData(),
                        new TypeReference<List<CertificateDataRegen.Surface>>() {
                        });
                if(StrUtil.contains(certificate.getCatalogName(), "国籍证书")){
                    //处理国籍证书信息
                    certificate.setCertificateTypeCode(certificateType.getCertificateTypeCode());
                }

                // 生成二维码
                String qrCodeStr = certificate.getCertificateTypeCode() + "_" + certificate.getCertificateId();
                String encryptedId = Sm4Tool.encrypt(qrCodeStr,
                        "5d3b282609644b4f8992b31a2e92f0f3");
                // URL编码
                String para = URLEncoder.encode(encryptedId, StandardCharsets.UTF_8.toString());
                String qrCode = scanUrl + "?id=" + para;

                // 创建二维码Surface并添加到surfaces列表中
                CertificateDataRegen.Surface qrCodeSurface = new CertificateDataRegen.Surface();
                qrCodeSurface.setName("qrCodeUrl");
                qrCodeSurface.setValueType("string");
                qrCodeSurface.setValue(qrCode);

                // 添加二维码Surface到列表
                surfaces.add(qrCodeSurface);

                certificate.setSurface(surfaces);

            }

            // 将CertificateDataRegen转换为CertificateData对象
            CertificateData certificateData = convertToCertificateData(certificate);

            Map<String, String> map = new HashMap<>();
            map.put("内河船舶船员培训合格证","内河船舶船员特殊培训合格证");
            map.put("海船船员适任证书承认签证","承认签证电子证书");
            map.put("海船船员培训合格证承认签证","承认签证电子证书");
//            map.put("游艇驾驶证（内河）","09游艇驾驶证（内河）");
            // 证照配置名称转换
            if (StrUtil.isNotBlank(certificate.getCatalogName())) {
                String newName = map.get(certificateData.getCatalogName());
                if (newName != null) {
                    if("海船船员适任证书承认签证".equals(certificate.getCatalogName()) ||
                       "海船船员培训合格证承认签证".equals(certificate.getCatalogName())){
                        certificateData.setCertificateType(newName);
                    }else{
                        certificateData.setCatalogName(newName);
                        certificateData.setCertificateType(newName);
                    }
                }
            }

            // 调用CertificateUtil的getCertificate方法生成证书
            //log.info("【证照重新生成】开始生成证书，请求参数：{}", JSONUtil.toJsonStr(certificateData));

            // 创建证照生成日志对象
            CertificateDataRegenLog certificateLog = new CertificateDataRegenLog();
            certificateLog.setId(UUID.randomUUID().toString().replace("-", ""));
            certificateLog.setDataId(certificate.getDataId());
            certificateLog.setCertificateId(certificate.getCertificateId());
            certificateLog.setCatalogName(certificate.getCatalogName());
            certificateLog.setCreateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));

            if("1".equals(certificate.getStatus())){
                certificate.setOperType("add");
            }else if("-2".equals(certificate.getStatus())){
                certificate.setOperType("del");
            }

            // 修改这里：正确调用getCertificate方法并判断结果
            String certificateId = certificateUtil.getCertificate(certificateData,
                    urlConfig.getChromatographyPrintingUrl(), certificate, certificateLog);
            boolean success = StrUtil.isNotBlank(certificateId);

            // 处理surface中的base64内容
            processSurfaceBase64Content(certificateData);
            if(StrUtil.isNotBlank(certificate.getOperType())){
                certificateData.setOperType(certificate.getOperType());
            }else{
                certificateData.setOperType("add");
            }

            // 设置请求内容
            if (certificateLog != null) {
                certificateLog.setRequestData(JSON.toJSON(certificateData).toString());
            }
            // 保存证照生成日志
            certificateDataRegenLogMapper.insert(certificateLog);

            // 如果状态为11，设置主实体
            if (StrUtil.isNotBlank(certificateData.getCreateStatus()) && "11".equals(certificateData.getCreateStatus())) {
                log.error("【证照重新生成】photo关键字段有误,id:{}",certificate.getCertificateId());
                certificate.setCreateStatus("11");
                updateById(certificate);
                return;
            }

            //判断certificate.getDataType()是否等于"1"，1为历史数据
            if ("1".equals(certificate.getDataType())) {
                // 判断filePath字段是否为空或"marking"
                if (StrUtil.isBlank(certificate.getFilePath()) || "marking".equals(certificate.getFilePath())) {
                    certificate.setCreateStatus("11");
                    String errorMessage = StrUtil.isBlank(certificate.getFilePath())
                        ? "【证照重新生成】filePath关键字段为空"
                        : "【证照重新生成】filePath关键字段为 marking";
                    log.error(errorMessage+"id:{}", certificate.getCertificateId());
                    updateById(certificate);
                    return;
                }
            }

            // 更新状态
            certificate.setCreateStatus(success ? CertificateRegenStatus.SUCCESS.getCode() : CertificateRegenStatus.FAILED.getCode());
            updateById(certificate);

            // 如果证书生成成功，将船舶国籍证书的基本信息写入Certificate表，照面信息写入CertificateAttribute表
            if (StrUtil.equalsAny(certificate.getCatalogName(),
                            CertificateTypeCode.SHIP_NATIONALITY_CN.getName(),
                            CertificateTypeCode.SHIP_NATIONALITY_CN_EN.getName(),
                            CertificateTypeCode.SHIP_NATIONALITY_PROV_CN.getName(),
                            CertificateTypeCode.SHIP_NATIONALITY_PROV_CN_EN.getName())) {
                try {
                    log.info("【证照重新生成】开始保存证照基本信息和照面信息，certificateId：{}", certificate.getCertificateId());
                    //处理国籍证书信息
                    //获取事项名称
                    certificate.setAffairName(certificateType.getAffairName());
                    //certificate.getCatalogName()的值位"中华人民共和国船舶国籍证书_中文"，需要去掉"中华人民共和国"，和"中文"，后缀按下划线去掉包括下划线。
                    // 从证照名称中移除前缀 "中华人民共和国" 和后缀 "_中文"
                    String catalogName = certificate.getCatalogName();
                    // 移除前缀（如果存在）
                    if (catalogName.startsWith("中华人民共和国")) {
                        catalogName = catalogName.substring("中华人民共和国".length());
                    }

                    // 更新证照名称
                    certificate.setCatalogName(catalogName);
                    //重新赋值证照类型名称
                    certificate.setCertificateType(certificateType.getCertTypeDirName());
                    certificate.setCertificateTypeCode(certificateType.getCertificateTypeCode());
                    //取json报文中的签发机构
                    if (certificate.getSurface() != null && !certificate.getSurface().isEmpty()) {
                        // 构建字段名到Surface的映射
                        Map<String, CertificateDataRegen.Surface> surfaceMap = new HashMap<>();
                        for (CertificateDataRegen.Surface regenSurface : certificate.getSurface()) {
                            surfaceMap.put(regenSurface.getName(), regenSurface);
                            // 检查并更新 issueDept 字段的值
                            if ("signDept".equals(regenSurface.getName()) ||
                                "signDept1".equals(regenSurface.getName())) {
                                if (StrUtil.isNotBlank(regenSurface.getValue())) {
                                    String result = regenSurface.getValue().replaceFirst("^\\d+", "");
                                    certificate.setIssueDept(result);
                                    log.info("【证照重新生成】修正国籍证书的签发机关字段，更新为：{}", regenSurface.getValue());
                                    break;
                                }
                            }
                        }
                    }
                    log.info("【证照重新生成】国籍证书，更新为：{}，{}，{},{}", certificate.getCatalogName(),
                                                certificate.getAffairName(),certificate.getCertificateType(),certificate.getIssueDept());
                    saveCertificateData(certificate);
                } catch (Exception e) {
                    log.error("【证照重新生成】保存证照信息异常，certificateId：{}", certificate.getCertificateId(), e);
                }
            }

            log.info("【证照重新生成】证照生成{}, certificateId：{}",
                    success ? "成功" : "失败",
                    certificate.getCertificateId());
        } catch (Exception e) {
            log.error("【证照重新生成】证照生成异常，certificateId：{}", certificate.getCertificateId(), e);
            certificate.setCreateStatus(CertificateRegenStatus.FAILED.getCode());
            updateById(certificate);
        }
    }

    /**
     * 处理surface集合中的base64内容，将其替换为描述性文字
     * @param certificateData 证照数据对象
     */
    private void processSurfaceBase64Content(CertificateData certificateData) {
        if (certificateData != null && certificateData.getSurface() != null) {
            for (Surface surface : certificateData.getSurface()) {
                if (downloadPhotoKey.contains(surface.getColumnName())) {
                    // 如果value内容不为空，则改为文字：base64内容有值
                    // 如果value内容为空，则改为文字：base64内容为空
                    surface.setValue(StrUtil.isNotBlank(surface.getValue()) ? "base64内容有值" : "base64内容为空");
                }
            }
        }
    }

    /**
     * 将CertificateDataRegen对象转换为CertificateData对象
     *
     * @param certificate CertificateDataRegen对象
     * @return CertificateData对象
     */
    private CertificateData convertToCertificateData(CertificateDataRegen certificate) {
        CertificateData certificateData = new CertificateData();

        // 设置预览证书
        certificateData.setIsView("");
        // 获取证照类型的版本号和字段映射
        String version = "1";
        // 字段映射
        Map<String, String> fieldMappings = new HashMap<>();

        try {
            String certificateName = certificate.getCatalogName();  //查找证照配置使用
            // 通过catalogName关联证照类型表获取版本号和字段映射
            if (StrUtil.isNotBlank(certificate.getCatalogName())) {
                String catalogNameToQuery = certificate.getCertificateType();

                // 如果是游艇驾驶证，需要根据资格类型进一步确定证照类型
                if (catalogNameToQuery.startsWith("游艇驾驶证") && certificate.getSurface() != null
                        && !certificate.getSurface().isEmpty()) {
                    // 查找qualificationCn的值
                    String qualificationValue = "";
                    for (CertificateDataRegen.Surface surface : certificate.getSurface()) {
                        if ("qualificationCn".equals(surface.getName())) {
                            qualificationValue = surface.getValue();
                            break;
                        }
                    }

                    // 根据资格类型调整查询的证照名称
                    if (StrUtil.isNotBlank(qualificationValue)) {
                        certificate.setCertificateType("游艇驾驶证");
                        if (qualificationValue.startsWith("A")) {
                            certificateName = "游艇驾驶证（海上）";  //查找配置时，各环境保持一致
                            if("prod".equals(active)){
                                catalogNameToQuery = "游艇驾驶证海上";
                            }else {
                                catalogNameToQuery = "游艇驾驶证（海上）";
                            }
                            certificate.setCatalogName(catalogNameToQuery);
                            log.info("【证照重新生成】检测到资格类型为A，查询证照类型：{}", catalogNameToQuery);
                        } else if (qualificationValue.startsWith("B")) {
                            certificateName = "游艇驾驶证（内河）";  //查找配置时，各环境保持一致
                            if("prod".equals(active)){
                                catalogNameToQuery = "游艇驾驶证内河";
                            }else{
                                catalogNameToQuery = "游艇驾驶证（内河）";
                            }
                            certificate.setCatalogName(catalogNameToQuery);
                            log.info("【证照重新生成】检测到资格类型为B，查询证照类型：{}", catalogNameToQuery);
                        }
                    }
                }

                //证照重新生成 内河船舶船员特殊培训合格证 名称转换
                Map<String, String> map = new HashMap<>();
                map.put("内河船舶船员特殊培训合格证","内河船舶船员培训合格证");
                // 证照配置名称转换
                if (StrUtil.isNotBlank(certificateName)) {
                    String newName = map.get(certificateName);
                    if (newName != null) {
                        certificateName = newName;
                    }
                }

                // 构建查询条件
                log.info("【证照重新生成】查询证照配置，证照类型：{}", certificateName);
                LambdaQueryWrapper<CertificateType> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(CertificateType::getCertificateName, certificateName);

                // 查询证照类型
                CertificateType certificateType = certificateTypeMapper.selectOne(queryWrapper);
                if (certificateType != null) {
                    // 获取版本号
                    if (StrUtil.isNotBlank(certificateType.getCertVersion())) {
                        version = certificateType.getCertVersion();
                        log.info("【证照重新生成】获取到证照类型版本号：{}, catalogName: {}", version, certificate.getCatalogName());
                    }


                    // 获取字段映射
                    if (StrUtil.isNotBlank(certificateType.getFieldMapping())) {
                        try {
                            // 解析字段映射JSON为Map
                            fieldMappings = objectMapper.readValue(
                                    certificateType.getFieldMapping(),
                                    new TypeReference<Map<String, String>>() {
                                    });
                            log.info("【证照重新生成】获取到字段映射：{}", fieldMappings);
                        } catch (Exception e) {
                            log.error("【证照重新生成】解析字段映射JSON异常", e);
                        }
                    }
                } else {
                    log.warn("【证照重新生成】未找到对应的证照类型, catalogName: {}", catalogNameToQuery);
                }
            } else {
                log.warn("【证照重新生成】证照catalogName为空，无法获取版本号和字段映射");
            }
        } catch (Exception e) {
            log.error("【证照重新生成】获取证照版本号和字段映射异常", e);
        }

        // 设置版本号

        //设置版本号
        certificateData.setVersion(version);

        // 设置基本属性
        certificateData.setCertificateId(certificate.getCertificateId());
        certificateData.setCertificateType(certificate.getCertificateType());
        certificateData.setCertificateNumber(certificate.getCertificateNumber());

        // 设置目录名称
        certificateData.setCatalogName(certificate.getCatalogName() != null ?
                                      certificate.getCatalogName() :
                                      certificate.getCertificateType());

        certificateData.setCatalogName(
                certificate.getCatalogName() != null ? certificate.getCatalogName() : certificate.getCertificateType());

        // 设置颁发机构信息
        certificateData.setIssueDept(certificate.getIssueDept());

        // 处理IssueDeptCode，还原为真实机构编码
        String issueDeptCode = certificate.getIssueDeptCode();
        if (StrUtil.isNotBlank(issueDeptCode)) {
            issueDeptCode = restoreRealOrgCode(issueDeptCode);
        }
        certificateData.setIssueDeptCode(issueDeptCode);
        certificateData.setCreditCode(certificate.getCreditCode());
        certificateData.setDeptCode(issueDeptCode);

        // 设置证书持有者信息
        certificateData.setCertificateHolder(certificate.getCertificateHolder());
        certificateData.setCertificateHolderCode(certificate.getCertificateHolderCode());
        certificateData.setCertificateHolderType(certificate.getCertificateHolderType());
        certificateData.setName(certificate.getCertificateHolder());

        // 设置日期信息
        certificateData.setIssueDate(certificate.getIssueDate());
        certificateData.setCertificateValidateStart(certificate.getValidBeginDate());
        certificateData.setCertificateValidateEnd(certificate.getValidEndDate());

        // 设置区域编码
        certificateData.setCertificateAreaCode(certificate.getCertificateAreaCode());

        // 设置申请编号
        certificateData.setApplyNum(certificate.getApplyNum());

        // 设置证照类型和签章类型
        certificateData.setZzType(certificate.getZzType());
//        if("内河船员培训许可证".equals(certificate.getCatalogName())){
//            certificateData.setQzType("03"); //2025.5.16 生产联调
//        }else {
//            certificateData.setQzType("17");
//        }
         // 动态签章qzType赋值，后续该配置文件即可
        String catalogName = certificate.getCatalogName();
        int qzIndex = CERTIFICATE_NAMES.indexOf(catalogName);

        if (qzIndex != -1 && qzIndex < qzTypeValues.length) {
            certificateData.setQzType(qzTypeValues[qzIndex]);
            log.info("【证照生成】证书[{}]匹配qzType: {}", catalogName, qzTypeValues[qzIndex]);
        }else{
            log.error("【证照生成】证书[{}]未匹配到qzType", catalogName);
        }


        // 将Surface列表转换为CertificateData需要的格式
        if (certificate.getSurface() != null && !certificate.getSurface().isEmpty()) {
            List<Surface> surfaces = new ArrayList<>();

            // 用于存储year、month、day的值
            String yearValue = "";
            String monthValue = "";
            String dayValue = "";
            String qualificationValue = "";  //资格

            if("海船船员培训合格证书".equals(certificate.getCatalogName())){
                log.info("【证照生成】开始调整[海船船员培训合格证书]培训合格证顺序，证照编号[{}]", certificate.getCertificateNumber());
                List<CertificateDataRegen.Surface> result = sortSeafarerCertificateGroups(certificate.getSurface());
                certificate.setSurface(result);
                log.info("【证照生成】调整[海船船员培训合格证书]培训合格证顺序完成，证照编号[{}]", certificate.getCertificateNumber());
            }


            // 首先处理所有现有字段，并收集year、month、day的值
            for (CertificateDataRegen.Surface regenSurface : certificate.getSurface()) {
                Surface surface = new Surface();
                surface.setValue(regenSurface.getValue());
                surface.setValueType(StrUtil.isNotBlank(regenSurface.getValueType()) ?
                                   regenSurface.getValueType() :
                                   "string");

                surface.setValueType(
                        StrUtil.isNotBlank(regenSurface.getValueType()) ? regenSurface.getValueType() : "string");

                // 获取原字段名
                String oldFieldName = regenSurface.getName();

                // 处理照片
                if (downloadPhotoKey.contains(oldFieldName) && StrUtil.isNotBlank(regenSurface.getValue())) {
                    try {
                        // 构建完整的照片下载URL
                        String photoUrl = downloadPhotoUrl;
                        if (!photoUrl.endsWith("/") && !regenSurface.getValue().startsWith("/")) {
                            photoUrl += "/";
                        }
                        photoUrl += regenSurface.getValue();

                        log.info("【证照重新生成】开始下载照片，URL：{}",isBase64(regenSurface.getValue()) ? "已是base64图片，无需转换": photoUrl);

                        // 下载照片并转换为base64
                        String base64Photo = isBase64(regenSurface.getValue()) ? regenSurface.getValue():downloadImageAsBase64(photoUrl);

                        if (StrUtil.isNotBlank(base64Photo)) {
                            // 更新照片值为base64字符串
                            surface.setValue(base64Photo);
                        } else {
                            //photo为空，状态设置为 11,错误的标记
                            certificateData.setCreateStatus("11");
                            log.error("【证照重新生成】照片下载或转换失败,{}",photoUrl);
                        }
                    } catch (Exception e) {
                        log.error("【证照重新生成】处理照片异常", e);
                    }
                }else if((downloadPhotoKey.contains(oldFieldName) && StrUtil.isBlank(regenSurface.getValue()))){
                    certificateData.setCreateStatus("11");
                    log.error("【证照重新生成】照片字段值为空");
                }

                if (certificate.getCertificateType().startsWith("游艇驾驶证")) {
                    // 收集year、month、day的值
                    if ("year".equals(oldFieldName)) {
                        yearValue = regenSurface.getValue();
                    } else if ("month".equals(oldFieldName)) {
                        monthValue = regenSurface.getValue();
                    } else if ("day".equals(oldFieldName)) {
                        dayValue = regenSurface.getValue();
                    } else if ("qualificationCn".equals(oldFieldName)) {
                        qualificationValue = regenSurface.getValue();
                    }
                }

                // 应用字段映射转换为新字段名
                String newFieldName = oldFieldName;
                if (!fieldMappings.isEmpty() && fieldMappings.containsKey(oldFieldName)) {
                    newFieldName = fieldMappings.get(oldFieldName);
                    log.info("【证照重新生成】字段映射转换: {} -> {}", oldFieldName, newFieldName);
                }

                surface.setColumnName(newFieldName);
                surface.setName(newFieldName);

                surfaces.add(surface);
            }

            // 解析日期
            String formattedDate = convertDateBySubstring(certificateData.getIssueDate());
            // 格式化为目标格式

            if ("内河船舶船员培训合格证".equals(certificate.getCatalogName()) ||
                "内河船舶船员特殊培训合格证".equals(certificate.getCatalogName())) {
                //判断surface中的字段 number 字段的值是否等于certificate.getCertificateNumber()
                //如果不等于，则修改为certificate.getCertificateNumber()
                // 遍历 certificate 的 surface 数据
                if (certificate.getSurface() != null && !certificate.getSurface().isEmpty()) {
                    for (Surface regenSurface : surfaces) {
                        Surface surface = new Surface();
                        surface.setValue(regenSurface.getValue());
                        surface.setValueType(StrUtil.isNotBlank(regenSurface.getValueType()) ? regenSurface.getValueType() : "string");

                        // 检查并更新 number 字段的值
                        if ("number".equals(regenSurface.getName())) {
                            if (!Objects.equals(surface.getValue(), certificate.getCertificateNumber())) {
                                regenSurface.setValue(certificate.getCertificateNumber());
                                log.info("【证照重新生成】修正surface中的number字段，更新为证书编号：{}", surface.getValue());
                                break;
                            }
                        }
                    }
                }

                // 添加签发日期 签发机构
                Surface issueDept = new Surface();
                issueDept.setValue(certificateData.getIssueDept());
                issueDept.setValueType("string");
                issueDept.setColumnName("issueDept");
                issueDept.setName("issueDept");
                surfaces.add(issueDept);

                Surface issueDate = new Surface();
                issueDate.setValue(formattedDate);
                issueDate.setValueType("string");
                issueDate.setColumnName("issueDate");
                issueDate.setName("issueDate");
                surfaces.add(issueDate);
            }
            //在字段映射后进行转换 证书截止日期或有效期至超过2500，转成长期
            if ("内河船舶船员特殊培训合格证".equals(certificate.getCatalogName()) ||
                "内河船舶船员培训合格证".equals(certificate.getCatalogName()) ||
                "内河船舶船员适任证书".equals(certificate.getCatalogName()) ||
                "游艇驾驶证".equals(certificate.getCatalogName()) ||
                "游艇驾驶证海上".equals(certificate.getCatalogName()) ||
                "游艇驾驶证内河".equals(certificate.getCatalogName()) ||
                "游艇驾驶证（内河）".equals(certificate.getCatalogName()) ||
                "游艇驾驶证（海上）".equals(certificate.getCatalogName()) ||
                "引航员船员适任证书".equals(certificate.getCatalogName())){
                if (certificate.getSurface() != null && !certificate.getSurface().isEmpty()) {
                    for (Surface regenSurface : surfaces) {
                        if (StrUtil.contains(regenSurface.getColumnName(), "endDate") ||
                            StrUtil.contains(regenSurface.getColumnName(), "dateOfExpiry") ||
                            StrUtil.contains(regenSurface.getColumnName(),"certificateExpiringDate")) {
                            String value = regenSurface.getValue();
                            if (StrUtil.isNotBlank(value)) {
                                // 创建字段映射关系（中文字段 -> 英文字段）
                                Map<String, String> fieldMapping = new HashMap<>();
                                fieldMapping.put("dateOfExpiry1", "dateOfExpiry2");
                                fieldMapping.put("certificateExpiringDate1", "certificateExpiringDate2");
                                // 仅当值以4位数字开头时才处理，否则跳过
                                if (StrUtil.isNotBlank(value) && value.matches("^\\d{4}.*")){
                                    try {
                                        // 提取前4位作为年份
                                        String yearStr = value.length() >= 4 ? value.substring(0, 4) : value;
                                        int year = Integer.parseInt(yearStr);
                                        if (year > 2500) {
                                            regenSurface.setValue("长期");
                                            String enFieldName = fieldMapping.get(regenSurface.getColumnName());
                                            if (fieldMapping.containsKey(regenSurface.getColumnName()) &&
                                                        "长期".equals(regenSurface.getValue())) {
                                                for (Surface enSurface : surfaces) {
                                                    if (enFieldName.equals(enSurface.getColumnName())) {
                                                        enSurface.setValue("Long-term");
                                                        log.info("【证照重新生成】同步英文有效期字段: {} -> {}", enFieldName, "Long-term");
                                                        break;
                                                    }
                                                }
                                            }
                                        }
                                    } catch (Exception e) {
                                        log.warn("无法解析日期值：{}", value, e);
                                    }
                                }
                            }
                        }

                    }
                }
            }

            if (certificate.getCertificateType().startsWith("游艇驾驶证")) {
                //取主表的证照编号
                if (certificate.getSurface() != null && !certificate.getSurface().isEmpty()) {
                    for (Surface regenSurface : surfaces) {
                        Surface surface = new Surface();
                        surface.setValue(regenSurface.getValue());
                        surface.setValueType(StrUtil.isNotBlank(regenSurface.getValueType()) ? regenSurface.getValueType() : "string");

                        // 检查并更新 number 字段的值
                        if ("number".equals(regenSurface.getName()) || "certificateNo".equals(regenSurface.getName())) {
                            if (!Objects.equals(surface.getValue(), certificate.getCertificateNumber())) {
                                regenSurface.setValue(certificate.getCertificateNumber());
                                log.info("【证照重新生成】修正surface中的number字段，更新为证书编号：{}", surface.getValue());
                                break;
                            }
                        }
                    }
                }
                // 拼接year、month、day的值，并创建新的Surface对象
                if (StrUtil.isNotBlank(yearValue) || StrUtil.isNotBlank(monthValue)
                        || StrUtil.isNotBlank(dayValue)) {
                    String dateStrA =
                        yearValue + "-" +
                        (StrUtil.isNotBlank(monthValue) ? monthValue + "-" : "") +
                        (StrUtil.isNotBlank(dayValue) ? dayValue : "");
                    log.info("【证照重新生成】拼接日期字段: {}",dateStrA );

                    if (qualificationValue.startsWith("A")) {
                        // 游艇海上
                        // 创建issued0n1 Surface
                        Surface issuedOn1 = new Surface();
                        issuedOn1.setValue(dateStrA);
                        issuedOn1.setValueType("string");
                        issuedOn1.setColumnName("issuedOn1");
                        issuedOn1.setName("issued0n1");
                        surfaces.add(issuedOn1);
                        // 创建issued0n2 Surface
                        Surface issuedOn2 = new Surface();
                        issuedOn2.setValue(this.convertDateEn(dateStrA));
                        issuedOn2.setValueType("string");
                        issuedOn2.setColumnName("issuedOn2");
                        issuedOn2.setName("issued0n2");
                        surfaces.add(issuedOn2);

                        //类别
                        Surface category1 = new Surface();
                        category1.setValue(CategoryEnum.SEA.getName());
                        category1.setValueType("string");
                        category1.setColumnName("category1");
                        category1.setName("category1");
                        surfaces.add(category1);
                        Surface category2 = new Surface();
                        category2.setValue(CategoryEnum.SEA.getNameEn());
                        category2.setValueType("string");
                        category2.setColumnName("category2");
                        category2.setName("category2");
                        surfaces.add(category2);
                        //等级
                        if(qualificationValue.substring(1,2).equals("1")){
                           Surface class1 = new Surface();
                           class1.setValue(ClassEnum.SEA.getName());
                           class1.setValueType("string");
                           class1.setColumnName("class1");
                           class1.setName("class1");
                           surfaces.add(class1);
                           Surface class2 = new Surface();
                           class2.setValue(ClassEnum.SEA.getNameEn());
                           class2.setValueType("string");
                           class2.setColumnName("class2");
                           class2.setName("class2");
                           surfaces.add(class2);
                        }else if(qualificationValue.substring(1,2).equals("2")){
                           Surface class1 = new Surface();
                           class1.setValue(ClassEnum.SEA2.getName());
                           class1.setValueType("string");
                           class1.setColumnName("class1");
                           class1.setName("class1");
                           surfaces.add(class1);
                           Surface class2 = new Surface();
                           class2.setValue(ClassEnum.SEA2.getNameEn());
                           class2.setValueType("string");
                           class2.setColumnName("class2");
                           class2.setName("class2");
                           surfaces.add(class2);
                        }
                        //推进动力装置
                        if(qualificationValue.substring(2,3).equals("E")){
                            Surface propulsionType1 = new Surface();
                            propulsionType1.setValue(PropulsionTypeEnum.SEA.getName());
                            propulsionType1.setValueType("string");
                            propulsionType1.setColumnName("propulsionType1");
                            propulsionType1.setName("propulsionType1");
                            surfaces.add(propulsionType1);
                            Surface propulsionType2 = new Surface();
                            propulsionType2.setValue(PropulsionTypeEnum.SEA.getNameEn());
                            propulsionType2.setValueType("string");
                            propulsionType2.setColumnName("propulsionType2");
                            propulsionType2.setName("propulsionType2");
                            surfaces.add(propulsionType2);
                        }else if(qualificationValue.substring(2,3).equals("F")){
                            Surface propulsionType1 = new Surface();
                            propulsionType1.setValue(PropulsionTypeEnum.INNER_RIVER.getName());
                            propulsionType1.setValueType("string");
                            propulsionType1.setColumnName("propulsionType1");
                            propulsionType1.setName("propulsionType1");
                            surfaces.add(propulsionType1);
                            Surface propulsionType2 = new Surface();
                            propulsionType2.setValue(PropulsionTypeEnum.INNER_RIVER.getNameEn());
                            propulsionType2.setValueType("string");
                            propulsionType2.setColumnName("propulsionType2");
                            propulsionType2.setName("propulsionType2");
                            surfaces.add(propulsionType2);
                        }


                        Surface limitation1 = new Surface();
                        limitation1.setValue("无");
                        limitation1.setValueType("string");
                        limitation1.setColumnName("linination1");
                        limitation1.setName("linination1");
                        surfaces.add(limitation1);
                        Surface limitation2 = new Surface();
                        limitation2.setValue("None");
                        limitation2.setValueType("string");
                        limitation2.setColumnName("linination1");
                        limitation2.setName("linination1");
                        surfaces.add(limitation2);
                        //证书编号
                        Surface certificateNo = new Surface();
                        certificateNo.setValue(certificate.getCertificateNumber());
                        certificateNo.setValueType("string");
                        certificateNo.setColumnName("certificateNo");
                        certificateNo.setName("certificateNo");
                        surfaces.add(certificateNo);
                    } else {
                        // 游艇内河
                        // 创建 initialDate Surface
                        Surface issuedOn1 = new Surface();
                        issuedOn1.setValue(dateStrA);
                        issuedOn1.setValueType("string");
                        issuedOn1.setColumnName("initialDate");
                        issuedOn1.setName("initialDate");
                        surfaces.add(issuedOn1);
                        //类别
                        Surface category1 = new Surface();
                        category1.setValue(CategoryEnum.INNER_RIVER.getName());
                        category1.setValueType("string");
                        category1.setColumnName("category1");
                        category1.setName("category1");
                        surfaces.add(category1);
                        //等级
                        if(qualificationValue.substring(1,2).equals("1")){
                            Surface class1 = new Surface();
                            class1.setValue(ClassEnum.INNER_RIVER.getName());
                            class1.setValueType("string");
                            class1.setColumnName("class1");
                            class1.setName("class1");
                            surfaces.add(class1);
                        }else if(qualificationValue.substring(1,2).equals("2")){
                            Surface class1 = new Surface();
                            class1.setValue(ClassEnum.INNER_RIVER2.getName());
                            class1.setValueType("string");
                            class1.setColumnName("class1");
                            class1.setName("class1");
                            surfaces.add(class1);
                        }

                        //推进动力装置
                        if(qualificationValue.substring(2,3).equals("E")){
                            Surface propulsionType1 = new Surface();
                            propulsionType1.setValue(PropulsionTypeEnum.SEA.getName());
                            propulsionType1.setValueType("string");
                            propulsionType1.setColumnName("propulsionType1");
                            propulsionType1.setName("propulsionType1");
                            surfaces.add(propulsionType1);
                        }else if(qualificationValue.substring(2,3).equals("F")){
                            Surface propulsionType1 = new Surface();
                            propulsionType1.setValue(PropulsionTypeEnum.INNER_RIVER.getName());
                            propulsionType1.setValueType("string");
                            propulsionType1.setColumnName("propulsionType1");
                            propulsionType1.setName("propulsionType1");
                            surfaces.add(propulsionType1);
                        }
                        Surface limitation1 = new Surface();
                        limitation1.setValue("无");
                        limitation1.setValueType("string");
                        limitation1.setColumnName("limitation1");
                        limitation1.setName("limitation1");
                        surfaces.add(limitation1);
                    }

                    log.info("【证照重新生成】添加issued0n1和issued0n2字段，值为: {}", dateStrA);
                }
            }

            if(certificate.getCatalogName().equals("海船船员内河航线行驶资格证明")){
                // 假设 certificate.getCreditCode() 返回的是身份证号码
                String creditCode = certificate.getCertificateHolderCode(); // 例如 "11010519491231002X"

                // 提取出生日期部分（第7到14位）
                String year = creditCode.substring(6, 10);   // 年份（4位）
                String month = creditCode.substring(10, 12); // 月份（2位）
                String day = creditCode.substring(12, 14);   // 日期（2位）

                // 拼接成 "yyyy年MM月dd日" 格式
                String birthDateVal = year + "-" + month + "-" + day;

                //循环surfaces，获取creditCode
                for (Surface surface : surfaces) {
                    if ("creditCode".equals(surface.getColumnName())) {
                        surface.setValue(birthDateVal);
                        break;
                    }
                }
            }

            if(certificate.getCatalogName().equals("海上非自航船舶船员适任证书")){
                if (certificate.getSurface() != null && !certificate.getSurface().isEmpty()) {
                    Set<String> dateFields = new HashSet<>(Arrays.asList(
                        "dateOfBirth",
                        "dateOfExpirty",
                        "dateOfIssue"
                    ));

                    for (Surface regenSurface : surfaces) {
                        if (dateFields.contains(regenSurface.getName())) {
                            String rawValue = regenSurface.getValue();
                            if (StrUtil.isNotBlank(rawValue)) {
                                try {
                                    // 明确指定输入格式
                                    Date date = DateUtil.parse(rawValue, "yyyy年MM月dd日");
                                    // 统一输出格式
                                    String formattedDateStr = DateUtil.format(date, "yyyy-MM-dd");
                                    regenSurface.setValue(formattedDateStr);
                                    log.info("【证照重新生成】日期字段 {} 已转换: {} -> {}",
                                        regenSurface.getName(), rawValue, formattedDateStr);
                                } catch (Exception e) {
                                    log.error("日期格式转换失败: {}", rawValue, e);
                                    // 可选：设置特殊标记便于后续排查
                                    regenSurface.setValue("INVALID_DATE");
                                }
                            }
                        }
                    }
                }
            }

            if ("海上设施工作人员海上交通安全技能培训合格证明".equals(certificate.getCatalogName())) {
                String year2 = certificate.getIssueDate().substring(0, 4);
                String month2 = certificate.getIssueDate().substring(5, 7);
                String day2 = certificate.getIssueDate().substring(8, 10);

                Surface yearSurface = new Surface();
                yearSurface.setValue(year2);
                yearSurface.setValueType("string");
                yearSurface.setColumnName("year2");
                yearSurface.setName("year2");
                surfaces.add(yearSurface);

                Surface monthSurface = new Surface();
                monthSurface.setValue(month2);
                monthSurface.setValueType("string");
                monthSurface.setColumnName("month2");
                monthSurface.setName("month2");
                surfaces.add(monthSurface);

                Surface daySurface = new Surface();
                daySurface.setValue(day2);
                daySurface.setValueType("string");
                daySurface.setColumnName("day2");
                daySurface.setName("day2");
                surfaces.add(daySurface);
            }

            if(certificate.getCatalogName().equals("海员外派机构资质证书")){
                //服务范围
                Surface serviceInclude1 = new Surface();
                serviceInclude1.setValue("为外国籍或港澳台地区籍船舶提供配员。");
                serviceInclude1.setValueType("string");
                serviceInclude1.setColumnName("serviceInclude1");
                serviceInclude1.setName("serviceInclude1");
                surfaces.add(serviceInclude1);

                Surface serviceInclude2 = new Surface();
                serviceInclude2.setValue("Crew manning for Vessels flying flags of foreign countries or Hong Kong, Macao and Taiwan.");
                serviceInclude2.setValueType("string");
                serviceInclude2.setColumnName("serviceInclude2");
                serviceInclude2.setName("serviceInclude2");
                surfaces.add(serviceInclude2);
            }
            //健康证明
            if(certificate.getCatalogName().equals("海船船员健康证明")){
                //取主表的证照编号
                if (certificate.getSurface() != null && !certificate.getSurface().isEmpty()) {
                    for (Surface regenSurface : surfaces) {
                        if (StrUtil.contains(regenSurface.getColumnName(),"yesOrNo")) {
                            if (StrUtil.isNotBlank(regenSurface.getValue())) {
                                // 1. 统一Yes/No格式（首字母大写，其余小写）
                                String originalValue = regenSurface.getValue();
                                String formattedValue = originalValue;
                                if (formattedValue.toLowerCase().startsWith("yes")) {
                                    formattedValue = "Yes" + formattedValue.substring(3).toLowerCase();
                                } else if (formattedValue.toLowerCase().startsWith("no")) {
                                    formattedValue = "No" + formattedValue.substring(2).toLowerCase();
                                }
                                // 2. 清理重复逗号（只替换连续多个逗号为单个逗号，保留末尾逗号）
                                formattedValue = formattedValue.replaceAll(",,+", ",");
                                // 3. 更新字段值
                                regenSurface.setValue(formattedValue);
                                // 3. 更新字段值
                                regenSurface.setValue(formattedValue);
                                log.info("【证照重新生成】更新健康证明surface中的yesOrNo字段，原始值：{} -> 更新值：{}",
                                        originalValue, formattedValue);
                            }
                        }
                    }
                }
            }
            //国籍证书照面信息为空取"--"
            if(StrUtil.contains(certificate.getCatalogName(),"船舶国籍证书")){
                if (certificate.getSurface() != null && !certificate.getSurface().isEmpty()) {
                    for (Surface regenSurface : surfaces) {
                        if(!"remarks".equals(regenSurface.getName())){
                            if (StrUtil.isBlank(regenSurface.getValue())) {
                               regenSurface.setValue("－－");
                            }
                        }
                    }
                }
            }
            //质量体系证书
            if(certificate.getCatalogName().equals("船员培训质量管理体系证书")){
                //照面信息截止日期取主表的month,以及dzqm
                if (certificate.getSurface() != null && !certificate.getSurface().isEmpty()) {
                    Surface surface = new Surface();
                    for (Surface regenSurface : surfaces) {
                        if ("month1".equals(regenSurface.getColumnName())) {
                            //截取 certificate.getValidEndDate().toString() 月份
                            if (!Objects.equals(regenSurface.getValue(), certificate.getValidEndDate().toString().substring(5,7))) {
                                regenSurface.setValue(certificate.getValidEndDate().toString().substring(5,7));
                                log.info("【证照重新生成】修正surface中的month1字段，更新为月份：{}", regenSurface.getValue());
                            }
                        }
                        if("nameOfDulyAuthorizedOfficial1".equals(regenSurface.getColumnName())){
                            surface.setValue(regenSurface.getValue());
                            surface.setValueType("string");
                            surface.setColumnName("dzqm");
                            surface.setName("dzqm");
                        }
                    }
                    if(StrUtil.isNotBlank(surface.getColumnName())){
                        surfaces.add(surface);
                        log.info("【证照重新生成】质量证书新增dzqm:{}", surface.getValue());
                    }
                }

                if("1".equals(certificate.getDataType())){
                    String[] fieldNames = {
                        "typeOfTheEvaluation1",
                        "typeOfTheEvaluation2",
                        "typeOfTheEvaluation3",
                        "typeOfTheEvaluation4",
                        "typeOfTheEvaluation5",
                        "typeOfTheEvaluation6",
                        "resultOfEvaluation1",
                        "resultOfEvaluation2",
                        "resultOfEvaluation3",
                        "resultOfEvaluation4",
                        "resultOfEvaluation5",
                        "resultOfEvaluation6",
                        "evaluationDate1",
                        "evaluationDate2",
                        "evaluationDate3",
                        "evaluationDate4",
                        "evaluationDate5",
                        "evaluationDate6",
                        "evaOranization1",
                        "evaOranization2",
                        "evaOranization3",
                        "evaOranization4",
                        "evaOranization5",
                        "evaOranization6",
                        "evaOranizationCode1",
                        "evaOranizationCode2",
                        "evaOranizationCode3",
                        "evaOranizationCode4",
                        "evaOranizationCode5",
                        "evaOranizationCode6",
                        "remarks2",
                    };

                    for (String fieldName : fieldNames) {
                        Surface surface = new Surface();
                        surface.setValue("");
                        surface.setValueType("string");
                        surface.setColumnName(fieldName);
                        surface.setName(fieldName);
                        surfaces.add(surface);
                    }
                }

            }

            //海船船员适任证书承认签证，
            if(certificate.getCatalogName().equals("海船船员适任证书承认签证") ||
               certificate.getCatalogName().equals("海船船员培训合格证承认签证") ||
               certificate.getCatalogName().equals("海船船员特免证明")){
                //新增dzqm
                if (certificate.getSurface() != null && !certificate.getSurface().isEmpty()) {
                    Surface surface = new Surface();
                    for (Surface regenSurface : surfaces) {
                        if("nameOfDulyAuthorizedOfficial1".equals(regenSurface.getColumnName())){
                            surface.setValue(regenSurface.getValue());
                            surface.setValueType("string");
                            surface.setColumnName("dzqm");
                            surface.setName("dzqm");
                        }
                    }
                    if(StrUtil.isNotBlank(surface.getColumnName())){
                        surfaces.add(surface);
                        log.info("【证照重新生成】{}新增dzqm:{}",certificate.getCatalogName(), surface.getValue());
                    }
                }
            }

            //船上厨师培训合格证明
            if(certificate.getCatalogName().equals("船上厨师培训合格证明")){
                //新增dzqm
                if (certificate.getSurface() != null && !certificate.getSurface().isEmpty()) {
                    Surface surface = new Surface();
                    for (Surface regenSurface : surfaces) {
                        if("nameOfTheTraingManager1".equals(regenSurface.getColumnName())){
                            surface.setValue(regenSurface.getValue());
                            surface.setValueType("string");
                            surface.setColumnName("dzqm");
                            surface.setName("dzqm");
                        }
                    }
                    if(StrUtil.isNotBlank(surface.getColumnName())){
                        surfaces.add(surface);
                        log.info("【证照重新生成】船上厨师新增dzqm:{}", surface.getValue());
                    }
                }
            }

            if(certificate.getCatalogName().equals("游艇驾驶证海上")){
                if (certificate.getSurface() != null && !certificate.getSurface().isEmpty()) {
                    String dateOfBirthEn = "";
                    String dateOfExpiryEn = "";
                    Surface surface = new Surface();
                    for (Surface regenSurface : surfaces) {
                        if("dateOfBirth1".equals(regenSurface.getColumnName())){
                            dateOfBirthEn = this.convertDateEn(regenSurface.getValue());
                        }else if("dateOfExpiry1".equals(regenSurface.getColumnName())){
                            dateOfExpiryEn = this.convertDateEn(regenSurface.getValue());
                        }
                    }
                    for (Surface regenSurface : surfaces) {
                        if("dateOfBirth2".equals(regenSurface.getColumnName())){
                            regenSurface.setValue(dateOfBirthEn);
                        }else if("dateOfExpiry2".equals(regenSurface.getColumnName())){
                            regenSurface.setValue(dateOfExpiryEn);
                        }
                    }
                }
            }

            certificateData.setSurface(surfaces);
        }
        return certificateData;
    }

    /**
     * 从URL下载图片并转换为Base64字符串
     * 🔑 修复：使用try-with-resources确保资源正确关闭，防止句柄泄露
     *
     * @param imageUrl 图片URL
     * @return Base64编码的字符串
     */
    private String downloadImageAsBase64(String imageUrl) {
        HttpURLConnection connection = null;

        try {
            // 建立连接
            URL url = new URL(imageUrl);
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(10000);
            connection.setReadTimeout(10000);

            // 获取响应码
            int responseCode = connection.getResponseCode();
            if (responseCode != HttpURLConnection.HTTP_OK) {
                log.error("【证照重新生成】下载照片失败，响应码：{}", responseCode);
                return null;
            }

            // 🔑 关键修复：使用try-with-resources确保流被正确关闭
            // 资源关闭保证：
            // 1. outputStream.close() - 确保数据完全写入
            // 2. inputStream.close() - 释放网络连接资源
            // 3. 即使发生异常，这两个流都会被自动关闭
            try (InputStream inputStream = connection.getInputStream();
                 ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }

                // 转换为Base64
                byte[] imageBytes = outputStream.toByteArray();
                return Base64.getEncoder().encodeToString(imageBytes);
            }

        } catch (Exception e) {
            log.error("【证照重新生成】下载图片并转换为Base64异常", e);
            return null;
        } finally {
            // 只需要关闭HttpURLConnection，流资源已由try-with-resources自动关闭
            if (connection != null) {
                try {
                    connection.disconnect();
                } catch (Exception e) {
                    log.warn("【证照重新生成】关闭HTTP连接异常", e);
                }
            }
        }
    }

    /**
     * 创建证照数据
     *
     * @param certificateDataRegen 证照数据
     * @return 证照ID
     */
    public String createCertificateData(CertificateDataRegen certificateDataRegen) {
        // 非空校验
        if (certificateDataRegen == null) {
            log.error("请求参数certificateDataRegen不能为空");
            throw new IllegalArgumentException("certificateDataRegen不能为空");
        }

        List<String> missingFields = new ArrayList<>();

        if (StrUtil.isBlank(certificateDataRegen.getCreditCode())) {
            missingFields.add("creditCode");
        }
        if (StrUtil.isBlank(certificateDataRegen.getCatalogName())) {
            missingFields.add("catalogName");
        }
        if (StrUtil.isBlank(certificateDataRegen.getCertificateType())) {
            missingFields.add("certificateType");
        }
        if (StrUtil.isBlank(certificateDataRegen.getCertificateTypeCode())) {
            missingFields.add("certificateTypeCode");
        }
        if (StrUtil.isBlank(certificateDataRegen.getCertificateAreaCode())) {
            missingFields.add("certificateAreaCode");
        }
        if (StrUtil.isBlank(certificateDataRegen.getCertificateHolder())) {
            missingFields.add("certificateHolder");
        }
        if (StrUtil.isBlank(certificateDataRegen.getCertificateHolderCode())) {
            missingFields.add("certificateHolderCode");
        }
        if (StrUtil.isBlank(certificateDataRegen.getCertificateHolderType())) {
            missingFields.add("certificateHolderType");
        }
        if (StrUtil.isBlank(certificateDataRegen.getIssueDept())) {
            missingFields.add("issueDept");
        }
        if (StrUtil.isBlank(certificateDataRegen.getCertificateNumber())) {
            missingFields.add("certificateNumber");
        }
        if (StrUtil.isBlank(certificateDataRegen.getIssueDeptCode())) {
            missingFields.add("issueDeptCode");
        }
        if (StrUtil.isBlank(certificateDataRegen.getIssueDate())) {
            missingFields.add("issueDate");
        }
        if (StrUtil.isBlank(certificateDataRegen.getCertificateValidateStart())) {
            missingFields.add("certificateValidateStart");
        }
        if (StrUtil.isBlank(certificateDataRegen.getCertificateValidateEnd())) {
            missingFields.add("certificateValidateEnd");
        }
        boolean foundPhoto = false;
        if((!"海船船员培训许可证".equals(certificateDataRegen.getCatalogName())) &&
           (!"内河船员培训许可证".equals(certificateDataRegen.getCatalogName())) &&
           (!"船员培训质量管理体系证书".equals(certificateDataRegen.getCatalogName())) &&
           (!"海员外派机构资质证书".equals(certificateDataRegen.getCatalogName())) &&
           (!StrUtil.contains(certificateDataRegen.getCatalogName(), "国籍证书"))){

            if (!certificateDataRegen.getSurface().isEmpty() && certificateDataRegen.getSurface().size() > 0) {
                for (CertificateDataRegen.Surface surface : certificateDataRegen.getSurface()){
                    if (downloadPhotoKey.contains(surface.getColumnName())) {
                       //判断photo是否存在，并且值是否为空
                       foundPhoto = true;
                       if (StrUtil.isBlank(surface.getValue())) {
                           missingFields.add("surface.图片字段");
                       }
                    }
                }
            }
            // 如果未找到 photo 字段，也视为字段缺失
            if (!foundPhoto) {
                missingFields.add("surface.photo");
            }
        }

        if (!missingFields.isEmpty()) {
            String errorMessage = "请求参数不能为空字段: " + String.join(", ", missingFields);
            log.error(errorMessage);
            throw new IllegalArgumentException(errorMessage);
        }


        // 判断是否传值certificateId 生成基于时间戳的证照ID
        String certificateId = StrUtil.isNotBlank(certificateDataRegen.getCertificateId()) ? certificateDataRegen.getCertificateId() : generateCertificateId();
        certificateDataRegen.setCertificateId(certificateId);

        log.info("【证照生成】生成certificateId：{}", certificateId);

        // 设置初始状态为未创建
        certificateDataRegen.setCreateStatus(CertificateRegenStatus.UN_CREATE.getCode());

        if("内河船舶船员特殊培训合格证".equals(certificateDataRegen.getCertificateType())){
            certificateDataRegen.setCertificateType("内河船舶船员培训合格证");
        }

        //获取证照类型
        CertificateType certificateType = certificateTypeMapper.selectOne(new QueryWrapper<CertificateType>().lambda()
                                            .eq(StrUtil.isNotBlank(certificateDataRegen.getCertificateType()),CertificateType::getCertificateName,certificateDataRegen.getCatalogName()));
        if (certificateType == null) {
            log.error("未找到对应的证照类型，certificateType: {}", certificateDataRegen.getCertificateType());
            throw new IllegalArgumentException("证照类型不存在：" + certificateDataRegen.getCatalogName());
        }
        //同步状态（0-新增未上传,1-已上传，2-更新未上传，3-更新已上传,4-删除未上传,5-删除未上传）
        certificateDataRegen.setSyncStatus("1");
        certificateDataRegen.setTemplateId(certificateType.getCatalogId());
        certificateDataRegen.setCatalogId(certificateType.getCatalogId());
        certificateDataRegen.setCertificateTypeCode(certificateType.getCertificateTypeCode());
        certificateDataRegen.setCreateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        certificateDataRegen.setValidBeginDate(certificateDataRegen.getCertificateValidateStart());
        certificateDataRegen.setValidEndDate(certificateDataRegen.getCertificateValidateEnd());
        //外部推送数据
        certificateDataRegen.setDataType("2");
        //判断是新办件还是已注销的数据
        if(StrUtil.isNotBlank(certificateDataRegen.getOperType()) && "add".equals(certificateDataRegen.getOperType())){
            certificateDataRegen.setStatus("1");
        }else if(StrUtil.isNotBlank(certificateDataRegen.getOperType()) && "del".equals(certificateDataRegen.getOperType())){
            certificateDataRegen.setStatus("-2");
        }

        if (certificateDataRegen.getSurface() != null && !certificateDataRegen.getSurface().isEmpty()) {
            certificateDataRegen.getSurface().forEach(surface -> {
                surface.setName(surface.getColumnName());
            });
        }
        // 将surface数据转换为JSON字符串
        String jsonSurface = JSONUtil.toJsonStr(certificateDataRegen.getSurface());
        certificateDataRegen.setSurfaceData(jsonSurface);
        // 保存证照数据
        save(certificateDataRegen);

        log.info("【证照生成】证照数据保存成功，certificateId：{}", certificateId);
        return certificateId;
    }

    /**
     * 生成证照ID，格式为：时间戳+5位随机数
     *
     * @return 证照ID
     */
    private String generateCertificateId() {
        // 获取当前时间戳
        long timestamp = System.currentTimeMillis();

        // 生成5位随机数 (10000-99999)
        int random = 10000 + (int) (Math.random() * 90000);

        // 拼接时间戳和随机数
        return timestamp + String.valueOf(random);
    }

    /**
     * 保存证照信息到证照基本信息表和照面属性表
     *
     * @param certificate CertificateDataRegen对象
     */
    private void saveCertificateData(CertificateDataRegen certificate) {
        // 1. 保存证照基本信息到Certificate表
        Certificate certEntity = new Certificate();

        // 设置主键ID
        certEntity.setId(certificate.getCertificateId());

        // 电子证照唯一ID
        certEntity.setCertificateId(certificate.getCertificateId());

        // 证书名称
        certEntity.setCertificateName(certificate.getCatalogName());

        // 证照类型
        certEntity.setCertificateType(certificate.getCertificateType());

        // 证照类型代码
        //CertificateTypeCode certificateTypeCode = CertificateTypeCode.getCertificateByName(certificate.getCatalogName());
        //if (certificateTypeCode != null) {
            certEntity.setCertificateTypeCode(certificate.getCertificateTypeCode());
        //}
        // 发证机关名称
        certEntity.setIssuOrgNameCn(certificate.getIssueDept());

        // 发证机关代码:2.0版本
        certEntity.setIssuOrgCode2(certificate.getIssueDeptCode());

        // 发证机关代码:3.0版本 - 从字典表获取
        String orgCode3 = null;
        try {
            orgCode3 = dictYthOrgMappingService.getOrgCode(certificate.getIssueDeptCode());
            log.info("【证照重新生成】查询3.0机构编码，srcOrgCode: {}, orgCode: {}", certificate.getIssueDeptCode(), orgCode3);
        } catch (Exception e) {
            log.error("【证照重新生成】查询3.0机构编码异常", e);
        }
        // 交通运输局和地方海事局机构不需要映射成3.0
        if (StrUtil.contains(certificate.getIssueDept(),"地方海事局")
                || StrUtil.contains(certificate.getIssueDept(),"地方海事处")
                || StrUtil.contains(certificate.getIssueDept(),"交通运输局")
                || StrUtil.contains(certificate.getIssueDept(),"交通运输厅")) {
            orgCode3 = certificate.getIssueDeptCode();
        }
        certEntity.setIssuOrgCode3(orgCode3);

        // 持有人姓名
        certEntity.setHolderName(certificate.getCertificateHolder());

        // 持有人身份标识号码
        certEntity.setHolderIdentityNumber(certificate.getCertificateHolderCode());

        // 签发日期
        try {
            if (StrUtil.isNotBlank(certificate.getIssueDate())) {
                certEntity.setIssueDate(new java.text.SimpleDateFormat("yyyy-MM-dd").parse(certificate.getIssueDate()));
            }
        } catch (Exception e) {
            log.error("【证照重新生成】解析签发日期异常", e);
        }

        // 有效期日期-开始
        try {
            if (StrUtil.isNotBlank(certificate.getValidBeginDate())) {
                certEntity.setEffectDate(
                        new java.text.SimpleDateFormat("yyyy-MM-dd").parse(certificate.getValidBeginDate()));
            }
        } catch (Exception e) {
            log.error("【证照重新生成】解析有效期开始日期异常", e);
        }

        // 有效期日期-结束
        try {
            if (StrUtil.isNotBlank(certificate.getValidEndDate())) {
                certEntity.setExpireDate(
                        new java.text.SimpleDateFormat("yyyy-MM-dd").parse(certificate.getValidEndDate()));
            }
        } catch (Exception e) {
            log.error("【证照重新生成】解析有效期结束日期异常", e);
        }

        // 证照创建状态
        certEntity.setCreateStatus("1"); // 设置为已生成

        // 证照使用状态:1-已登记
        certEntity.setStatusFlag("1");

        // 申请编号
        certEntity.setApplyNo(certificate.getApplyNum());

        // 事项名称
        certEntity.setAffairName(certificate.getAffairName());

        // 申请人
        certEntity.setApplicantName(certificate.getCertificateHolder());

        // 受理机构编码2.0
        certEntity.setAcceptOrgCode2(certificate.getIssueDeptCode());

        // 审批机构编码2.0
        certEntity.setApprOrgCode2(certificate.getIssueDeptCode());

        // 受理机构编码3.0
        certEntity.setAcceptOrgCode3(orgCode3);

        // 审批机构编码3.0
        certEntity.setApprOrgCode3(orgCode3);

        // 证书编号/证照编号
        certEntity.setCertificateNum(certificate.getCertificateNumber());

        // 船舶相关信息从照面信息中获取
        if (certificate.getSurface() != null && !certificate.getSurface().isEmpty()) {
            for (CertificateDataRegen.Surface surface : certificate.getSurface()) {
                // 船舶识别号
                if ("shipId".equals(surface.getName())) {
                    certEntity.setShipId(this.getSurfaceValue(surface));
                }
                // 船舶名称
                else if ("shipName".equals(surface.getName()) || "shipName1".equals(surface.getName())) {
                    certEntity.setShipName(this.getSurfaceValue(surface));
                }
                // IMO
                else if ("IMONo".equals(surface.getName())) {
                    certEntity.setShipImo(this.getSurfaceValue(surface));
                }
                // 船舶呼号
                else if ("shipSign".equals(surface.getName())) {
                    certEntity.setShipCallSign(this.getSurfaceValue(surface));
                }
                // 船舶英文名
                else if ("shipName2".equals(surface.getName())) {
                    certEntity.setShipNameEn(this.getSurfaceValue(surface));
                }
                // MMSI
                else if ("shipMmsi".equals(surface.getName())) {
                    certEntity.setShipMmsi(this.getSurfaceValue(surface));
                }
                // 印刷号
                else if ("printNo".equals(surface.getName())) {
                    certEntity.setCertPrintNo(this.getSurfaceValue(surface));
                }
            }
        }

        // 设置创建/修改时间
        Date now = new Date();
        certEntity.setCreateDate(now);
        certEntity.setModifyDate(now);

        // 设置创建/修改人
        certEntity.setCreateOperId(certificate.getCertificateHolderCode());
        certEntity.setModifyOperId(certificate.getCertificateHolderCode());

        // 保存证照基本信息
        certificateService.save(certEntity);
        log.info("【证照重新生成】保存证照基本信息成功，certificateId：{}", certificate.getCertificateId());

        // 2. 保存照面信息到CertificateAttribute表
        if (certificate.getSurface() != null && !certificate.getSurface().isEmpty()) {
            List<CertificateAttribute> attributeList = new ArrayList<>();

            // 判断是否是船舶国籍证书或临时船舶国籍证书的中文版本
            boolean isNationalCertificate = "船舶国籍证书_中文".equals(certificate.getCatalogName()) ||
                                            "临时船舶国籍证书_中文".equals(certificate.getCatalogName());

            // 构建字段名到Surface的映射
            Map<String, CertificateDataRegen.Surface> surfaceMap = new HashMap<>();
            for (CertificateDataRegen.Surface surface : certificate.getSurface()) {
                surfaceMap.put(surface.getName(), surface);
            }

            for (CertificateDataRegen.Surface surface : certificate.getSurface()) {
                String name = surface.getName();
                // 如果是需要处理的证书类型
                if (isNationalCertificate) {
                    // 跳过 buildCompany 和 reBuildCompany 字段
                    if ("buildCompany".equals(name) || "reBuildCompany".equals(name)) {
                        continue;
                    }

                    // 处理 buildPlace 字段
                    if ("buildPlace".equals(name)) {
                        CertificateDataRegen.Surface buildCompany = surfaceMap.get("buildCompany");
                        if (buildCompany != null) {
                            String value1 = surface.getValue() != null ? surface.getValue() : "";
                            String value2 = buildCompany.getValue() != null ? buildCompany.getValue() : "";
                             if (value1.isEmpty() && value2.isEmpty()) {
                                surface.setValue("--"); // 都为空显示 "--"
                             } else if (value1.isEmpty()) {
                                surface.setValue(value2); // 仅 value1 为空
                             } else if (value2.isEmpty()) {
                                surface.setValue(value1); // 仅 value2 为空
                             } else {
                                surface.setValue(value1 + "\r\n" + value2);// 两个都不为空，拼接
                             }
                        }
                    }

                    // 处理 reBuildPlace 字段
                    if ("reBuildPlace".equals(name)) {
                        CertificateDataRegen.Surface reBuildCompany = surfaceMap.get("reBuildCompany");
                        if (reBuildCompany != null) {
                            String value1 = surface.getValue() != null ? surface.getValue() : "";
                            String value2 = reBuildCompany.getValue() != null ? reBuildCompany.getValue() : "";
                             if (value1.isEmpty() && value2.isEmpty()) {
                                surface.setValue("--"); // 都为空显示 "--"
                             } else if (value1.isEmpty()) {
                                surface.setValue(value2); // 仅 value1 为空
                             } else if (value2.isEmpty()) {
                                surface.setValue(value1); // 仅 value2 为空
                             } else {
                                surface.setValue(value1 + "\r\n" + value2);// 两个都不为空，拼接
                             }
                        }
                    }
                }

                CertificateAttribute attribute = new CertificateAttribute();

                // 生成主键ID
                attribute.setCertificateAttributeId(java.util.UUID.randomUUID().toString().replace("-", ""));

                // 关联证照ID
                attribute.setCertificateId(certEntity.getId());

                // 属性名称
                attribute.setAttributeName(surface.getName());

                // 属性字段名称
                attribute.setAttributeColumnName(surface.getName());

                // 属性值
                attribute.setAttributeValue(surface.getValue());

                // 创建时间
                attribute.setCreateDate(new Date());

                // 修改时间
                attribute.setModifyDate(new Date());

                attributeList.add(attribute);
            }

            // 批量保存照面属性
            if (!attributeList.isEmpty()) {
                certificateAttributeService.saveBatch(attributeList);
                log.info("【证照重新生成】保存证照照面属性成功，共{}条，certificateId：{}", attributeList.size(),
                        certificate.getCertificateId());
            }
        }
    }

    /**
     * 将照面信息的空值设置为 - -
     * @param surface 照面信息
     * @return 照面信息值
     */
    private String getSurfaceValue(CertificateDataRegen.Surface surface) {
        return StrUtil.equalsAny(surface.getValue(),"","----") ? "- -" : surface.getValue();
    }

    /**
     * 根据certificateId查询电子证照信息
     * @param certificateId 电子证照ID
     * @return 电子证照实体
     */
    public CertificateDataRegen getByCertificateId(String certificateId) {
        QueryWrapper<CertificateDataRegen> queryWrapper = Wrappers.<CertificateDataRegen>query();
        queryWrapper.lambda()
                .eq(CertificateDataRegen::getCertificateId, certificateId)
                .last("LIMIT 1");
        return this.getOne(queryWrapper, false);
    }

    public List<CertificateDataRegen> getByDataIds(List<String> dataIds) {
        return this.list(Wrappers.<CertificateDataRegen>lambdaQuery()
                .in(CertificateDataRegen::getDataId, dataIds));
    }


    public static String convertDateBySubstring(String inputDate) {
        // 假设输入格式严格为 yyyy-MM-dd
        String year = inputDate.substring(0, 4);   // 截取年份
        String month = inputDate.substring(5, 7);  // 截取月份
        String day = inputDate.substring(8, 10);    // 截取日期

        // 拼接成中文格式
        return year + "-" + month + "-" + day;
    }

    // 还原真实的机构编码
    private String restoreRealOrgCode(String paddedCode) {
        if (StrUtil.isBlank(paddedCode)) {
            return paddedCode;
        }

        // 1️⃣ 长度合法性校验
        if (paddedCode.length() != 6) {
            log.warn("【机构编码还原】非法输入：长度非6位，原值={}", paddedCode);
            return paddedCode;
        }

        // 2️⃣ 优先匹配直属局（2位补4个零）
        if (paddedCode.endsWith("0000")) {
            String realCode = paddedCode.substring(0, 2);
            log.info("【机构编码还原】直属局编码还原：{} → {}", paddedCode, realCode);
            return realCode;
        }

        // 3️⃣ 匹配分支局（4位补2个零）
        if (paddedCode.endsWith("00") && !paddedCode.endsWith("0000")) {
            String realCode = paddedCode.substring(0, 4);
            log.info("【机构编码还原】分支局编码还原：{} → {}", paddedCode, realCode);
            return realCode;
        }

        // 4️⃣ 默认返回原值（海事处或非法格式）
        log.info("【机构编码还原】海事处或非法格式：{}", paddedCode);
        return paddedCode;
    }



    //判断是否是base64字符串
    private boolean isBase64(String str) {
        if (StrUtil.isBlank(str) || str.length() % 4 != 0) {
            return false;
        }
        try {
            Base64.getDecoder().decode(str); // ✅ 实际尝试解码
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }

 /**
     * 将日期字符串从一种格式转换为另一种格式
     *
     * @param dateStr       输入的日期字符串
     * @return 转换后的日期字符串，如果转换失败则返回原始字符串
     */
    public  String convertDateEn(String dateStr) {
        if (dateStr == null || dateStr.isEmpty()) {
            return "";
        }
        String inputFormat = "yyyy-MM-dd";
        String outputFormat = "d MMM.yyyy";
        Locale locale = Locale.ENGLISH;
        try {
            // 解析输入日期字符串
            DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern(inputFormat);
            LocalDate date = LocalDate.parse(dateStr, inputFormatter);

            // 格式化为目标格式
            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern(outputFormat, locale);
            return date.format(outputFormatter);
        } catch (DateTimeParseException e) {
            // 如果解析失败，记录日志并返回原始字符串
            log.error("日期格式转换失败: {}", dateStr, e);
            return dateStr;
        }
    }

    /**
     * 对海船船员培训合格证书的合格证书组进行排序，并重建编号
     * 每组包括：prefixN、titleOftheCertificate{2N-1}、titleOftheCertificate{2N}、levelN、dateOfIssue{2N-1}、dateOfIssue{2N}、dateOfExpiry{2N-1}、dateOfExpiry{2N}
     * 按照PREFIX_ORDER顺序排序，返回排序后的字段列表，且编号从1递增
     * @param surfaces 原始照面字段列表
     * @return 排序后的照面字段列表
     */
    private List<CertificateDataRegen.Surface> sortSeafarerCertificateGroups(List<CertificateDataRegen.Surface> surfaces) {
        // 1. 提取所有合格证组字段名
        Pattern groupFieldPattern = Pattern.compile("^(prefix\\d+|titleOftheCertificate\\d+|level\\d+|dateOfIssue\\d+|dateOfExpiry\\d+)$");
        // 1.1 合格证组字段
        List<CertificateDataRegen.Surface> certGroupFields = new ArrayList<>();
        // 1.2 其他字段
        List<CertificateDataRegen.Surface> otherFields = new ArrayList<>();
        for (CertificateDataRegen.Surface s : surfaces) {
            if (groupFieldPattern.matcher(s.getName()).matches()) {
                certGroupFields.add(s);
            } else {
                otherFields.add(s);
            }
        }

        // 2. 对合格证组排序并重编号
        List<CertificateDataRegen.Surface> sortedCertGroupFields = sortSeafarerCertificateGroupsOnly(certGroupFields);

        // 3. 合并结果：先加otherFields，再加sortedCertGroupFields
        List<CertificateDataRegen.Surface> result = new ArrayList<>();
        result.addAll(otherFields);
        result.addAll(sortedCertGroupFields);
        return result;
    }

    /**
     * 只对合格证组字段排序并重编号
     */
    private List<CertificateDataRegen.Surface> sortSeafarerCertificateGroupsOnly(List<CertificateDataRegen.Surface> surfaces) {
        // 1. 收集所有prefixN及其组号和prefix值
        Map<String, String> groupNumToPrefixValue = new HashMap<>();
        Pattern prefixPattern = Pattern.compile("^prefix(\\d+)$");
        for (CertificateDataRegen.Surface s : surfaces) {
            Matcher m = prefixPattern.matcher(s.getName());
            if (m.matches()) {
                String groupNum = m.group(1);
                groupNumToPrefixValue.put(groupNum, s.getValue());
            }
        }

        // 2. 按PREFIX_ORDER顺序排序组号
        List<Map.Entry<String, String>> sortedGroups = new ArrayList<>(groupNumToPrefixValue.entrySet());
        sortedGroups.sort((a, b) -> {
            int indexA = PREFIX_ORDER.indexOf(a.getValue());
            int indexB = PREFIX_ORDER.indexOf(b.getValue());
            return Integer.compare(indexA, indexB);
        });

        // 3. 组装排序后的字段，并重建编号
        List<CertificateDataRegen.Surface> result = new ArrayList<>();
        int newIndex = 1;
        for (Map.Entry<String, String> entry : sortedGroups) {
            String oldGroupNum = entry.getKey();
            String prefixValue = entry.getValue();

            // prefixN
            result.add(new CertificateDataRegen.Surface("prefix" + newIndex, prefixValue));
            int oldIdx1 = Integer.parseInt(oldGroupNum) * 2 - 1;
            int oldIdx2 = Integer.parseInt(oldGroupNum) * 2;
            int newIdx1 = newIndex * 2 - 1;
            int newIdx2 = newIndex * 2;

            for (CertificateDataRegen.Surface s : surfaces) {
                if (s.getName().equals("titleOftheCertificate" + oldIdx1)) {
                    result.add(new CertificateDataRegen.Surface("titleOftheCertificate" + newIdx1, s.getValue()));
                }
                if (s.getName().equals("titleOftheCertificate" + oldIdx2)) {
                    result.add(new CertificateDataRegen.Surface("titleOftheCertificate" + newIdx2, s.getValue()));
                }
                if (s.getName().equals("level" + oldGroupNum)) {
                    result.add(new CertificateDataRegen.Surface("level" + newIndex, s.getValue()));
                }
                if (s.getName().equals("dateOfIssue" + oldIdx1)) {
                    result.add(new CertificateDataRegen.Surface("dateOfIssue" + newIdx1, s.getValue()));
                }
                if (s.getName().equals("dateOfIssue" + oldIdx2)) {
                    result.add(new CertificateDataRegen.Surface("dateOfIssue" + newIdx2, s.getValue()));
                }
                if (s.getName().equals("dateOfExpiry" + oldIdx1)) {
                    result.add(new CertificateDataRegen.Surface("dateOfExpiry" + newIdx1, s.getValue()));
                }
                if (s.getName().equals("dateOfExpiry" + oldIdx2)) {
                    result.add(new CertificateDataRegen.Surface("dateOfExpiry" + newIdx2, s.getValue()));
                }
            }
            newIndex++;
        }
        return result;
    }

    public static void main(String[] args) {
        CertificateDataRegenService service = new CertificateDataRegenService();
        List<CertificateDataRegen.Surface> surfaces = new ArrayList<>();

        // 添加非培训合格证相关字段
        surfaces.add(new CertificateDataRegen.Surface("fullNameoftheHolder1", "于谭涛"));
        surfaces.add(new CertificateDataRegen.Surface("fullNameoftheHolder2", "YU TANTAO"));
        surfaces.add(new CertificateDataRegen.Surface("nationality1", "中国"));
        surfaces.add(new CertificateDataRegen.Surface("nationality2", "CHINA"));
        surfaces.add(new CertificateDataRegen.Surface("dateOfBirth1", "1990-01-01"));
        surfaces.add(new CertificateDataRegen.Surface("dateOfBirth2", "1 Jan.1990"));

        // Z系列
        surfaces.add(new CertificateDataRegen.Surface("prefix1", "T06"));
        surfaces.add(new CertificateDataRegen.Surface("titleOftheCertificate1", "船舶保安员培训合格证"));
        surfaces.add(new CertificateDataRegen.Surface("titleOftheCertificate2", "Ship Security Officer Training Certificate"));
        surfaces.add(new CertificateDataRegen.Surface("level1", "VI/1,A-VI/1"));
        surfaces.add(new CertificateDataRegen.Surface("dateOfIssue1", "2020-04-23"));
        surfaces.add(new CertificateDataRegen.Surface("dateOfIssue2", "23 Apr.2020"));
        surfaces.add(new CertificateDataRegen.Surface("dateOfExpiry1", "2025-04-23"));
        surfaces.add(new CertificateDataRegen.Surface("dateOfExpiry2", "23 Apr.2025"));

        surfaces.add(new CertificateDataRegen.Surface("prefix2", "Z02"));
        surfaces.add(new CertificateDataRegen.Surface("titleOftheCertificate3", "精通救生艇筏和救助艇培训合格证"));
        surfaces.add(new CertificateDataRegen.Surface("titleOftheCertificate4", "Proficiency in Survival Craft and Rescue Boats other than Fast Rescue Boats"));
        surfaces.add(new CertificateDataRegen.Surface("level2", "VI/2,A-VI/2"));
        surfaces.add(new CertificateDataRegen.Surface("dateOfIssue3", "2021-04-29"));
        surfaces.add(new CertificateDataRegen.Surface("dateOfIssue4", "29 Apr.2021"));
        surfaces.add(new CertificateDataRegen.Surface("dateOfExpiry3", "2026-04-29"));
        surfaces.add(new CertificateDataRegen.Surface("dateOfExpiry4", "29 Apr.2026"));

        surfaces.add(new CertificateDataRegen.Surface("prefix3", "Z07"));
        surfaces.add(new CertificateDataRegen.Surface("titleOftheCertificate5", "保安意识培训合格证"));
        surfaces.add(new CertificateDataRegen.Surface("titleOftheCertificate6", "Security Awareness Training Certificate"));
        surfaces.add(new CertificateDataRegen.Surface("level3", "VI/6,A-VI/6"));
        surfaces.add(new CertificateDataRegen.Surface("dateOfIssue5", "2020-04-23"));
        surfaces.add(new CertificateDataRegen.Surface("dateOfIssue6", "23 Apr.2020"));
        surfaces.add(new CertificateDataRegen.Surface("dateOfExpiry5", "2053-07-06"));
        surfaces.add(new CertificateDataRegen.Surface("dateOfExpiry6", "6 July.2053"));

        surfaces.add(new CertificateDataRegen.Surface("prefix4", "Z08"));
        surfaces.add(new CertificateDataRegen.Surface("titleOftheCertificate7", "负有指定保安职责船员培训合格证"));
        surfaces.add(new CertificateDataRegen.Surface("titleOftheCertificate8", "Seafarers with Designated Security Duties Certificate"));
        surfaces.add(new CertificateDataRegen.Surface("level4", "VI/6,A-VI/6"));
        surfaces.add(new CertificateDataRegen.Surface("dateOfIssue7", "2020-04-23"));
        surfaces.add(new CertificateDataRegen.Surface("dateOfIssue8", "23 Apr.2020"));
        surfaces.add(new CertificateDataRegen.Surface("dateOfExpiry7", "2053-07-06"));
        surfaces.add(new CertificateDataRegen.Surface("dateOfExpiry8", "6 July.2053"));

        surfaces.add(new CertificateDataRegen.Surface("prefix5", "Z01"));
        surfaces.add(new CertificateDataRegen.Surface("titleOftheCertificate9", "基本安全培训合格证"));
        surfaces.add(new CertificateDataRegen.Surface("titleOftheCertificate10", "Basic Safety Training Certificate"));
        surfaces.add(new CertificateDataRegen.Surface("level5", "VI/1,A-VI/1"));
        surfaces.add(new CertificateDataRegen.Surface("dateOfIssue9", "2025-04-19"));
        surfaces.add(new CertificateDataRegen.Surface("dateOfIssue10", "19 Apr.2025"));
        surfaces.add(new CertificateDataRegen.Surface("dateOfExpiry9", "2030-04-19"));
        surfaces.add(new CertificateDataRegen.Surface("dateOfExpiry10", "19 Apr.2030"));

        // 随机添加T系列证书
        surfaces.add(new CertificateDataRegen.Surface("prefix6", "T01"));
        surfaces.add(new CertificateDataRegen.Surface("titleOftheCertificate11", "油船和化学品船货物操作基本培训合格证"));
        surfaces.add(new CertificateDataRegen.Surface("titleOftheCertificate12", "Basic Training for Oil and Chemical Tanker Cargo Operations Certificate"));
        surfaces.add(new CertificateDataRegen.Surface("level6", "V/1-1-1"));
        surfaces.add(new CertificateDataRegen.Surface("dateOfIssue11", "2022-01-01"));
        surfaces.add(new CertificateDataRegen.Surface("dateOfIssue12", "1 Jan.2022"));
        surfaces.add(new CertificateDataRegen.Surface("dateOfExpiry11", "2027-01-01"));
        surfaces.add(new CertificateDataRegen.Surface("dateOfExpiry12", "1 Jan.2027"));

        surfaces.add(new CertificateDataRegen.Surface("prefix7", "T03"));
        surfaces.add(new CertificateDataRegen.Surface("titleOftheCertificate13", "化学品船货物操作高级培训合格证"));
        surfaces.add(new CertificateDataRegen.Surface("titleOftheCertificate14", "Advanced Training for Chemical Tanker Cargo Operations Certificate"));
        surfaces.add(new CertificateDataRegen.Surface("level7", "V/1-1-2"));
        surfaces.add(new CertificateDataRegen.Surface("dateOfIssue13", "2023-02-02"));
        surfaces.add(new CertificateDataRegen.Surface("dateOfIssue14", "2 Feb.2023"));
        surfaces.add(new CertificateDataRegen.Surface("dateOfExpiry13", "2028-02-02"));
        surfaces.add(new CertificateDataRegen.Surface("dateOfExpiry14", "2 Feb.2028"));

        surfaces.add(new CertificateDataRegen.Surface("prefix8", "T02"));
        surfaces.add(new CertificateDataRegen.Surface("titleOftheCertificate15", "大型船舶操纵特殊培训合格证"));
        surfaces.add(new CertificateDataRegen.Surface("titleOftheCertificate16", "Special Training for Large Ship Handling Certificate"));
        surfaces.add(new CertificateDataRegen.Surface("level8", "V/1-2-1"));
        surfaces.add(new CertificateDataRegen.Surface("dateOfIssue15", "2024-03-03"));
        surfaces.add(new CertificateDataRegen.Surface("dateOfIssue16", "3 Mar.2024"));
        surfaces.add(new CertificateDataRegen.Surface("dateOfExpiry15", "2029-03-03"));
        surfaces.add(new CertificateDataRegen.Surface("dateOfExpiry16", "3 Mar.2029"));

        surfaces.add(new CertificateDataRegen.Surface("prefix9", "T12"));
        surfaces.add(new CertificateDataRegen.Surface("titleOftheCertificate17", "使用气体或其他低闪点燃料船舶船员高级培训合格证"));
        surfaces.add(new CertificateDataRegen.Surface("titleOftheCertificate18", "Advanced Training for Ships Using Gases or Other Low-flashpoint Fuels Certificate"));
        surfaces.add(new CertificateDataRegen.Surface("level9", "V/1-2-2"));
        surfaces.add(new CertificateDataRegen.Surface("dateOfIssue17", "2025-04-04"));
        surfaces.add(new CertificateDataRegen.Surface("dateOfIssue18", "4 Apr.2025"));
        surfaces.add(new CertificateDataRegen.Surface("dateOfExpiry17", "2030-04-04"));
        surfaces.add(new CertificateDataRegen.Surface("dateOfExpiry18", "4 Apr.2030"));

        surfaces.add(new CertificateDataRegen.Surface("prefix10", "T09"));
        surfaces.add(new CertificateDataRegen.Surface("titleOftheCertificate19", "极地水域船舶操作船员高级培训合格证"));
        surfaces.add(new CertificateDataRegen.Surface("titleOftheCertificate20", "Advanced Training for Polar Waters Operations Certificate"));
        surfaces.add(new CertificateDataRegen.Surface("level10", "V/1-2-3"));
        surfaces.add(new CertificateDataRegen.Surface("dateOfIssue19", "2026-05-05"));
        surfaces.add(new CertificateDataRegen.Surface("dateOfIssue20", "5 May.2026"));
        surfaces.add(new CertificateDataRegen.Surface("dateOfExpiry19", "2031-05-05"));
        surfaces.add(new CertificateDataRegen.Surface("dateOfExpiry20", "5 May.2031"));

        // 调用排序方法
        List<CertificateDataRegen.Surface> sorted = service.sortSeafarerCertificateGroups(surfaces);

        // 打印排序结果
        System.out.println("排序后照面字段：");
        for (CertificateDataRegen.Surface s : sorted) {
            System.out.printf("%-25s : %s\n", s.getName(), s.getValue());
        }
    }

    /**
     * 处理证照生成结果回调
     * @param certificateId 证照ID
     * @param code 回调状态码
     * @param msg 回调消息
     * @return 处理结果
     */
    public String handleCertificateCallback(String certificateId, String code, String msg, boolean flag) {
        try {
            if(!flag){
                log.info("【证照回调Service】船员系统回调请求，只打印日志不处理，certificateId：{}，code：{}，msg：{}", certificateId, code, msg);
                return "处理成功";
            }

            log.info("【证照回调Service】开始处理回调请求，certificateId：{}，code：{}，msg：{}", certificateId, code, msg);

            // 参数校验
            if (StrUtil.isBlank(certificateId)) {
                log.error("【证照回调Service】证照ID不能为空");
                throw new IllegalArgumentException("证照ID不能为空");
            }

            if (StrUtil.isBlank(code)) {
                log.error("【证照回调Service】状态码不能为空");
                throw new IllegalArgumentException("状态码不能为空");
            }

            if (StrUtil.isBlank(msg)) {
                log.error("【证照回调Service】消息不能为空");
                throw new IllegalArgumentException("消息不能为空");
            }

            // 根据certificateId查询证照数据
            CertificateDataRegen certificateData = getByCertificateId(certificateId);
            if (certificateData == null) {
                log.error("【证照回调Service】未找到对应的证照数据，certificateId：{}", certificateId);
                throw new IllegalArgumentException("未找到对应的证照数据");
            }

            // 根据code值更新数据库
            if ("0".equals(code)) {
                // code = 0，只修改result_desc字段
                certificateData.setResultDesc(msg);
                log.info("【证照回调Service】更新证照数据，只修改result_desc字段，certificateId：{}，msg：{}", certificateId, msg);
            } else if ("1".equals(code)) {
                // code = 1，修改create_status = 2，result_desc字段
                certificateData.setCreateStatus("2");
                certificateData.setResultDesc(msg);
                // 转板失败的状态改为 3
                if (StrUtil.equalsAny(msg,"文件转版失败","签章失败，签章接口返回信息为:","签名失败,调用签章接口返回值为：")) {
                    certificateData.setCreateStatus("3");
                }
                log.info("【证照回调Service】更新证照数据，修改create_status和result_desc字段，certificateId：{}，msg：{}", certificateId, msg);
            } else {
                log.error("【证照回调Service】无效的状态码：{}", code);
                throw new IllegalArgumentException("无效的状态码");
            }

            // 保存更新后的数据
            updateById(certificateData);
            log.info("【证照回调Service】证照数据更新成功，certificateId：{}", certificateId);

            // 通知船员系统
            try {
                notifyCrewSystem(certificateId, code, msg);
                log.info("【证照回调Service】船员系统通知成功，certificateId：{}", certificateId);
            } catch (Exception e) {
                log.error("【证照回调Service】通知船员系统失败，certificateId：{}，错误：{}", certificateId, e.getMessage(), e);
                // 通知失败不影响主流程，只记录日志
            }

            return "处理成功";
        } catch (Exception e) {
            log.error("【证照回调Service】处理回调请求异常", e);
            throw e;
        }
    }

    /**
     * 通知船员系统
     * @param certificateId 证照ID
     * @param callbackCode 回调状态码
     * @param callbackMsg 回调消息
     */
    private void notifyCrewSystem(String certificateId, String callbackCode, String callbackMsg) {
        try {
            // 使用配置文件中的URL
            String crewSystemUrl = crewSystemNotificationUrl;

            // 构建请求参数
            CrewNotificationRequest request = new CrewNotificationRequest(certificateId, callbackCode, callbackMsg);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 创建请求实体
            HttpEntity<CrewNotificationRequest> requestEntity = new HttpEntity<>(request, headers);

            log.info("【证照回调Service】开始通知船员系统，URL：{}，参数：{}", crewSystemUrl, request);

            // 发送POST请求
            ResponseEntity<String> response = restTemplate.postForEntity(crewSystemUrl, requestEntity, String.class);

            log.info("【证照回调Service】船员系统响应，状态码：{}，响应内容：{}", response.getStatusCode(), response.getBody());

        } catch (Exception e) {
            log.error("【证照回调Service】通知船员系统异常", e);
            throw e;
        }
    }

}
