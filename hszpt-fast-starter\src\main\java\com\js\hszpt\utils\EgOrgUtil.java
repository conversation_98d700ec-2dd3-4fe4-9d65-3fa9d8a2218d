package com.js.hszpt.utils;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.js.hszpt.entity.SysDeptEn;

import java.util.HashMap;
import java.util.Map;

public class EgOrgUtil {

    /**
     * 电子证照生成机构信息
     * @param sysDeptEn 中英文机构信息表
     * @param flag 是否国际航线 true-是 false-否
     * @return 机构信息map
     */
    public static Map<String, String> getEgOrgMap(SysDeptEn sysDeptEn, boolean flag) {
        // X海事局(中文)
        String orgName = StrUtil.isNotBlank(sysDeptEn.getOrgName()) ? sysDeptEn.getOrgName() : "--";
        // X海事局(英文)
        String orgNameEn = flag && StrUtil.isNotBlank(sysDeptEn.getOrgNameEn()) ?
                sysDeptEn.getOrgNameEn().replaceAll("&", "＆") : " ";
        // 地点(中文)
        String orgAddr = sysDeptEn.getOrgAddr() != null ? sysDeptEn.getOrgAddr() : "--";
        // 地点(英文)
        String orgAddrEn = flag && StrUtil.isNotBlank(sysDeptEn.getOrgAddrEn()) ?
                sysDeptEn.getOrgAddrEn().replaceAll("&", "＆") : " ";
        return MapUtil.builder(new HashMap<String,String>())
                .put("X海事局(中文)",orgName)
                .put("X海事局(英文)", orgNameEn)
                .put("地点(中文)",orgAddr)
                .put("地点(英文)", orgAddrEn)
                .build();
    }
}
