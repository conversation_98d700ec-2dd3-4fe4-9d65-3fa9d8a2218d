package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 电子政务-人员用户信息
 */

@Data
@TableName("dict_eg_user")
public class DictEgUser {

    /**
     * 用户标识
     */
    private Long userId;

    /**
     * 账号标志（主键）
     */
    @TableId(value = "account_id")
    private String accountId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 密码
     */
    private String passwd;

    /**
     * 随机盐
     */
    private String salt;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 海事机构代码 DICT_EG_ORG
     */
    private Long orgCode;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    /**
     * 创建日期
     */
    private Date createDate;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    /**
     * 修改日期
     */
    private Date modifyDate;

    /**
     * 锁定标志
     */
    private String lockFlag;

    /**
     * 删除标志代码 0：正常；1：删除
     */
    private String deleteFlagCode;

    /**
     * 微信号码
     */
    private String wechat;

    /**
     * QQ号码
     */
    private String qq;

    /**
     * 证件号码
     */
    private String idcardNo;

    /**
     * 身份识别号散列码
     */
    private String certKey;

    /**
     * 用户类型代码（主键）
     */
    @TableId(value = "user_type_code")
    private String userTypeCode;

    /**
     * 法人类型
     */
    private String legalType;

    /**
     * 代理人
     */
    private String legalName;

    /**
     * 用户令牌
     */
    private String tokenSno;

    /**
     * 主账户（管理员）
     */
    private String mainAccountId;

    /**
     * 电子邮箱
     */
    private String email;

    /**
     * 生效时间
     */
    private String effDate;

    /**
     * 失效时间
     */
    private String expDate;

    /**
     * 账号描述
     */
    private String accountDesc;

    /**
     * 4A侧用户标识
     */
    private String mainUserId;

    /**
     * 性别
     */
    private String gender;

    /**
     * 联系地址
     */
    private String contactAddr;

    /**
     * 身份证类型
     */
    private String certType;

    /**
     * 经办人身份证
     */
    private String legalCertNo;

    /**
     * 从账户类型
     */
    private String subAccountType;

    /**
     * 从账号标识
     */
    private String subAccountId;

    /**
     * 机构部门代码
     */
    private String deptCode;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    /**
     * 记录创建日期
     */
    private Date recCreateDate;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    /**
     * 记录修改日期
     */
    private Date recModifyDate;
}
