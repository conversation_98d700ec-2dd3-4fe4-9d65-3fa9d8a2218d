//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fernflower decompiler)
//

package com.js.sys.msg.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.js.cache.msg.BasePubMessage;
import com.js.sys.entity.LogOperBusiness;
import com.js.sys.msg.service.BaseSub;
import com.js.sys.service.LogOperBusinessService;
import lombok.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class BusinessOperLogSub implements BaseSub {
    @Generated
    private static final Logger log = LoggerFactory.getLogger(BusinessOperLogSub.class);
    @Autowired
    private LogOperBusinessService operBusinessService;

    public BusinessOperLogSub() {
    }

    public void receiveMessage(String jsonMessage) {


    }
}
