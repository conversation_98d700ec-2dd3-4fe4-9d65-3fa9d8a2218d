package com.js.hszpt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.js.api.entity.User;
import com.js.hszpt.dto.SysUserDto;
import com.js.hszpt.entity.SysUser;
import com.js.hszpt.vo.SysUserVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 
 * @ClassName:  SysUser   
 * @Description:TODO(用户表数据处理层)   
 * @author:   System Generation 
 */
public interface SysUserDao extends BaseMapper<SysUser> {

//    @Select("SELECT * FROM t_user WHERE id = #{currentUserId}")
//    User getUserByUserName(String currentUserId);

//    @Select("SELECT * FROM t_user WHERE username = #{currentUserName}")
//    User getUserById(String currentUserName);
//

//    IPage<SysUserVo> selectUserListForWorkflow(
//            @Param("page") Page<SysUserVo> page,
//            @Param("dto") SysUserDto sysUserDto
//    );

    @Select("SELECT * FROM dzzz_sys_user WHERE user_id = #{currentUserId}")
    SysUser get(String currentUserId);

//    @Select("SELECT * FROM t_user WHERE username = #{currentUserName}")
//    User getUserById(String currentUserName);

    IPage<SysUserVo> selectUserListForWorkflow(@Param("page") Page<SysUserVo> page,
                                               @Param("dto") SysUserDto sysUserDto);

//    @Select("SELECT data_org_id FROM t_user WHERE username = #{username}")
//    String selectOrgCode(String username);

    @Select("SELECT name FROM sys_dept_iamb WHERE code = #{orgCode}")
    String selectDeptId(String username);

    //通过身份证号码查找改人员的所在部门
    @Select("SELECT duty_code FROM dzzz_sys_user WHERE cert_no = #{certNo}")
    String selectDutyCode(String certNo);
}