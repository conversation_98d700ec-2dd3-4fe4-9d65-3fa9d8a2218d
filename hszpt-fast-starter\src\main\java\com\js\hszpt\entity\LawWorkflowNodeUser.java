package com.js.hszpt.entity;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 工作流用户信息(LawWorkflowNodeUser)实体类
 *
 * <AUTHOR>
 * @since 2022-08-12 10:09:00
 */
@Entity
@Data
@Accessors(chain = true)
@Table(name = "LAW_WORKFLOW_NODE_USER")
@ApiModel(value = "工作流用户信息", description = "工作流用户信息-添加工作流用户信息实体OR工作流用户信息多条件查询")
public class LawWorkflowNodeUser {
    private static final long serialVersionUID = 851288587037793104L;
    
    @Id
    @Column(name = "LAW_WORKFLOW_NODE_USER_ID")
//    @Generator("snowFlakeGenerator")
    @ApiModelProperty(value = "LAW_WORKFLOW_NODE_USER_ID", notes = "LAW_WORKFLOW_NODE_USER_ID")
    private String lawWorkflowNodeUserId;


    @Column(name = "LAW_WORKFLOW_NODE_SET_ID")
    @ApiModelProperty(value = "工作流模板ID", notes = "工作流模板ID")
    private String lawWorkflowNodeSetId;

    @Column(name = "LAW_WORKFLOW_NODE_INFO_ID")
    @ApiModelProperty(value = "工作流模板节点ID", notes = "工作流模板节点ID")
    private String lawWorkflowNodeInfoId;

    @Column(name = "USER_CODE")
    @ApiModelProperty(value = "sysUser表UserName", notes = "sysUser表UserName")
    private String userCode;

    @Column(name = "USER_NAME")
    @ApiModelProperty(value = "sysUser表name", notes = "sysUser表name")
    private String userName;

    @Column(name = "CREATE_OPER_ID")
    @ApiModelProperty(value = "创建人", notes = "创建人")
    private String createOperId;

    @Column(name = "CREATE_OPER_DEPT")
    @ApiModelProperty(value = "创建部门", notes = "创建部门")
    private String createOperDept;

    @Column(name = "CREATE_TIME")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ApiModelProperty(value = "创建时间", notes = "创建时间")
    private Date createTime;

    @Column(name = "MODIFY_OPER_ID")
    @ApiModelProperty(value = "修改人", notes = "修改人")
    private String modifyOperId;

    @Column(name = "MODIFY_OPER_DEPT")
    @ApiModelProperty(value = "修改部门", notes = "修改部门")
    private String modifyOperDept;

    @Column(name = "RECORD_VERSION")
//    @Version
    @ApiModelProperty(value = "版本号", notes = "版本号")
    private String recordVersion;

    @Column(name = "ORG_CODE")
    @ApiModelProperty(value = "海事机构", notes = "海事机构")
    private String orgCode;

    @Column(name = "LAW_WORKFLOW_NODE_ORG_ID")
    @ApiModelProperty(value = "工作流模板节点机构自定义配置表ID", notes = "工作流模板节点机构自定义配置表ID")
    private String lawWorkflowNodeOrgId;

    @Column(name = "CERT_NO")
    @ApiModelProperty(value = "用户身份证号码", notes = "用户身份证号码")
    private String certNo;

    @Column(name = "MODIFY_TIME")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ApiModelProperty(value = "修改时间", notes = "修改时间")
    private Date modifyTime;

}
