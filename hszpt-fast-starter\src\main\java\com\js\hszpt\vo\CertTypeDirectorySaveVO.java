package com.js.hszpt.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@ApiModel("证照类型目录保存对象")
public class CertTypeDirectorySaveVO {
	
    @ApiModelProperty("证照类型目录ID(编辑时传入)")
    private String certTypeDirId;
	
    @NotBlank(message = "证照类型代码不能为空")
    @ApiModelProperty("证照类型代码")
    private String certificateTypeCode;
	
    @NotBlank(message = "证照类型名称不能为空")
    @ApiModelProperty("证照类型名称")
    private String certificateTypeName;
	
    @NotBlank(message = "证照定义机构不能为空")
    @ApiModelProperty("证照定义机构")
    private String createOrgName;
	
    //@NotBlank(message = "关联事项名称不能为空")
    @ApiModelProperty("关联事项名称")
    private String relatedItemName;
	
    //@NotBlank(message = "关联事项代码不能为空")
    @ApiModelProperty("关联事项代码")
    private String relatedItemCode;
	
    @NotBlank(message = "持证主体类别不能为空")
    @ApiModelProperty("持证主体类别")
    private String certificateHolderCategory;
	
    //@NotBlank(message = "有效期限不能为空")
    @ApiModelProperty("有效期限")
    private String validityRange;
	
    @NotNull(message = "是否提交不能为空")
    @ApiModelProperty("是否提交(true-提交 false-保存)")
    private Boolean submit;

    @NotNull(message = "父级证照类型id不能为空")
    @ApiModelProperty("父级证照类型id")
    private String parentId;

	@NotNull(message = "证照定义机构代码不能为空")
    @ApiModelProperty("证照定义机构代码")
    private String createOrgCode;

	@NotNull(message = "定义机构统一社会信用代码不能为空")
    @ApiModelProperty("定义机构统一社会信用代码，18位，符合GB32100")
    private String certificateDefineAuthorityCode;
}
