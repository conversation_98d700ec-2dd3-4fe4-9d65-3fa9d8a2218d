package com.js.hszpt.vo;

import javax.persistence.*;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/8/11 18:29
 */
@Entity
@Table(name = "LAW_WORKFLOW_NODE_INFO")
public class LawWorkflowNodeInfoTreeVo{

    /**
     * 节点Id
     */
    @Id
//    @Generator("snowFlakeGenerator")
    @Column(name = "LAW_WORKFLOW_NODE_INFO_ID")
    private String lawWorkflowNodeInfoId;

    /**
     * 模板Id
     */
    @Column(name = "LAW_WORKFLOW_NODE_SET_ID")
    private String lawWorkflowNodeSetId;

    /**
     * 节点名称
     */
    @Column(name = "NODE_NAME")
    private String nodeName;

    public String getNodeName() {
        return nodeName;
    }

    public String getNodeOrgLevel() {
        return nodeOrgLevel;
    }

    public void setNodeOrgLevel(String nodeOrgLevel) {
        this.nodeOrgLevel = nodeOrgLevel;
    }

    @Column(name = "NODE_ORG_LEVEL")
    private String nodeOrgLevel;

    @Transient
    private String itemsName;

    public String getLawWorkflowNodeInfoId() {
        return lawWorkflowNodeInfoId;
    }

    public void setLawWorkflowNodeInfoId(String lawWorkflowNodeInfoId) {
        this.lawWorkflowNodeInfoId = lawWorkflowNodeInfoId;
    }

    public String getLawWorkflowNodeSetId() {
        return lawWorkflowNodeSetId;
    }

    public void setLawWorkflowNodeSetId(String lawWorkflowNodeSetId) {
        this.lawWorkflowNodeSetId = lawWorkflowNodeSetId;
    }

    public String getItemsName() {
        return itemsName= nodeName;
    }

    public void setItemsName(String itemsName) {
        this.itemsName = itemsName;
    }

    public void setNodeName(String nodeName) {
        this.nodeName = nodeName;
    }
}
