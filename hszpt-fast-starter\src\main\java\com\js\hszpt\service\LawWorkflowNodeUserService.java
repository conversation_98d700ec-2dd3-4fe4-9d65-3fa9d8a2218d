package com.js.hszpt.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.js.common.entity.CurrentUser;
import com.js.hszpt.dto.LawWorkflowNodeUserDTO;
import com.js.hszpt.dto.LawWorkflowNodeUserListDto;
import com.js.hszpt.entity.SysDeptIamb;
import com.js.hszpt.entity.SysUser;
import com.js.hszpt.mapper.LawWorkflowNodeUserDao;
import com.js.hszpt.entity.LawWorkflowNodeUser;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.core.common.utils.PageUtil;
import com.js.core.common.vo.PageVo;
import com.js.core.common.vo.SearchVo;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;

import java.util.*;
import java.util.stream.Collectors;

import com.js.hszpt.mapper.SysDeptIambDao;
import com.js.hszpt.mapper.SysUserDao;
import com.js.hszpt.until.SessionUtils;
import com.js.hszpt.vo.LawWorkflowNodeUserListVo;
import com.js.hszpt.vo.SysUserVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
 
/**
 * 
 * @ClassName:  LawWorkflowNodeUserService    
 * @Description:TODO(工作流用户信息接口实现)   
 * @author:  System Generation
 *
 */
@Slf4j
@Service
public class LawWorkflowNodeUserService extends ServiceImpl<LawWorkflowNodeUserDao,LawWorkflowNodeUser> {
	@Autowired
	private LawWorkflowNodeUserDao lawWorkflowNodeUserDao;
	@Autowired
	private SysUserDao sysUserDao;
	@Autowired
	private SysDeptIambDao sysDeptIambDao;
	@Autowired
	private SysUserService sysUserService;
	/**
	 * 多条件分页查询
	 * @param param
	 * @param searchVo
	 * @param pageVo
	 * @return
	 */
	public Page<LawWorkflowNodeUser> findByCondition(LawWorkflowNodeUser param, SearchVo searchVo, PageVo pageVo) {
		Page<LawWorkflowNodeUser> page = PageUtil.initMpPage(pageVo);
		QueryWrapper<LawWorkflowNodeUser> queryWrapper = this.getCondition(param,searchVo);
		return this.page(page, queryWrapper);
	}
	
	/**
	 * 
	 * @Title: findByCondition   
	 * @Description: TODO(多条件列表查询)   
	 * @param param
	 * @param searchVo
	 * @return  List<LawWorkflowNodeUser>      
	 * @throws
	 */
	public List<LawWorkflowNodeUser> findByCondition(LawWorkflowNodeUser param, SearchVo searchVo){
		QueryWrapper<LawWorkflowNodeUser> queryWrapper = this.getCondition(param, searchVo);
		//添加排序字段
		queryWrapper.orderByAsc("create_time");
		
		return this.list(queryWrapper);
	}
	
	
	/**
	 * 
	 * @Title: getCondition   
	 * @Description: TODO(构建自定义查询条件)   
	 * @param param
	 * @param searchVo
	 * @return  QueryWrapper<LawWorkflowNodeUser>      
	 * @throws
	 */
	private QueryWrapper<LawWorkflowNodeUser> getCondition(LawWorkflowNodeUser param, SearchVo searchVo){
		QueryWrapper<LawWorkflowNodeUser> queryWrapper = new QueryWrapper<LawWorkflowNodeUser>();
		//请根据实际字段自行扩展
		 
		// 创建时间
		if (StrUtil.isNotBlank(searchVo.getStartDate()) && StrUtil.isNotBlank(searchVo.getEndDate())) {
			Date start = DateUtil.parse(searchVo.getStartDate());
			Date end = DateUtil.parse(searchVo.getEndDate());
			queryWrapper.between("create_time",start, DateUtil.endOfDay(end));
		}
		return queryWrapper;
		
	}

	/**
	 * 查询当前流程节点用户
	 * @param lawWorkflowNodeUserListDto
	 * @return
	 */
	public Map<String, Object> selectJgWorkflowNodeUser(LawWorkflowNodeUserListDto lawWorkflowNodeUserListDto) {

		//设置分页
		Page<LawWorkflowNodeUserListVo> page = new Page<>(
				lawWorkflowNodeUserListDto.getCurrentPage(),
				lawWorkflowNodeUserListDto.getPageSize()
		);

		if (lawWorkflowNodeUserListDto.getOrgCode() == null || lawWorkflowNodeUserListDto.getOrgCode().isEmpty()) {
			throw new IllegalArgumentException("机构编码不能为空");
		}
		//判断前端提交过来的机构数组是不是只有一个
		if (lawWorkflowNodeUserListDto.getOrgCode().size() == 1) {
			// 获取集合中的第一个元素
			String orgCode = lawWorkflowNodeUserListDto.getOrgCode().get(0);

			// 判断是否以 "bj" 结尾，是否是本级，如果是本级需要去掉bj，只查询本级的用户数据
			if (orgCode.endsWith("bj")) {
				// 去掉最后两位 "bj"
				String resultOrgCode = orgCode.substring(0, orgCode.length() - 2);
				// 设置新的 orgCode
				lawWorkflowNodeUserListDto.setOrgCode(Collections.singletonList(resultOrgCode));
				//查询本级数据
				Page<LawWorkflowNodeUserListVo> resultPage = lawWorkflowNodeUserDao.selectWorkflowNodeUserbj(page, lawWorkflowNodeUserListDto);
				// 返回结果
				Map<String, Object> result = new HashMap<>();
				result.put("records", resultPage.getRecords());
				result.put("total", resultPage.getTotal());
				return result;
			}
		}

		// 如果机构数组有大量的数据，获取集合中的第一个元素，看看是不是有本级，有本级需要去掉bj，然后查询机构数组中的用户数据
		String orgCode = lawWorkflowNodeUserListDto.getOrgCode().get(0);

		// 判断是否以 "bj" 结尾
		if (orgCode.endsWith("bj")) {
			// 去掉最后两位 "bj"
			String resultOrgCode = orgCode.substring(0, orgCode.length() - 2);
			// 设置新的 orgCode
			lawWorkflowNodeUserListDto.setOrgCode(Collections.singletonList(resultOrgCode));
		}
		Page<LawWorkflowNodeUserListVo> resultPage = lawWorkflowNodeUserDao.selectWorkflowNodeUser(page, lawWorkflowNodeUserListDto);

		// 返回结果
		Map<String, Object> result = new HashMap<>();
		result.put("records", resultPage.getRecords());
		result.put("total", resultPage.getTotal());
		return result;
	}

	/**
	 * 查询本级和下级用户
	 * @param lawWorkflowNodeUserListDto
	 * @return
	 */
	public Map<String, Object> JgWorkflowNodeSetUser(LawWorkflowNodeUserListDto lawWorkflowNodeUserListDto) {
		// 创建分页对象
		Page<LawWorkflowNodeUserListVo> page = new Page<>(
				lawWorkflowNodeUserListDto.getCurrentPage(),
				lawWorkflowNodeUserListDto.getPageSize()
		);
		// 如果 orgCode只有一个数据，判断是否是本级
		if (lawWorkflowNodeUserListDto.getOrgCode().size() == 1) {
			// 检查 orgCode 是否非空，并且包含至少一个元素
			if (lawWorkflowNodeUserListDto.getOrgCode() != null
					&& !lawWorkflowNodeUserListDto.getOrgCode().isEmpty()) {

				// 获取集合中的第一个元素
				String orgCode = lawWorkflowNodeUserListDto.getOrgCode().get(0);

				// 判断是否以 "bj" 结尾
				if (orgCode.endsWith("bj")) {
					// 去掉最后两位 "bj"
					String resultOrgCode = orgCode.substring(0, orgCode.length() - 2);
					// 设置新的 orgCode
					lawWorkflowNodeUserListDto.setOrgCode(Collections.singletonList(resultOrgCode));
					// 执行查询
					Page<LawWorkflowNodeUserListVo> resultPage = lawWorkflowNodeUserDao.selectUserForWorkflowSet2(page, lawWorkflowNodeUserListDto);
					// 返回结果
					Map<String, Object> result = new HashMap<>();
					result.put("records", resultPage.getRecords());
					result.put("total", resultPage.getTotal());
					return result;
				}
			}
			// 执行查询
			Page<LawWorkflowNodeUserListVo> resultPage = lawWorkflowNodeUserDao.selectUserForWorkflowSet(page, lawWorkflowNodeUserListDto);
			// 返回结果
			Map<String, Object> result = new HashMap<>();
			result.put("records", resultPage.getRecords());
			result.put("total", resultPage.getTotal());
			return result;
		}else {
			// 获取 orgCode 集合
			List<String> orgCodeList = lawWorkflowNodeUserListDto.getOrgCode();
			if (orgCodeList != null && !orgCodeList.isEmpty()) {
				// 新集合用于存储处理后的 orgCode
				List<String> processedOrgCodeList = new ArrayList<>();
				// 遍历 orgCode 集合
				for (String orgCode : orgCodeList) {
					if (orgCode != null && orgCode.endsWith("bj")) {
						// 如果以 "bj" 结尾，移除最后两位 "bj"
						String processedOrgCode = orgCode.substring(0, orgCode.length() - 2);
						processedOrgCodeList.add(processedOrgCode);
					} else {
						// 如果不以 "bj" 结尾，直接保留原值
						processedOrgCodeList.add(orgCode);
					}
				}
				// 将处理后的 orgCode 设置回 DTO
				lawWorkflowNodeUserListDto.setOrgCode(processedOrgCodeList);
			}
			// 执行查询
			Page<LawWorkflowNodeUserListVo> resultPage = lawWorkflowNodeUserDao.selectUserForWorkflowSet2(page, lawWorkflowNodeUserListDto);
			// 返回结果
			Map<String, Object> result = new HashMap<>();
			result.put("records", resultPage.getRecords());
			result.put("total", resultPage.getTotal());
			return result;
		}

	}

	/**
	 * 添加用户权限
	 * @param lawWorkflowNodeUserDTOS
	 * @return
	 */
	public String insertLawWorkflowNodeUser(List<LawWorkflowNodeUserDTO> lawWorkflowNodeUserDTOS) {

		CurrentUser currUser = sysUserService.getCurrUser();
		if (currUser == null) {
			throw new IllegalArgumentException("获取当前用户信息失败");
		}
		String orgCode=sysUserService.getCurrOrgCode();
		//通过用户code来获取部门名字
		String deptId=sysUserService.selectDeptId(orgCode);


		Date date = new Date();
		//查该节点下所有用户，新增的人中若存在相同的，则把重复的人从新增集合里面删除
		String lawWorkflowNodeSetId = lawWorkflowNodeUserDTOS.get(0).getLawWorkflowNodeSetId();
		String lawWorkflowNodeInfoId = lawWorkflowNodeUserDTOS.get(0).getLawWorkflowNodeInfoId();
//        lawWorkflowNodeUserDTOS = lawWorkflowNodeUserDTOS.stream().distinct().collect(Collectors.toList());
		lawWorkflowNodeUserDTOS.forEach(l -> {

			//查询所属部门编码
//			String dutyCode=l.getCertNo()+'-'+sysUserService.selectDutyCode(l.getCertNo());
//			SysDeptIamb sysDeptIamb= sysDeptIambDao.get(deptId);
			l.setUserCode(l.getUserCode());
			l.setOrgCode(orgCode);
			l.setLawWorkflowNodeUserId(UUID.randomUUID().toString());
			l.setCreateOperDept(deptId);
			l.setModifyOperDept(deptId);
			l.setCreateTime(date);
			l.setModifyTime(date);
			l.setCreateOperId(currUser.getNickName());
			l.setModifyOperId(currUser.getNickName());
			l.setLawWorkflowNodeOrgId(UUID.randomUUID().toString());
		});
		// 获取机构代码
//		String orgCode = lawWorkflowNodeUserDTOS.get(0).getOrgCode();

		// 如果相关ID和机构代码都不为空，则进行重复检查
		if (lawWorkflowNodeSetId != null && lawWorkflowNodeInfoId != null && orgCode != null) {
			// 查询已存在的用户列表
			List<LawWorkflowNodeUser> originalUserList = this.list(
					new LambdaQueryWrapper<LawWorkflowNodeUser>()
							.select(LawWorkflowNodeUser::getUserCode)  // 只查询userCode字段
							.eq(LawWorkflowNodeUser::getLawWorkflowNodeInfoId, lawWorkflowNodeInfoId)
							.eq(LawWorkflowNodeUser::getLawWorkflowNodeSetId, lawWorkflowNodeSetId)
							.eq(LawWorkflowNodeUser::getOrgCode, orgCode)
			);

			// 如果找到已存在的用户，从待插入列表中移除
			if (!originalUserList.isEmpty()) {
				// 获取已存在用户的userCode列表
				Set<String> existingUserCodes = originalUserList.stream()
						.map(LawWorkflowNodeUser::getUserCode)
						.collect(Collectors.toSet());

				// 从待插入列表中移除已存在的用户
				lawWorkflowNodeUserDTOS.removeIf(dto ->
						existingUserCodes.contains(dto.getUserCode()));
			}
		}
		if (lawWorkflowNodeUserDTOS.size()==0) {
			return "保存失败";
		}
		int saveRow = lawWorkflowNodeUserDao.batchSaveWorkflowNodeUsers(lawWorkflowNodeUserDTOS);
		if (saveRow < 1) {
			return "保存失败";
		}
		return "您已保存成功";
	}

	/**
	 * 删除用户权限
	 * @param lawWorkflowNodeUserIds
	 * @return
	 */
	public boolean deleteLawWorkflowNodeUser(List<String> lawWorkflowNodeUserIds) {
		//判断前端是否有传递数据
		if (lawWorkflowNodeUserIds == null || lawWorkflowNodeUserIds.isEmpty()) {
			return false;
		}
		try {
			int result = lawWorkflowNodeUserDao.deleteBylawWorkflowNodeUserId(lawWorkflowNodeUserIds);
			// 只要有记录被删除就认为成功
			return result >= 0;
		} catch (Exception e) {
			log.error("删除工作流节点用户失败，IDs: {}", String.join(",", lawWorkflowNodeUserIds), e);
			return false;
		}
	}
}
