package com.js.hszpt.vo;

import com.js.core.common.vo.PageVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "证照类型目录查询对象")
public class CertTypeDirectoryQueryVO extends PageVo {

    @ApiModelProperty(value = "证照类型代码")
    private String certTypeCode;

    @ApiModelProperty(value = "证照名称列表")
    private String certName;

    @ApiModelProperty(value = "证照定义机构列表")
    private String orgName;

    @ApiModelProperty(value = "关联事项名称")
    private String affairName;

    @ApiModelProperty(value = "关联事项代码")
    private String affairNo;

    @ApiModelProperty(value = "持证主体类别")
    private String certificateHolderCategory;

    @ApiModelProperty(value = "有效期限范围")
    private String validPeriod;

    @ApiModelProperty(value = "审批状态")
    private String approvalStatus;

    @ApiModelProperty(value = "下发状态")
    private String issueStatus;

    @ApiModelProperty(value = "申请编号")
    private String applyNo;

    @ApiModelProperty(value = "申请机构")
    private String applyOrgCode;

    @ApiModelProperty(value = "申请类型")
    private String applyType;


    @ApiModelProperty(value = "发布机构")
    private String publishOrgCode;

    @ApiModelProperty(value = "受理机构")
    private String acceptOrgCode;

    @ApiModelProperty(value = "审批机构")
    private String approvalOrgCode;

    @ApiModelProperty(value = "审批开始日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date approvalTimeStart;

    @ApiModelProperty(value = "审批结束日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date approvalTimeEnd;

    @ApiModelProperty(value = "受理开始日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date acceptTimeStart;

    @ApiModelProperty(value = "受理结束日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date acceptTimeEnd;

    @ApiModelProperty(value = "下发开始日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date issueDateStart;

    @ApiModelProperty(value = "下发结束日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date issueDateEnd;

    @ApiModelProperty(value = "创建开始日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeStart;

    @ApiModelProperty(value = "创建结束日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeEnd;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "证照目录ID")
    private String certTypeId;

    @ApiModelProperty(value = "证照类型ID")
    private String parentId;
}
