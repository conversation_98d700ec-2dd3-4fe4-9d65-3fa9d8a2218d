package com.js.hszpt.vo;

import java.io.Serializable;

/**
 * 电子证照服务参数对象
 * @Author: Yangkl_欧巴
 * @Date: 2019-09-12 16:46 星期四
 */
public class Surface implements Serializable {
    private static final long serialVersionUID = 4084081538027878972L;
    private String name;
    private String value;
    private String valueType;
    private String columnName;

    public Surface() {
    }
    public Surface(String name, String value) {
        this.name = name;
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        if (value == null) {
            this.value = "";
        } else {
            this.value = value;
        }
    }

    public String getValueType() {
        return valueType;
    }

    public void setValueType(String valueType) {
        this.valueType = valueType;
    }

    public String getColumnName() {
        return columnName;
    }

    public void setColumnName(String columnName) {
        this.columnName = columnName;
    }
}
