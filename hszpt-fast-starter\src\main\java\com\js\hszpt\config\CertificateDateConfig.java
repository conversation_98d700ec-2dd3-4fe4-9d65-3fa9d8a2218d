package com.js.hszpt.config;

import cn.hutool.core.date.DateUtil;
import com.js.hszpt.builder.CertificateInfoBuilder;
import com.js.hszpt.enmus.CertificateInfo;
import com.js.hszpt.entity.*;
import com.js.hszpt.service.*;
import com.js.hszpt.vo.CertificateVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.js.hszpt.enmus.CertificateCreateStatus.UN_CREATE;

@Component
@Slf4j
public class CertificateDateConfig {

    @Autowired
    private DictEgUserService dictEgUserService;

    @Autowired
    private DictEgOrgService dictEgOrgService;

    @Autowired
    private BizAffairInsuranceService bizAffairInsuranceService;

    @Autowired
    private BizDgrOpicService bizDgrOpicService;

    @Autowired
    private BizEgCertCheckService bizEgCertCheckService;

    @Autowired
    private DictEgPortService dictEgPortService;

    @Autowired
    private SysDeptEnService sysDeptEnService;

    @Autowired
    private DictEgPublicService dictEgPublicService;

    @Autowired
    private BizEgApproveService bizEgApproveService;

    @Autowired
    private CertificateService certificateService;

    @Autowired
    private CertificateAttributeService certificateAttributeService;

    @Autowired
    private CertificateTypeService certificateTypeService;

    @Autowired
    private OfficerInfoService officeInfoService;

    @Autowired
    private ZwApplyOfficerInfoService zwApplyOfficerInfoService;

    @Autowired
    private DictYthOrgMappingService dictYthOrgMappingService;

    @Autowired
    private BizEgApplyInfoService bizEgApplyInfoService;

    @Autowired
    private ShipInfoAllService shipInfoAllService;

    @Autowired
    private CertShipOwnershipService certShipOwnershipService;

    @Autowired
    private BizEgAcceptService bizEgAcceptService;

    @Autowired
    private BizAffairApplyService bizAffairApplyService;

    @Transactional(rollbackFor = Exception.class)
    public boolean createCertificate(BizAffairApply bizAffairApply) {
        if (bizAffairApply == null) {
            log.error("【电子证照信息】办件信息为空");
            return false;
        }
        // 查询当前办件是否已生成电子证照信息
        Certificate certificate = certificateService.getCertificateByApplyId(bizAffairApply.getApplyId());
        if (certificate != null) {
            log.error("【电子证照信息】当前办件：{}，已生成电子证照信息", bizAffairApply.getApplyId());
            return true;
        }
        // 生成证照信息
        CertificateVo certificateVo = this.createCertificateVo(bizAffairApply);
        if (certificateVo == null) {
            log.error("【电子证照信息】当前办件：{}，生成证照信息失败", bizAffairApply.getApplyId());
            return false;
        }
        // 保存证照基本信息
        certificate = this.createCertificate(certificateVo);
        boolean save = certificateService.save(certificate);
        if (!save) {
            log.error("【电子证照信息】当前办件：{}，证照基本信息保存失败", bizAffairApply.getApplyId());
            return false;
        }
        String id = certificate.getId();
        CertificateInfo certificateInfo = CertificateInfo.getCertificateNameByName(certificateVo.getBizAffairInsurance().getInsurCertName());
        if (certificateInfo == null) {
            log.error("【电子证照信息】当前办件：{}，构建电子证照信息失败", bizAffairApply.getApplyId());
            return false;
        }
        // 获取电子证照信息照面信息构建器
        CertificateInfoBuilder certificateInfoBuilder = certificateInfo.getCertificateInfoBuilder();
        if (certificateInfoBuilder == null) {
            log.error("【电子证照信息】当前办件：{}，获取电子证照信息照面信息构建器失败", bizAffairApply.getApplyId());
            return false;
        }
        // 获取电子证照信息照面信息
        Map<String, String> certInfoMap = certificateInfoBuilder.certInfo();
        if (certInfoMap == null) {
            log.error("【电子证照信息】当前办件：{}，获取电子证照信息照面信息失败", bizAffairApply.getApplyId());
            return false;
        }
        // 构建电子证照信息照面信息值
        Map<String, String> map = certificateInfoBuilder.buildCertificateAttribute(certificateVo);
        if (map == null) {
            log.error("【电子证照信息】当前办件：{}，构建电子证照信息照面信息值失败", bizAffairApply.getApplyId());
            return false;
        }
        // 保存证照照面属性信息表
        List<CertificateAttribute> certificateAttributeList = new ArrayList<>();
        certInfoMap.forEach((key, value) -> {
                    CertificateAttribute certificateAttribute = new CertificateAttribute();
                    // 主表id
                    certificateAttribute.setCertificateId(id);
                    certificateAttribute.setAttributeName(key);
                    certificateAttribute.setAttributeColumnName(value);
                    certificateAttribute.setAttributeValue(map.get(key));
                    certificateAttribute.setCreateDate(new Date());
                    certificateAttribute.setModifyDate(new Date());
                    certificateAttributeList.add(certificateAttribute);
                });
        boolean saveBatch = certificateAttributeService.saveBatch(certificateAttributeList);
        if (!saveBatch) {
            log.error("【电子证照信息】当前办件：{}，证照照面属性信息表保存失败", bizAffairApply.getApplyId());
            return false;
        }
        log.info("【电子证照信息】当前办件：{}，证照照面属性信息表保存成功", bizAffairApply.getApplyId());
        return true;
    }

    /**
     * 根据办件信息表查询生成电子证照信息所需的数据
     * @param bizAffairApply 政务办件表
     * @return 电子证照信息所需信息
     */
    private CertificateVo createCertificateVo(BizAffairApply bizAffairApply) {
        // 创建证书对象
        CertificateVo certificateVo = new CertificateVo();
        // 办件id
        String applyId = bizAffairApply.getApplyId();
        // 处理人信息
        DictEgUser dictEgUser = dictEgUserService.getDictEgUser(bizAffairApply.getUscc());
        if (dictEgUser == null) {
            dictEgUser = dictEgUserService.getDictEgUser(bizAffairApply.getApplicantId());
            if (dictEgUser == null) {
                log.error("【电子证照信息】当前办件：{}，查询处理人信息失败", bizAffairApply.getApplyId());
                bizAffairApply.setCleanStatus("E3");    //缺少用户信息
                this.bizAffairApplyService.saveOrUpdate(bizAffairApply);
                return null;
            }
        }
        // 获取发证机关中文和英文信息
        String srcOrgCode = dictYthOrgMappingService.getSrcOrgCode(bizAffairApply.getAcceptOrgCode());
        SysDeptEn sysDeptEn = sysDeptEnService.getSysDeptEn(srcOrgCode);
        if (sysDeptEn == null) {
            log.error("【电子证照信息】当前办件：{}，查询发证机关中文和英文信息失败", bizAffairApply.getApplyId());
            bizAffairApply.setCleanStatus("E7");    //缺少发证机关中文和英文信息
            this.bizAffairApplyService.saveOrUpdate(bizAffairApply);
            return null;
        }
        // 获取操作人机构信息
        DictEgOrg dictEgOrg = dictEgOrgService.getById(bizAffairApply.getAcceptOrgCode());
        // 如果是政务中心,取上级层级code
        if (dictEgOrg != null && "1".equals(dictEgOrg.getIfCenter())) {
            dictEgOrg = dictEgOrgService.getDictEgOrgByOrgCode(dictEgOrg.getParentCode());
        }
        if (dictEgOrg == null) {
            dictEgOrg = dictEgOrgService.getDictEgOrgByOrgCode(bizAffairApply.getAcceptOrgCode());
        }
        if (dictEgOrg == null) {
            log.error("【电子证照信息】当前办件：{}，查询操作人机构信息失败", bizAffairApply.getApplyId());
            return null;
        }
        // 获取保险信息
        BizAffairInsurance bizAffairInsurance = bizAffairInsuranceService.getByWindowApplyId(applyId);
        if (bizAffairInsurance == null) {
            log.error("【电子证照信息】当前办件：{}，查询保险信息失败", bizAffairApply.getApplyId());
            bizAffairApply.setCleanStatus("E8");    //缺少保险信息
            this.bizAffairApplyService.saveOrUpdate(bizAffairApply);
            return null;
        }
        // 获取证书类型信息
        CertificateType certificateType = certificateTypeService.getCertificateTypeByName(bizAffairInsurance.getInsurCertName());
        if (certificateType == null) {
            log.error("【电子证照信息】当前办件：{}，查询证书类型信息失败", bizAffairApply.getApplyId());
            return null;
        }
        // 证书信息
        BizEgCertCheck bizEgCertCheck = bizEgCertCheckService.getCertCheckByApplyId(applyId);
        if (bizEgCertCheck == null) {
            log.error("【电子证照信息】当前办件：{}，查询证书信息失败", bizAffairApply.getApplyId());
            return null;
        }
        // 签证官员
        ZwApplyOfficerInfo zwApplyOfficerInfo = zwApplyOfficerInfoService.getZwApplyOfficerInfoByWindowApplyId(bizAffairApply.getApplyId());
        if (zwApplyOfficerInfo == null) {
            log.error("【电子证照信息】当前办件：{}，查询签证官员信息失败", bizAffairApply.getApplyId());
            log.error("【电子证照信息】查询签证官员机构编码为：{}", bizAffairApply.getAcceptOrgCode());
            bizAffairApply.setCleanStatus("E2");    //缺少签证官员信息
            this.bizAffairApplyService.saveOrUpdate(bizAffairApply);
            return null;
        }
        // 获取审批信息
        BizEgApprove bizEgApprove = bizEgApproveService.getBizEgApproveByApplyId(applyId);
        if (bizEgApprove == null) {
            log.error("【电子证照信息】当前办件：{}，查询审批信息失败", bizAffairApply.getApplyId());
            bizAffairApply.setCleanStatus("E5");    //缺少审批信息
            this.bizAffairApplyService.saveOrUpdate(bizAffairApply);
            return null;
        }

        // 申请受理审批信息
        BizEgApplyInfo bizEgApplyInfo = bizEgApplyInfoService.getBizEgApplyInfoByApplyId(applyId);
        if (bizEgApplyInfo == null) {
            log.error("【电子证照信息】当前办件：{}，查询bizEgApplyInfo信息失败", bizAffairApply.getApplyId());
            bizAffairApply.setCleanStatus("E6");    //缺少bizEgApplyInfo信息
            this.bizAffairApplyService.saveOrUpdate(bizAffairApply);
            return null;
        }

        // biz_dgr_opic表船舶信息
        BizDgrOpic bizDgrOpic = bizDgrOpicService.getByShipId(bizEgApplyInfo.getShipId());

        // 港口信息
        DictEgPort dictEgPort;
        String regportName = null;
        if (bizDgrOpic == null) {
            log.error("【电子证照信息】当前办件：{}，查询biz_dgr_opic表船舶信息失败", bizAffairApply.getApplyId());

            // 船舶基本信息
            ShipInfoAll shipInfoAll = shipInfoAllService.getShipInfoByShipId(bizEgApplyInfo.getShipId());
            if (shipInfoAll == null) {
                log.error("【电子证照信息】当前办件：{}，查询船舶基本信息失败", bizAffairApply.getApplyId());
                bizAffairApply.setCleanStatus("EA");    //缺少船舶信息
                this.bizAffairApplyService.saveOrUpdate(bizAffairApply);
                return null;
            }
            // 船舶证书-所有权证书
            regportName = shipInfoAll.getShipRegNo();
            CertShipOwnership certShipOwnership = certShipOwnershipService.getById(shipInfoAll.getShipRegNo());
            certificateVo.setBizEgApplyInfo(bizEgApplyInfo);
            certificateVo.setShipInfoAll(shipInfoAll);
            certificateVo.setCertShipOwnership(certShipOwnership);
            dictEgPort = dictEgPortService.getPortByName(shipInfoAll.getRegportName());
        }else {
            regportName = bizDgrOpic.getRegportName();
            certificateVo.setBizDgrOpic(bizDgrOpic);
            dictEgPort = dictEgPortService.getPortByName(bizDgrOpic.getRegportName());
        }
        // 港口信息
        if (dictEgPort == null) {
            log.error("【电子证照信息】当前办件：{}，查询港口信息失败", bizAffairApply.getApplyId());
            log.error("【电子证照信息】港口名称：{}", regportName);
            bizAffairApply.setCleanStatus("E1");    //缺少港口信息
            this.bizAffairApplyService.saveOrUpdate(bizAffairApply);
            return null;
        }

        // 获取受理信息
        BizEgAccept bizEgAccept = bizEgAcceptService.getByApplyId(applyId);
        if (bizEgAccept == null) {
            log.error("【电子证照信息】当前办件：{}，查询受理信息失败", applyId);
            bizAffairApply.setCleanStatus("E9");    //缺少受理信息
            this.bizAffairApplyService.saveOrUpdate(bizAffairApply);
            return null;
        }

        certificateVo.setBizAffairApply(bizAffairApply);
        certificateVo.setDictEgUser(dictEgUser);
        certificateVo.setDictEgOrg(dictEgOrg);
        certificateVo.setBizAffairInsurance(bizAffairInsurance);
        certificateVo.setSysDeptEn(sysDeptEn);
        certificateVo.setBizEgApprove(bizEgApprove);
        certificateVo.setDictEgPort(dictEgPort);
        certificateVo.setBizEgCertCheck(bizEgCertCheck);
        certificateVo.setZwApplyOfficerInfo(zwApplyOfficerInfo);
        certificateVo.setCertificateType(certificateType);
        certificateVo.setBizDgrOpic(bizDgrOpic);
        certificateVo.setBizEgAccept(bizEgAccept);
        return certificateVo;
    }

    private Certificate createCertificate(CertificateVo certificateVo) {
        Certificate certificate = new Certificate();
        certificate.setCertificateName(certificateVo.getBizAffairInsurance().getInsurCertName());
        certificate.setCertificateType(certificateVo.getBizAffairInsurance().getInsurCertName());
        certificate.setCertificateNum(certificateVo.getBizEgCertCheck().getCertNo());
        // 对应证照分类表的证照分类编码
        certificate.setCertificateTypeCode(certificateVo.getCertificateType().getCertificateTypeCode());
        // 3.0编码
        certificate.setIssuOrgCode3(certificateVo.getDictEgOrg().getCode());
        // 2.0编码
        String srcOrgCode = dictYthOrgMappingService.getSrcOrgCode(certificateVo.getDictEgOrg().getCode());
        certificate.setIssuOrgNameCn(sysDeptEnService.getSysDeptEn(srcOrgCode).getOrgName());
        certificate.setIssuOrgCode2(srcOrgCode);
        certificate.setHolderName(certificateVo.getBizAffairApply().getApplicantName());
        certificate.setHolderIdentityNumber(certificateVo.getBizAffairApply().getApplicantId());
        // 发证时间
        BizEgApprove bizEgApprove = certificateVo.getBizEgApprove();
        Date issueDate = bizEgApprove != null ? bizEgApprove.getApprDate() : new Date();
        certificate.setIssueDate(issueDate);
        certificate.setEffectDate(DateUtil.parseDate(certificateVo.getBizAffairInsurance().getEffStartDate()));
        certificate.setExpireDate(DateUtil.parseDate(certificateVo.getBizAffairInsurance().getDeadLine()));
        certificate.setStatusFlag("1");
        certificate.setCreateStatus(UN_CREATE.getCode());
        certificate.setApplyId(certificateVo.getBizAffairApply().getApplyId());

        certificate.setCreateOperId(String.valueOf(certificateVo.getDictEgUser().getAccountId()));
        certificate.setCreateDate(new Date());
        certificate.setModifyOperId(String.valueOf(certificateVo.getDictEgUser().getAccountId()));
        certificate.setModifyDate(new Date());
        // 新增存储业务字段
        certificate.setAffairName(certificateVo.getBizAffairApply().getAffairName());
        certificate.setApplicantName(certificateVo.getBizAffairApply().getApplicantName());
        certificate.setApplyDate(certificateVo.getBizAffairApply().getApplyDate());
        certificate.setApplyType(certificateVo.getBizAffairApply().getApplyType());
        certificate.setAcceptOrgCode3(certificateVo.getBizEgAccept().getMsaOrgCode());
        certificate.setAcceptDate(certificateVo.getBizEgAccept().getCreateDate());
        certificate.setApprOrgCode3(certificateVo.getBizAffairApply().getApprOrgCode());
        certificate.setApprDate(certificateVo.getBizEgApprove().getApprDate());

        // 船舶信息
        if(certificateVo.getShipInfoAll() !=null){
            certificate.setShipId(certificateVo.getShipInfoAll().getShipId());
            certificate.setShipName(certificateVo.getShipInfoAll().getShipName());
            certificate.setShipImo(certificateVo.getShipInfoAll().getShipImo());
            certificate.setShipCallSign(certificateVo.getShipInfoAll().getShipCallsign());
            certificate.setShipNameEn(certificateVo.getShipInfoAll().getShipNameEn());
            certificate.setShipMmsi(certificateVo.getShipInfoAll().getShipMmsi());
        }else{
            certificate.setShipId(certificateVo.getBizDgrOpic().getShipId());
            certificate.setShipName(certificateVo.getBizDgrOpic().getShipName());
            certificate.setShipImo(certificateVo.getBizDgrOpic().getShipImo());
            certificate.setShipCallSign(certificateVo.getBizDgrOpic().getShipCallsign());
            certificate.setShipNameEn(certificateVo.getBizDgrOpic().getShipNameEn());
            certificate.setShipMmsi(certificateVo.getBizDgrOpic().getShipMmsi());
        }

        certificate.setApplyNo(certificateVo.getBizAffairApply().getApplyNo());
        certificate.setProType(certificateVo.getBizAffairApply().getProType());
        // 受理机构2.0
        String acceptOrgCode2 = dictYthOrgMappingService.getSrcOrgCode(certificateVo.getBizEgAccept().getMsaOrgCode());
        certificate.setAcceptOrgCode2(acceptOrgCode2);
        // 审批机构没有值，判断下标准层没值时，取受理机构即可
        if (StringUtils.isBlank(certificateVo.getBizAffairApply().getApprOrgCode())) {
            certificate.setApprOrgCode3(certificateVo.getBizEgAccept().getMsaOrgCode());
            certificate.setApprOrgCode2(acceptOrgCode2);
        }else{
            certificate.setApprOrgCode3(certificateVo.getBizAffairApply().getApprOrgCode());
            String apprOrgCode2 = dictYthOrgMappingService.getSrcOrgCode(certificateVo.getBizAffairApply().getApprOrgCode());
            certificate.setApprOrgCode2(apprOrgCode2);
        }

        return certificate;
    }


}

