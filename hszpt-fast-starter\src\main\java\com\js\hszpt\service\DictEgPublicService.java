package com.js.hszpt.service;

import com.alibaba.druid.util.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.hszpt.constants.Common;
import com.js.hszpt.entity.DictEgPublic;
import com.js.hszpt.mapper.DictEgPublicMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
// 注意：此处需要继承ServiceImpl，并在继承时指定所使用的Mapper类型
public class DictEgPublicService extends ServiceImpl<DictEgPublicMapper, DictEgPublic> {


    public List<DictEgPublic> getValidDictByType(String type, String value) {
        return baseMapper.getValidDictByType(type,value);
    }

    public Map<String, String> getCertNumberDeptShortName() {
        return getCertNumberDeptMap(Common.CERT_NUMBER_DEPT_SHORT_NAME);
    }

    public Map<String, String> getCertNumberDeptMap(String type) {
        Map<String, String> result = new HashMap<>();
        List<DictEgPublic> validDictByType = getValidDictByType(type, null);
        if (CollectionUtils.isNotEmpty(validDictByType)) {
            log.info("证书文书编号机构简称映射：{}，取自数据库字典", type);
            validDictByType.forEach(x->{
                result.put(x.getDictValue(),x.getDictDesc());
            });
        } else {
            log.info("证书文书编号机构简称映射：{}，取自枚举值", type);
            if (StringUtils.equals(Common.CERT_NUMBER_DEPT_CODE, type)) {
                return Common.deptCode;
            } else if (StringUtils.equals(Common.CERT_NUMBER_DEPT_SHORT_NAME, type)) {
                return Common.shortName;
            }
        }
        return result;

    }
}
