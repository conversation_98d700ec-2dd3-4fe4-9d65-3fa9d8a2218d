package com.js.hszpt.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.hszpt.dto.SubOrgUsageStatisticsDTO;
import com.js.hszpt.dto.TimeTypeSubOrgUsageStatisticsDTO;
import com.js.hszpt.entity.DwsCertificateUsage;
import com.js.hszpt.mapper.DwsCertificateUsageMapper;
import com.js.hszpt.service.DwsCertificateUsageService;
import com.js.hszpt.dto.SubOrgUsageRatioStatisticsDTO;
import com.js.hszpt.dto.HolderCategoryUsageStatisticsDTO;
import com.js.hszpt.vo.CertificateUsageStatisticsVO;
import com.js.hszpt.dto.TimeUsageStatisticsDTO;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.ArrayList;

/**
 * 电子证照使用情况统计Service实现类
 */
@Service
public class DwsCertificateUsageServiceImpl extends ServiceImpl<DwsCertificateUsageMapper, DwsCertificateUsage> implements DwsCertificateUsageService {

    /**
     * 按下级机构统计使用情况
     * @param param 查询参数
     * @return 按下级机构统计结果
     */
    @Override
    public List<SubOrgUsageStatisticsDTO> statisticsBySubOrg(CertificateUsageStatisticsVO param) {
        // 调用Mapper进行按下级机构统计
        return baseMapper.statisticsBySubOrg(param);
    }

    /**
     * 按年份和下级机构统计使用情况
     * @param param 查询参数
     * @return 按年份和下级机构统计结果
     */
    @Override
    public List<TimeTypeSubOrgUsageStatisticsDTO> statisticsByYearAndSubOrg(CertificateUsageStatisticsVO param) {
        // 调用Mapper进行按年份和下级机构统计
        return baseMapper.statisticsByYearAndSubOrg(param);
    }

    /**
     * 按下级机构统计使用情况并计算占比
     * @param param 查询参数
     * @return 按下级机构统计结果带占比
     */
    @Override
    public List<SubOrgUsageRatioStatisticsDTO> statisticsBySubOrgWithRatio(CertificateUsageStatisticsVO param) {
        // 获取总使用量
        Long totalUsageCount = baseMapper.getTotalUsageCount(param);
        
        // 调用Mapper进行按下级机构统计
        List<SubOrgUsageRatioStatisticsDTO> detailList = baseMapper.statisticsBySubOrgWithRatio(param);
        
        // 创建结果列表
        List<SubOrgUsageRatioStatisticsDTO> resultList = new ArrayList<>();
        
        // 创建汇总数据
        SubOrgUsageRatioStatisticsDTO summaryData = new SubOrgUsageRatioStatisticsDTO();
        summaryData.setOrgCode(param.getLoginUserOrgCode());
        // 获取登录人所在机构名称
        String loginOrgName = baseMapper.getOrgNameByOrgCode(param.getLoginUserOrgCode());
        summaryData.setOrgName(loginOrgName != null ? loginOrgName : "总计");
        summaryData.setUsageCount(totalUsageCount);
        summaryData.setUsageRatio(100.0); // 汇总数据占比100%
        summaryData.setIsSummary(true);
        
        // 计算各下级机构的占比
        if (detailList != null && !detailList.isEmpty() && totalUsageCount != null && totalUsageCount > 0) {
            for (SubOrgUsageRatioStatisticsDTO detail : detailList) {
                // 计算占比
                detail.setUsageRatio(detail.getUsageCount() * 100.0 / totalUsageCount);
            }
        }
        
        // 将汇总数据添加到结果列表的最前面
        resultList.add(summaryData);
        // 将原始数据添加到结果列表
        resultList.addAll(detailList);
        
        return resultList;
    }

    /**
     * 按持证主体类别统计使用情况并计算占比
     * @param param 查询参数
     * @return 按持证主体类别统计结果带占比
     */
    @Override
    public List<HolderCategoryUsageStatisticsDTO> statisticsByHolderCategoryWithRatio(CertificateUsageStatisticsVO param) {
        // 调用Mapper进行按持证主体类别统计
        List<HolderCategoryUsageStatisticsDTO> detailList = baseMapper.statisticsByHolderCategoryWithRatio(param);
        
        // 创建结果列表
        List<HolderCategoryUsageStatisticsDTO> resultList = new ArrayList<>();
        
        // 计算总使用量
        Long totalUsageCount = 0L;
        for (HolderCategoryUsageStatisticsDTO detail : detailList) {
            totalUsageCount += detail.getUsageCount();
        }
        
        // 创建汇总数据
        HolderCategoryUsageStatisticsDTO summaryData = new HolderCategoryUsageStatisticsDTO();
        summaryData.setHolderCategoryCode("total");
        summaryData.setHolderCategoryName("总计");
        summaryData.setUsageCount(totalUsageCount);
        summaryData.setUsageRatio(100.0); // 汇总数据占比100%
        summaryData.setIsSummary(true);
        
        // 计算各持证主体类别的占比
        if (detailList != null && !detailList.isEmpty() && totalUsageCount > 0) {
            for (HolderCategoryUsageStatisticsDTO detail : detailList) {
                // 计算占比
                detail.setUsageRatio(detail.getUsageCount() * 100.0 / totalUsageCount);
            }
        }
        
        // 将汇总数据添加到结果列表的最前面
        resultList.add(summaryData);
        // 将原始数据添加到结果列表
        resultList.addAll(detailList);
        
        return resultList;
    }

    /**
     * 按时间统计使用情况并计算占比
     * @param param 查询参数
     * @return 按时间统计结果带占比
     */
    @Override
    public List<TimeUsageStatisticsDTO> statisticsByTimeWithRatio(CertificateUsageStatisticsVO param) {
        // 调用Mapper进行按时间统计
        List<TimeUsageStatisticsDTO> detailList = baseMapper.statisticsByTimeWithRatio(param);
        
        // 创建结果列表
        List<TimeUsageStatisticsDTO> resultList = new ArrayList<>();
        
        // 计算总使用量
        Long totalUsageCount = 0L;
        for (TimeUsageStatisticsDTO detail : detailList) {
            totalUsageCount += detail.getUsageCount();
        }
        
        // 创建汇总数据
        TimeUsageStatisticsDTO summaryData = new TimeUsageStatisticsDTO();
        summaryData.setTimePoint("总计");
        summaryData.setUsageCount(totalUsageCount);
        summaryData.setUsageRatio(100.0); // 汇总数据占比100%
        summaryData.setIsSummary(true);
        
        // 计算各时间点的占比
        if (detailList != null && !detailList.isEmpty() && totalUsageCount > 0) {
            for (TimeUsageStatisticsDTO detail : detailList) {
                // 计算占比
                detail.setUsageRatio(detail.getUsageCount() * 100.0 / totalUsageCount);
            }
        }
        
        // 将汇总数据添加到结果列表的最前面
        resultList.add(summaryData);
        // 将原始数据添加到结果列表
        resultList.addAll(detailList);
        
        return resultList;
    }
} 