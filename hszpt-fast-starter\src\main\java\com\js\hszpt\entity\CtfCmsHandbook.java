package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @ClassName: CtfCmsHandbook
 * @Description: 帮助手册-内容管理
 * @author: System Generation
 */
@Data
@TableName("ctf_cms_handbook")
@ApiModel(value = "帮助手册-内容管理")
public class CtfCmsHandbook {

    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    @TableId
    @TableField("id")
    @ApiModelProperty(value = "编号")
    private String id;

    /**
     * 标题
     */
    @TableField("title")
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 内容详情
     */
    @TableField("content")
    @ApiModelProperty(value = "内容详情")
    private String content;

    /**
     * 内容简介
     */
    @TableField("introduce")
    @ApiModelProperty(value = "内容简介")
    private String introduce;

    /**
     * 排序
     */
    @TableField("sort_order")
    @ApiModelProperty(value = "排序")
    private Integer sortOrder;

    /**
     * logo图片路径
     */
    @TableField("logo")
    @ApiModelProperty(value = "logo图片路径")
    private String logo;

    /**
     * 栏目分类 1-船员证照 2-船舶证照
     */
    @TableField("column_type")
    @ApiModelProperty(value = "栏目分类 1-船员证照 2-船舶证照")
    private String columnType;

    /**
     * 是否发布，0-未发布 1-已发布 2-已下线
     */
    @TableField("is_release")
    @ApiModelProperty(value = "是否发布，0-未发布 1-已发布 2-已下线")
    private String isRelease;

    /**
     * 语言类型，1-中文 2-英文
     */
    @TableField("lang_type")
    @ApiModelProperty(value = "语言类型，1-中文 2-英文")
    private String langType;

    /**
     * 商家id
     */
    @TableField("merchant_id")
    @ApiModelProperty(value = "商家id")
    private String merchantId;

    /**
     * 创建人ID
     */
    @TableField("create_by")
    @ApiModelProperty(value = "创建人ID")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("create_time")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 修改人ID
     */
    @TableField("update_by")
    @ApiModelProperty(value = "修改人ID")
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField("update_time")
    @ApiModelProperty(value = "修改时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 发布时间
     */
    @TableField("issue_time")
    @ApiModelProperty(value = "发布时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date issueTime;

    /**
     * 发布人
     */
    @TableField("issue_person")
    @ApiModelProperty(value = "发布人")
    private String issuePerson;

    /**
     * 逻辑删除，0：否；1：是
     */
    @TableField("del_flag")
    @ApiModelProperty(value = "逻辑删除，0：否；1：是")
    private Integer delFlag;
}