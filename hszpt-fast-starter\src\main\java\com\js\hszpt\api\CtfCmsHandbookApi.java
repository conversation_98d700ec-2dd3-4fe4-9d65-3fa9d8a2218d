package com.js.hszpt.api;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.js.core.common.utils.ResultUtil;
import com.js.core.common.vo.Result;
import com.js.hszpt.dto.CtfCmsHandbookQueryDTO;
import com.js.hszpt.entity.CtfCmsHandbook;
import com.js.hszpt.service.CtfCmsHandbookService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @ClassName: CtfCmsHandbookApi
 * @Description: 帮助手册-内容管理API接口
 */
@Slf4j
@RestController
@RequestMapping("/ctfCmsHandbook")
@Api(tags = "帮助手册-内容管理")
public class CtfCmsHandbookApi {

    @Autowired
    private CtfCmsHandbookService ctfCmsHandbookService;

    /**
     * 分页查询帮助手册内容
     */
    @ApiOperation(value = "分页查询帮助手册内容", notes = "分页查询帮助手册内容")
    @GetMapping("/page")
    public Result<IPage<CtfCmsHandbook>> pageList(@ModelAttribute CtfCmsHandbookQueryDTO queryDTO) {
        IPage<CtfCmsHandbook> pageList = ctfCmsHandbookService.pageList(queryDTO);
        return ResultUtil.data(pageList);
    }

    /**
     * 不分页查询帮助手册内容列表
     */
    @ApiOperation(value = "不分页查询帮助手册内容列表", notes = "不分页查询帮助手册内容列表")
    @GetMapping("/list")
    public Result<List<CtfCmsHandbook>> listAll(@ModelAttribute CtfCmsHandbookQueryDTO queryDTO) {
        List<CtfCmsHandbook> list = ctfCmsHandbookService.listAll(queryDTO);
        return ResultUtil.data(list);
    }

    /**
     * 根据ID查询帮助手册内容
     */
    @ApiOperation(value = "根据ID查询帮助手册内容", notes = "根据ID查询帮助手册内容")
    @GetMapping("/getById/{id}")
    public Result<CtfCmsHandbook> getById(@PathVariable String id) {
        CtfCmsHandbook handbook = ctfCmsHandbookService.getHandbookById(id);
        return ResultUtil.data(handbook);
    }

    /**
     * 新增帮助手册内容
     */
    @ApiOperation(value = "新增帮助手册内容", notes = "新增帮助手册内容")
    @PostMapping("/add")
    public Result<Boolean> add(@RequestBody CtfCmsHandbook handbook) {
        boolean success = ctfCmsHandbookService.saveHandbook(handbook);
        return ResultUtil.data(success);
    }

    /**
     * 修改帮助手册内容
     */
    @ApiOperation(value = "修改帮助手册内容", notes = "修改帮助手册内容")
    @PostMapping("/edit")
    public Result<Boolean> edit(@RequestBody CtfCmsHandbook handbook) {
        boolean success = ctfCmsHandbookService.updateHandbook(handbook);
        return ResultUtil.data(success);
    }

    /**
     * 删除帮助手册内容
     */
    @ApiOperation(value = "删除帮助手册内容", notes = "删除帮助手册内容")
    @PostMapping("/delete/{id}")
    public Result<Boolean> delete(@ApiParam(value = "ID", required = true) @PathVariable String id) {
        boolean success = ctfCmsHandbookService.removeHandbookById(id);
        return ResultUtil.data(success);
    }

    /**
     * 发布帮助手册内容
     */
    @ApiOperation(value = "发布帮助手册内容", notes = "发布帮助手册内容")
    @PostMapping("/publish/{id}")
    public Result<Boolean> publish(@PathVariable String id) {
        boolean success = ctfCmsHandbookService.publishHandbook(id);
        return ResultUtil.data(success);
    }

    /**
     * 下线帮助手册内容
     */
    @ApiOperation(value = "下线帮助手册内容", notes = "下线帮助手册内容")
    @PostMapping("/offline/{id}")
    public Result<Boolean> offline(@ApiParam(value = "ID", required = true) @PathVariable String id) {
        boolean success = ctfCmsHandbookService.offlineHandbook(id);
        return ResultUtil.data(success);
    }
}