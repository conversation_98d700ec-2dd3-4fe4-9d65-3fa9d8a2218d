package com.js.hszpt.job;

import cn.hutool.core.collection.CollUtil;
import com.js.hszpt.entity.CertificateDataRegen;
import com.js.hszpt.service.CertificateDataRegenService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 证照重新生成定时任务
 */
@Slf4j
@Component
public class CreateCertificateRegenJob {

    @Autowired
    private CertificateDataRegenService certificateDataRegenService;

    @Value("${job.createCertificateRegen.enabled:false}")
    private boolean enabled;

    @Value("${crew.thread.corePoolSize}")
    private Integer corePoolSize;

    @Value("${crew.thread.maximumPoolSize}")
    private Integer maximumPoolSize;

    private ExecutorService executorService;

    // 添加线程池配置
    @PostConstruct
    public void init() {
        this.executorService = new ThreadPoolExecutor(
            corePoolSize, // 核心线程数
            maximumPoolSize, // 最大线程数
            60L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(100), // 工作队列
            r -> {
                Thread thread = new Thread(r);
                thread.setName("certificate-regen-pool-" + thread.getId());
                return thread;
            },
            new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略
        );
    }

    /**
     * 定时处理未生成证照的数据
     */
    @Scheduled(cron = "${job.createCertificateRegen.cron:0 0/5 * * * ?}")
    public void execute() {
        if (!enabled) {
            return;
        }
        log.info("【证照重新生成定时任务】开始执行");

        try {
            this.regenCertificate(null);
        } catch (Exception e) {
            log.error("【证照重新生成定时任务】执行异常", e);
        }
    }

    public void regenCertificate(List<CertificateDataRegen> certificateDataRegenList) {
        // 获取未处理的证照列表
        List<CertificateDataRegen> certificates = certificateDataRegenList;
        if (CollUtil.isEmpty(certificateDataRegenList)) {
            certificates = certificateDataRegenService.getUnCreatedCertificates();
        }
        log.info("【证照重新生成定时任务】获取到未处理的证照数量：{}", certificates.size());
        // 使用原子计数器记录处理结果
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failCount = new AtomicInteger(0);
        // 使用线程池并行处理证照
        for (CertificateDataRegen certificate : certificates) {
            executorService.submit(() -> {
                try {
                    certificateDataRegenService.createCertificate(certificate);
                    successCount.incrementAndGet();
                    log.info("【证照重新生成定时任务】证照处理成功，certificateId：{}", certificate.getCertificateId());
                } catch (Exception e) {
                    failCount.incrementAndGet();
                    log.error("【证照重新生成定时任务】处理证照异常，certificateId：{}",
                            certificate.getCertificateId(), e);
                }
            });
        }

        // 等待所有任务完成
//            executorService.shutdown();
//            if (!executorService.awaitTermination(30, TimeUnit.MINUTES)) {
//                log.warn("【证照重新生成定时任务】部分任务未在指定时间内完成");
//            }

        log.info("【证照重新生成定时任务】执行完成，成功：{}，失败：{}",
                successCount.get(), failCount.get());
    }
}
