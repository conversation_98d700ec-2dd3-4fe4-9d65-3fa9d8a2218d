package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("CTF_VERIFICATION_DISPLAY_CONFIG")
public class VerificationDisplayConfig {

    /**
     * 主键ID
     */
    @TableId
    private String configId;

    /**
     * 证照类型编码
     */
    private String certificateTypeCode;

    /**
     * 数据来源表名
     */
    private String dataSourceTableName;

    /**
     * 标签名称(中文)
     */
    private String chineseLabelName;

    /**
     * 标签名称(英文)
     */
    private String englishLabelName;

    /**
     * 取数字段名(中文)
     */
    private String chineseDataFieldName;

    /**
     * 取数字段名(英文)
     */
    private String englishDataFieldName;

    /**
     * 字段取值默认值(中文)
     */
    private String chineseDataDefaultValue;

    /**
     * 字段取值默认值(英文)
     */
    private String englishDataDefaultValue;

    /**
     * 高亮显示 0-否 1-是
     */
    private String highlight;

    /**
     * 排序字段，决定展示顺序
     */
    private Integer sortOrder;
}
