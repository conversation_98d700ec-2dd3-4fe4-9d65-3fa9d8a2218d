package com.js.hszpt.service;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.js.api.entity.User;
import com.js.common.entity.CurrentUser;
import com.js.hszpt.entity.SysDept;
import com.js.hszpt.entity.SysDeptInfoResponse;
import com.js.hszpt.entity.SysUser;
import com.js.hszpt.mapper.SysDeptIambDao;
import com.js.hszpt.entity.SysDeptIamb;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.core.common.utils.PageUtil;
import com.js.core.common.vo.PageVo;
import com.js.core.common.vo.SearchVo;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;

import java.util.*;
import java.util.stream.Collectors;

import com.js.hszpt.mapper.SysUserDao;
import com.js.hszpt.utils.ZptUtil;import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * @ClassName:  SysDeptIambService
 * @Description:TODO(组织信息表接口实现)
 * @author:  System Generation
 *
 */
@Slf4j
@Service
public class SysDeptIambService extends ServiceImpl<SysDeptIambDao,SysDeptIamb> {
	@Autowired
	private SysUserService sysUserService;
	@Autowired
	private SysUserDao sysUserDao;
    @Autowired
    private SysDeptIambDao sysDeptIambDao;

	/**
	 * 多条件分页查询
	 * @param param
	 * @param searchVo
	 * @param pageVo
	 * @return
	 */
	public Page<SysDeptIamb> findByCondition(SysDeptIamb param, SearchVo searchVo, PageVo pageVo) {
		Page<SysDeptIamb> page = PageUtil.initMpPage(pageVo);
		QueryWrapper<SysDeptIamb> queryWrapper = this.getCondition(param,searchVo);
		return this.page(page, queryWrapper);
	}

	/**
	 *
	 * @Title: findByCondition
	 * @Description: TODO(多条件列表查询)
	 * @param param
	 * @param searchVo
	 * @return  List<SysDeptIamb>
	 * @throws
	 */
	public List<SysDeptIamb> findByCondition(SysDeptIamb param, SearchVo searchVo){
		QueryWrapper<SysDeptIamb> queryWrapper = this.getCondition(param, searchVo);
		//添加排序字段
		queryWrapper.orderByAsc("create_time");

		return this.list(queryWrapper);
	}


	/**
	 *
	 * @Title: getCondition
	 * @Description: TODO(构建自定义查询条件)
	 * @param param
	 * @param searchVo
	 * @return  QueryWrapper<SysDeptIamb>
	 * @throws
	 */
	private QueryWrapper<SysDeptIamb> getCondition(SysDeptIamb param, SearchVo searchVo) {
		QueryWrapper<SysDeptIamb> queryWrapper = new QueryWrapper<SysDeptIamb>();
		//请根据实际字段自行扩展

		// 创建时间
		if (StrUtil.isNotBlank(searchVo.getStartDate()) && StrUtil.isNotBlank(searchVo.getEndDate())) {
			Date start = DateUtil.parse(searchVo.getStartDate());
			Date end = DateUtil.parse(searchVo.getEndDate());
			queryWrapper.between("create_time", start, DateUtil.endOfDay(end));
		}
		return queryWrapper;
	}

	/**
	 * 根据机构编码获取统一社会信用代码
	 * @param orgCode 机构编码
	 * @return 统一社会信用代码
	 */
	public String getUsccByOrgCode(String orgCode) {
		log.info("开始查询统一社会信用代码，机构编码：{}", orgCode);

		try {
			// 使用baseMapper直接查询
			QueryWrapper<SysDeptIamb> queryWrapper = new QueryWrapper<>();
			queryWrapper.eq("code", orgCode)
					   .eq("del_flag", "0")
					   .last("limit 1");

			// 使用baseMapper.selectList而不是getOne
			List<SysDeptIamb> list = baseMapper.selectList(queryWrapper);

			if (list != null && !list.isEmpty()) {
				String uscc = list.get(0).getUscc();
				log.info("查询到的统一社会信用代码：{}", uscc);
				return uscc;
			}

			log.warn("未查询到机构的统一社会信用代码，机构编码：{}", orgCode);
			return null;
		} catch (Exception e) {
			log.error("查询统一社会信用代码失败，机构编码：{}", orgCode, e);
			return null;
		}
	}



	public SysDeptIamb getDeptLevel() {
		// 获取用户信息
		CurrentUser user = sysUserService.getCurrUser();

		// 检查用户部门ID是否存在
		if (user == null || user.getDataOrgId() == null) {
			throw new IllegalArgumentException("未找到用户所在部门");
		}

		// 使用MyBatis-Plus查询部门信息
		SysDeptIamb dept = sysDeptIambDao.selectOne(
				new LambdaQueryWrapper<SysDeptIamb>()
						.eq(SysDeptIamb::getCode, user.getDataOrgId())
						.select(SysDeptIamb::getDeptId,
								SysDeptIamb::getName,
								SysDeptIamb::getCode,
								SysDeptIamb::getGovLevel)
		);

		return dept;
	}
	public List<SysDeptInfoResponse> getSonOrgTree(String deptId) {
		return sysDeptIambDao.getSonOrgTree(deptId);
	}

	public List<SysDept> list(String code) {
		return sysDeptIambDao.list(code);
	}


	public List getCurrentDeptInfo(String benCode) {
		return sysDeptIambDao.getCurrentDeptInfo(benCode);
	}

	/**
	 * 根据机构编码查询机构信息
	 * @param orgCode 机构编码
	 * @return 机构信息
	 */
	public SysDeptIamb getByOrgCode(String orgCode) {
		log.info("开始查询机构信息，机构编码：{}", orgCode);

		if (StrUtil.isBlank(orgCode)) {
			log.warn("机构编码为空");
			return null;
		}

		try {
			QueryWrapper<SysDeptIamb> queryWrapper = new QueryWrapper<>();
			queryWrapper.eq("code", orgCode)
					   .eq("del_flag", "0")  // 未删除
					   .last("LIMIT 1");     // 只取一条

			SysDeptIamb dept = this.getOne(queryWrapper);

			if (dept == null) {
				log.warn("未找到机构信息，机构编码：{}", orgCode);
			} else {
				log.info("查询到机构信息：{}", dept);
			}

			return dept;
		} catch (Exception e) {
			log.error("查询机构信息失败，机构编码：{}", orgCode, e);
			throw new RuntimeException("查询机构信息失败：" + e.getMessage());
		}
	}

    /**
     * 通过机构编码获取机构信息
     * @param orgCode
     * @return
     */
    public SysDeptIamb getSysDeptIamb(String orgCode){
        SysDeptIamb sysDeptIamb = null;
        QueryWrapper<SysDeptIamb> queryWrapper = Wrappers.<SysDeptIamb>query();
        queryWrapper.lambda()
                .eq(SysDeptIamb::getCode, orgCode);
        queryWrapper.last("LIMIT 1");
        List<SysDeptIamb> list = this.list(queryWrapper);
        if(CollUtil.isNotEmpty(list)){
            sysDeptIamb = list.get(0);
        }
        return sysDeptIamb;
    }

    /**
     * 部门机构编码特殊处理
     * 1. 如果5-6位不等于02，则orgCode等于1-2位
     * 2. 如果9-10位不等于02，则orgCode等于1-8位
     *
     * @param orgCode 原始机构编码
     * @return 处理后的机构编码
     */
    public static String processOrgCode(String orgCode) {
        if (orgCode == null || orgCode.isEmpty()) {
            return orgCode;
        }

        // 检查长度是否足够
        if (orgCode.length() >= 6) {
            // 获取第5-6位
            String pos5to6 = orgCode.substring(4, 6);
            if (!"02".equals(pos5to6)) {
                // 如果5-6位不等于02，则返回1-2位
                return orgCode.substring(0, 2);
            }
        }

        // 检查长度是否足够
        if (orgCode.length() >= 10) {
            // 获取第9-10位
            String pos9to10 = orgCode.substring(8, 10);
            if (!"02".equals(pos9to10)) {
                // 如果9-10位不等于02，则返回1-8位
                return orgCode.substring(0, 8);
            }
        }

        // 如果不满足上述条件，返回原始orgCode
        return orgCode;
    }

    /**
     * 根据机构编码获取机构树形结构（优化版）
     * @param orgCode 机构编码
     * @return 树形结构的机构信息
     */
    public List<SysDeptInfoResponse> deptSon(String orgCode,String searchType) {
        log.info("开始查询机构树形结构，机构编码：{}", orgCode);

        // 1. 根据机构编码查询当前机构
        List<SysDeptIamb> currentDepts = sysDeptIambDao.findDeptByOrgCode(orgCode);

        if (CollUtil.isEmpty(currentDepts)) {
            log.warn("未找到机构信息，机构编码：{}", orgCode);
            return Collections.emptyList();
        }

        SysDeptIamb currentDept = currentDepts.get(0);
        log.info("查询到当前机构：{}", currentDept.getName());

        // 2. 一次性查询所有可能的子机构（最多3级）
        List<SysDeptIamb> allDepts = sysDeptIambDao.findAllDescendants(currentDept.getDeptId());
        log.info("查询到所有相关机构总数：{}", allDepts.size());

        // 3. 在内存中构建树形结构
        return buildDeptTree(currentDept, allDepts,searchType);
    }

    /**
     * 在内存中构建机构树
     * @param rootDept 根机构
     * @param allDepts 所有相关机构
     * @return 树形结构
     */
    private List<SysDeptInfoResponse> buildDeptTree(SysDeptIamb rootDept, List<SysDeptIamb> allDepts,String searchType) {
        // 转换根节点
        SysDeptInfoResponse rootNode = convertToResponse(rootDept);

        // 使用Map按parentId分组所有机构，加速查找
        Map<String, List<SysDeptIamb>> deptMap = allDepts.stream()
                .collect(Collectors.groupingBy(
                    SysDeptIamb::getParentId,
                    Collectors.toList()
                ));

        // 递归构建树，但限制最多3级
        buildTreeRecursively(rootNode, deptMap, 0,searchType);

        return Collections.singletonList(rootNode);
    }

    /**
     * 递归构建树结构
     * @param parentNode 父节点
     * @param deptMap 按parentId分组的机构Map
     * @param depth 当前深度
     */
    private void buildTreeRecursively(SysDeptInfoResponse parentNode, Map<String, List<SysDeptIamb>> deptMap, int depth,String searchType) {
		if (depth == 1 && StrUtil.isNotBlank(searchType)) {
			parentNode.setIsMore(true);
			parentNode.setChildren(Collections.emptyList());
			return;
		}

        // 如果达到最大深度（第3级）或没有子机构，则返回
        if (depth >= 2 || !deptMap.containsKey(parentNode.getDeptId())) {
            parentNode.setIsMore(true);
            parentNode.setChildren(Collections.emptyList());
            return;
        }

        // 获取当前节点的直接子机构
        List<SysDeptIamb> childDepts = deptMap.get(parentNode.getDeptId());

        // 排序子机构（按sort字段）
        childDepts.sort(Comparator.comparing(
            dept -> dept.getSort() != null ? dept.getSort() : Integer.MAX_VALUE
        ));

        // 转换子机构并递归构建下一级
        List<SysDeptInfoResponse> children = childDepts.stream()
                .map(dept -> {
                    SysDeptInfoResponse node = convertToResponse(dept);

                    // 检查是否有子节点
                    boolean hasChildren = deptMap.containsKey(dept.getDeptId());
                    node.setIsMore(hasChildren ? true : false);

                    // 递归构建下一级，深度加1
                    buildTreeRecursively(node, deptMap, depth + 1, searchType);

                    return node;
                })
                .collect(Collectors.toList());

        // 设置子机构列表
        parentNode.setChildren(children);
    }

    /**
     * 将实体对象转换为响应对象
     * @param dept 机构实体
     * @return 响应对象
     */
    private SysDeptInfoResponse convertToResponse(SysDeptIamb dept) {
        SysDeptInfoResponse response = new SysDeptInfoResponse();
        // 设置基本属性
        response.setDeptId(dept.getDeptId());
        response.setTreeId(dept.getDeptId()); // 设置treeId为deptId
        response.setName(dept.getName());
        response.setSort(dept.getSort() != null ? dept.getSort().longValue() : null);
        response.setDelFlag(dept.getDelFlag());
        response.setParentId(dept.getParentId());
        response.setGovLevel(dept.getGovLevel());
        response.setCode(dept.getCode());
        response.setIsShow(dept.getIsShow());
        response.setHallShow(dept.getHallShow());
        response.setCreditCode(dept.getCreditCode());
        response.setHandleDate(dept.getHandleDate());
        response.setApproveAddress(dept.getApproveAddress());
        response.setConsultType(dept.getConsultType());
        response.setSuperviseType(dept.getSuperviseType());
        response.setIsCenter(dept.getIsCenter());
        response.setDeptPhone(dept.getDeptPhone());
        response.setTaskCode(dept.getTaskCode());
        response.setParentCode(dept.getParentCode());
        response.setDeptType(dept.getDeptType());
        response.setNameAbbr(dept.getNameAbbr());
        response.setCodesetId(dept.getCodesetId());
        response.setOrgTag(dept.getOrgTag());
        response.setOrgCcname(dept.getOrgCcname());
        response.setOrgDesc(dept.getOrgDesc());
        response.setOrgCertifit(dept.getOrgCertifit());
        response.setZsOrgType(dept.getZsOrgType());
        response.setOldDeptId(dept.getOldDeptId());
        response.setAreaId(dept.getAreaId());
        response.setGrade(dept.getGrade());

        return response;
    }

	public List<SysDeptInfoResponse> deptNow() {
		CurrentUser currUser = sysUserService.getCurrUser();

//		SysUser sysUser = sysUserService.get(currUser);
		String deptId = currUser.getDataOrgId();
		List<SysDeptInfoResponse> sysDeptInfoResponseList = sysDeptIambDao.deptNow(deptId);
		if (null == sysDeptInfoResponseList || sysDeptInfoResponseList.size() < 1){
			log.info("{}当前机构不存在下级机构!",deptId);
			return null;
		}
		return sysDeptInfoResponseList;
	}

    public List<com.js.hszpt.vo.SysDeptInfoResponse> deptSonApp(String orgCode ,String flag) {
            String processedOrgCode = ZptUtil.processOrgCode(orgCode);
            log.info("原始orgCode: {}, 处理后orgCode: {}", orgCode, processedOrgCode);
            SysDeptIamb sysDeptIamb = this.getSysDeptIamb(processedOrgCode);
            if(null == sysDeptIamb){
                return null;
            }
            return sysDeptIambDao.deptSonApp(sysDeptIamb.getDeptId(),flag);
        }
}
