package com.js.hszpt.service;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.js.hszpt.dto.LawWorkflowNodeUserListDto;
import com.js.hszpt.mapper.CertTypeApprovalDao;
import com.js.hszpt.entity.CertTypeApproval;
import com.js.hszpt.entity.CertTypeDirectory;
import com.js.hszpt.mapper.LawWorkflowBpmDao;
import com.js.hszpt.mapper.LawWorkflowNodeUserDao;
import com.js.hszpt.service.CertTypeDirectoryService;
import com.js.hszpt.service.CertTypeApprovalApplyService;
import com.js.hszpt.vo.CertTypeApprovalSubmitVO;
import com.js.hszpt.entity.CertTypeApprovalApply;
import com.js.hszpt.service.WorkflowService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.core.common.utils.PageUtil;
import com.js.core.common.vo.PageVo;
import com.js.core.common.vo.SearchVo;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

import com.js.hszpt.vo.LawWorkflowNodeUserListVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 
 * @ClassName:  CertTypeApprovalService    
 * @Description:TODO(证照类型目录审批表接口实现)   
 * @author:  System Generation
 *
 */
@Slf4j
@Service
public class CertTypeApprovalService extends ServiceImpl<CertTypeApprovalDao,CertTypeApproval> {

	@Autowired
	private CertTypeDirectoryService certTypeDirectoryService;
	
	@Autowired
	private CertTypeApprovalApplyService certTypeApprovalApplyService;
	
	@Autowired
	private WorkflowService workflowService;

	@Autowired
	private LawWorkflowBpmDao lawWorkflowBpmDao;

	@Autowired
	private LawWorkflowNodeUserDao lawWorkflowNodeUserDao;
	/**
	 * 多条件分页查询
	 * @param param
	 * @param searchVo
	 * @param pageVo
	 * @return
	 */
	public Page<CertTypeApproval> findByCondition(CertTypeApproval param, SearchVo searchVo, PageVo pageVo) {
		Page<CertTypeApproval> page = PageUtil.initMpPage(pageVo);
		QueryWrapper<CertTypeApproval> queryWrapper = this.getCondition(param,searchVo);
		return this.page(page, queryWrapper);
	}
	
	/**
	 * 
	 * @Title: findByCondition   
	 * @Description: TODO(多条件列表查询)   
	 * @param param
	 * @param searchVo
	 * @return  List<CertTypeApproval>      
	 * @throws
	 */
	public List<CertTypeApproval> findByCondition(CertTypeApproval param, SearchVo searchVo){
		QueryWrapper<CertTypeApproval> queryWrapper = this.getCondition(param, searchVo);
		//添加排序字段
		queryWrapper.orderByAsc("create_time");
		
		return this.list(queryWrapper);
	}
	
	
	/**
	 * 
	 * @Title: getCondition   
	 * @Description: TODO(构建自定义查询条件)   
	 * @param param
	 * @param searchVo
	 * @return  QueryWrapper<CertTypeApproval>      
	 * @throws
	 */
	private QueryWrapper<CertTypeApproval> getCondition(CertTypeApproval param, SearchVo searchVo){
		QueryWrapper<CertTypeApproval> queryWrapper = new QueryWrapper<CertTypeApproval>();
		//请根据实际字段自行扩展
		 
		// 创建时间
		if (StrUtil.isNotBlank(searchVo.getStartDate()) && StrUtil.isNotBlank(searchVo.getEndDate())) {
			Date start = DateUtil.parse(searchVo.getStartDate());
			Date end = DateUtil.parse(searchVo.getEndDate());
			queryWrapper.between("create_time",start, DateUtil.endOfDay(end));
		}
		return queryWrapper;
		
	}

	/**
	 * 保存审批草稿
	 * @param submitVO 提交VO
	 */
	public void saveApprovalDraft(CertTypeApprovalSubmitVO submitVO) {
		log.info("保存审批草稿，参数：{}", submitVO);
		CertTypeApprovalApply certTypeApprovalApply = certTypeApprovalApplyService.getById(submitVO.getCertTypeApprApplyId());
		if (certTypeApprovalApply == null) {
			throw new RuntimeException("审批申请办件不存在");
		}
		// 1. 查询是否已存在审批记录
		CertTypeApproval approval = this.getOne(Wrappers.<CertTypeApproval>lambdaQuery()
				.eq(CertTypeApproval::getCertTypeApprApplyId, submitVO.getCertTypeApprApplyId()));
		
		// 2. 如果不存在则创建新记录
		if (approval == null) {
			approval = new CertTypeApproval();
			approval.setCertTypeDirId(certTypeApprovalApply.getCertTypeDirId());
		}
		
		// 3. 设置草稿状态和审批意见
		approval.setApprovalResult(submitVO.getApprovalResult());
		approval.setApprovalOpinion(submitVO.getOpinion());
		approval.setCertTypeApprApplyId(certTypeApprovalApply.getId());
		approval.setApplicantId(submitVO.getOperatorId());
		approval.setApplicantName(certTypeApprovalApply.getApplicantName());
		approval.setApplyTime(new Date());
		approval.setCurrentNode(certTypeApprovalApply.getCurrentNode());
		approval.setHandleStatus("2");
		approval.setCreateBy(submitVO.getOperatorId());
		approval.setCreateTime(new Date());
		// 4. 保存或更新记录
		this.saveOrUpdate(approval);
		
		log.info("保存审批草稿成功，审批ID：{}", approval.getId());
	}

	/**
	 * 提交审批
	 * @param submitVO 提交VO
	 */
	@Transactional(rollbackFor = Exception.class)
	public void submitApproval(CertTypeApprovalSubmitVO submitVO) {
		log.info("提交审批，参数：{}", submitVO);
		CertTypeApprovalApply certTypeApprovalApply = certTypeApprovalApplyService.getById(submitVO.getCertTypeApprApplyId());
		if (certTypeApprovalApply == null) {
			throw new RuntimeException("审批申请办件不存在");
		}
		// 判断机构级别
		boolean isBureauUser = "1".equals(submitVO.getOrgLevel()); // 1表示部海事局
		boolean isDirectBureauUser = "2".equals(submitVO.getOrgLevel()); // 2表示直属局
		
		// 1. 保存审批表数据
		CertTypeApproval approval = this.getOne(Wrappers.<CertTypeApproval>lambdaQuery()
				.eq(CertTypeApproval::getCertTypeApprApplyId, submitVO.getCertTypeApprApplyId())
				.eq(CertTypeApproval::getHandleStatus,"2"),false);

		if (approval == null) {
			approval = new CertTypeApproval();
			approval.setId(UUID.randomUUID().toString());
			approval.setCertTypeDirId(certTypeApprovalApply.getCertTypeDirId());
			approval.setCreateBy(submitVO.getOperatorId());
			approval.setCreateTime(new Date());
		}

		CertTypeDirectory certTypeDirectory = certTypeDirectoryService.getById(certTypeApprovalApply.getCertTypeDirId());
		if (certTypeDirectory == null) {
			throw new RuntimeException("证照类型目录不存在");
		}

		CertTypeApprovalApply apply = this.certTypeApprovalApplyService.getByCertTypeDirId(certTypeDirectory.getId());

		// 设置审批结果
		approval.setApprovalResult(submitVO.getApprovalResult());
		approval.setApprovalOpinion(submitVO.getOpinion());
		approval.setCertTypeApprApplyId(apply.getId());
		approval.setApplicantId(apply.getApplicantId());
		approval.setApplicantName(apply.getApplicantName());
		approval.setApplyTime(apply.getApplyTime());
		approval.setCurrentNode(isBureauUser?"2":"1");
		approval.setHandleStatus("1");
		approval.setUpdateBy(submitVO.getOperatorId());
		approval.setUpdateTime(new Date());

		this.saveOrUpdate(approval);
		
		// 2. 修改证照类型目录表
		if (certTypeDirectory != null) {
			if (isBureauUser) {
				// 部局审批
				if ("1".equals(submitVO.getApprovalResult())) {
					// 通过
					certTypeDirectory.setApprovalStatus("3"); // 3-已审批
					if("1".equals(apply.getApplyType()) || "3".equals(apply.getApplyType())){
						//新增申请 或 启用申请
						certTypeDirectory.setIssueStatus("2"); // 2-已下发
						apply.setIssueStatus("2");
					}else if("2".equals(apply.getApplyType())){
						//废止申请
						certTypeDirectory.setIssueStatus("3"); // 2-已废止
						apply.setIssueStatus("3");
					}
				} else if ("2".equals(submitVO.getApprovalResult())) {
					// 不通过
					certTypeDirectory.setApprovalStatus("0"); // 0-草稿（待提交）
					certTypeDirectory.setIssueStatus("1"); // 1-未下发
					apply.setIssueStatus("1");
				} else if ("3".equals(submitVO.getApprovalResult())) {
					// 退回，部局退回分两种场景：1.直属局提交、并审核后，部局退回 2.部局提交后，部局退回
					if("2".equals(certTypeDirectory.getApprovalStatus())){
						//直属局已审核
						certTypeDirectory.setApprovalStatus("1"); // 1-已提交
						certTypeDirectory.setIssueStatus("1"); // 1-未下发
						apply.setIssueStatus("1");
					}else {
						certTypeDirectory.setApprovalStatus("0"); // 0-草稿（待提交）
						certTypeDirectory.setIssueStatus("1"); // 1-未下发
						apply.setIssueStatus("1");
					}
				}
			} else if (isDirectBureauUser) {
				// 直属局审核
				if ("1".equals(submitVO.getApprovalResult())) {
					// 通过
					certTypeDirectory.setApprovalStatus("2"); // 2-已审核
				} else if ("2".equals(submitVO.getApprovalResult())) {
					// 不通过
					certTypeDirectory.setApprovalStatus("0"); // 0-草稿（待提交）
				} else if ("3".equals(submitVO.getApprovalResult())) {
					// 退回
					certTypeDirectory.setApprovalStatus("0"); // 0-草稿（待提交）
				}
			}
			certTypeDirectory.setUpdateBy(submitVO.getOperatorId());
			certTypeDirectory.setUpdateTime(new Date());
			
			certTypeDirectoryService.updateById(certTypeDirectory);
		}
		
		// 3. 修改审批申请表
		if (apply != null) {
			if (isBureauUser) {
				// 部海事局用户处理
				apply.setCurrentNode("3"); // 3-审批
				
				// 设置状态
				if ("1".equals(submitVO.getApprovalResult())) {
					apply.setStatus("7"); // 7-审批通过
				} else if ("2".equals(submitVO.getApprovalResult())) {
					apply.setStatus("8"); // 8-审批不通过
				} else if ("3".equals(submitVO.getApprovalResult())) {
					apply.setStatus("9"); // 9-审批退回
				}
				
				// 设置审批信息
				apply.setApprovalOrgCode(submitVO.getOrgCode());
				apply.setApprovalOrgName(submitVO.getOrgName());
				apply.setApprovalTime(new Date());
				
				// 设置发布信息
				apply.setPublishOrgCode(submitVO.getOrgCode());
				apply.setPublishOrgName(submitVO.getOrgName());
				apply.setPublishTime(new Date());
				
			} else if (isDirectBureauUser) {
				// 直属局用户处理
				apply.setCurrentNode("2"); // 2-审核
				
				// 设置状态
				if ("1".equals(submitVO.getApprovalResult())) {
					apply.setStatus("2"); // 2-审核通过
				} else if ("2".equals(submitVO.getApprovalResult())) {
					apply.setStatus("3"); // 3-审核不通过
				} else if ("3".equals(submitVO.getApprovalResult())) {
					apply.setStatus("4"); // 4-审核退回
				}
				
				// 设置受理信息
				apply.setAcceptOrgCode(submitVO.getOrgCode());
				apply.setAcceptOrgName(submitVO.getOrgName());
				apply.setAcceptTime(new Date());
			}
			
			// 设置当前处理人
			apply.setCurrentHandlerId(submitVO.getOperatorId());
			apply.setCurrentHandlerName(submitVO.getOperatorName());
			apply.setUpdateBy(submitVO.getOperatorId());
			apply.setUpdateTime(new Date());
			
			certTypeApprovalApplyService.updateById(apply);
		}
		
		// 4. 调用工作流服务接口
		String executionId = apply.getExecutionId();
		if ("1".equals(submitVO.getApprovalResult())) {
			if (isBureauUser) {
				// 审批通过 - 处理工作流，工作流结束，不需要生成待办了
				log.info("审批通过，调用工作流处理接口，工作流ID：{}", executionId);
				lawWorkflowBpmDao.deleteByExecutionId(executionId);
				//workflowService.handleWorkflow(executionId, submitVO.getOperatorId(), certTypeDirectory.getId());
			}else if(isDirectBureauUser){
				// 审批通过 - 处理工作流
				log.info("审批通过，调用工作流处理接口，工作流ID：{}", executionId);
				LawWorkflowNodeUserListDto dto = new LawWorkflowNodeUserListDto();
				List<String> orgCodes = new ArrayList<>();
				orgCodes.add(submitVO.getOrgCode());
				dto.setOrgCode(orgCodes);
				if (StrUtil.equals(apply.getApplyType(), "1")) {
					dto.setLawWorkflowNodeSetId("T001");
					dto.setLawWorkflowNodeInfoId("N002");
				} else if (StrUtil.equals(apply.getApplyType(), "2")) {
					dto.setLawWorkflowNodeSetId("T002");
					dto.setLawWorkflowNodeInfoId("N004");
				} else if (StrUtil.equals(apply.getApplyType(), "3")) {
					dto.setLawWorkflowNodeSetId("T002");
					dto.setLawWorkflowNodeInfoId("N006");
				}
				workflowService.handleWorkflow(executionId, submitVO.getOperatorId(), certTypeDirectory.getId(),  dto);
			}
		} else if ("2".equals(submitVO.getApprovalResult())) {
			// 审批不通过 - 停止工作流
			log.info("审批不通过，调用工作流停止接口，工作流ID：{}", executionId);
			workflowService.stopWorkflow(executionId);
		} else if ("3".equals(submitVO.getApprovalResult())) {
			// 部局退回直属局 给直属局审核人生成待办
			if (!StrUtil.equals("01", apply.getApplyOrgCode())) {
				LawWorkflowNodeUserListDto dto = new LawWorkflowNodeUserListDto();
				List<String> orgCodes = new ArrayList<>();
				orgCodes.add("01");
				dto.setOrgCode(orgCodes);
				if (StrUtil.equals(apply.getApplyType(), "1")) {
					dto.setLawWorkflowNodeSetId("T001");
					dto.setLawWorkflowNodeInfoId("N001");
				} else if (StrUtil.equals(apply.getApplyType(), "2")) {
					dto.setLawWorkflowNodeSetId("T002");
					dto.setLawWorkflowNodeInfoId("N003");
				} else if (StrUtil.equals(apply.getApplyType(), "3")) {
					dto.setLawWorkflowNodeSetId("T002");
					dto.setLawWorkflowNodeInfoId("N005");
				}
				List<LawWorkflowNodeUserListVo> lawWorkflowNodeUserListVoList = lawWorkflowNodeUserDao.selectWorkflowNodeUserList(dto);
				workflowService.insertWorkflowData(executionId, certTypeDirectory.getId(), lawWorkflowNodeUserListVoList);
			}else {
				// 审批退回 - 工作流退回到上一节点
				log.info("审批退回，调用工作流退回接口，工作流ID：{}", executionId);
				workflowService.returnWorkflow(executionId, submitVO.getOperatorId(), submitVO.getOpinion(), certTypeDirectory.getId());
			}
		}
		
		log.info("提交审批成功，审批ID：{}", approval.getId());
	}
}