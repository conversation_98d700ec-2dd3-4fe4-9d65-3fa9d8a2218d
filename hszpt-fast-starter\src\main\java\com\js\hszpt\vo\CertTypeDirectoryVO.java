package com.js.hszpt.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

@Data
@ApiModel(value = "证照类型目录VO")
public class CertTypeDirectoryVO {
    @ApiModelProperty("证照类型目录ID")
    private String id;

    @ApiModelProperty("证照类型代码")
    private String certificateTypeCode;

    @ApiModelProperty("证照类型名称")
    private String certificateTypeName;

    @ApiModelProperty("创建机构名称")
    private String createOrgName;

    @ApiModelProperty("创建机构代码")
    private String createOrgCode;

    @ApiModelProperty("关联事项名称")
    private String relatedItemName;

    @ApiModelProperty("关联事项代码")
    private String relatedItemCode;

    @ApiModelProperty("持证主体类别")
    private String certificateHolderCategory;

    @ApiModelProperty("持证主体类别名称")
    private String certificateHolderCategoryName;

    @ApiModelProperty("有效期范围")
    private String validityRange;

    @ApiModelProperty("审批状态")
    private String approvalStatus;

    @ApiModelProperty("审批状态名称")
    private String approvalStatusName;

    @ApiModelProperty("下发状态")
    private String issueStatus;

    @ApiModelProperty("下发状态名称")
    private String issueStatusName;

    @ApiModelProperty("下发日期")
    private Date issueDate;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty("更新人")
    private String updateBy;

    @ApiModelProperty("更新时间")
    private Date updateTime;

    @ApiModelProperty("删除标志")
    private String delFlag;

    @ApiModelProperty("父级ID")
    private String parentId;

    @ApiModelProperty("定义机构统一社会信用代码，18位，符合GB32100")
    private String certificateDefineAuthorityCode;
}