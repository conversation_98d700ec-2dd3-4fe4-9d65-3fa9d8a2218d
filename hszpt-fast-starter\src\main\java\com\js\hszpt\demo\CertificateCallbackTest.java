package com.js.hszpt.demo;

import com.js.hszpt.vo.CertificateCallbackRequest;
import com.js.hszpt.vo.CrewNotificationRequest;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

/**
 * 证照回调接口测试类
 */
public class CertificateCallbackTest {

    public static void main(String[] args) {
        // 测试证照回调请求参数
        testCertificateCallbackRequest();
        
        // 测试船员系统通知请求参数
        testCrewNotificationRequest();
        
        // 测试HTTP请求（需要实际的服务环境）
        // testHttpRequest();
    }

    /**
     * 测试证照回调请求参数
     */
    private static void testCertificateCallbackRequest() {
        System.out.println("=== 测试证照回调请求参数 ===");
        
        CertificateCallbackRequest request = new CertificateCallbackRequest();
        request.setCertificateId("CERT123456789");
        request.setCode("0");
        request.setMsg("证照生成成功");
        
        System.out.println("证照ID: " + request.getCertificateId());
        System.out.println("状态码: " + request.getCode());
        System.out.println("消息: " + request.getMsg());
        System.out.println();
    }

    /**
     * 测试船员系统通知请求参数
     */
    private static void testCrewNotificationRequest() {
        System.out.println("=== 测试船员系统通知请求参数 ===");
        
        CrewNotificationRequest request = new CrewNotificationRequest(
            "CERT123456789", 
            "0", 
            "证照生成成功"
        );
        
        System.out.println("证照ID: " + request.getCertificateId());
        System.out.println("回调状态码: " + request.getCallbackCode());
        System.out.println("回调消息: " + request.getCallbackMsg());
        System.out.println();
    }

    /**
     * 测试HTTP请求（需要实际的服务环境）
     */
    private static void testHttpRequest() {
        try {
            System.out.println("=== 测试HTTP请求 ===");
            
            RestTemplate restTemplate = new RestTemplate();
            String url = "http://198.32.233.173/crewServer/services/CertElecProResService";
            
            CrewNotificationRequest request = new CrewNotificationRequest(
                "CERT123456789", 
                "0", 
                "证照生成成功"
            );
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<CrewNotificationRequest> requestEntity = new HttpEntity<>(request, headers);
            
            System.out.println("发送请求到: " + url);
            System.out.println("请求参数: " + request);
            
            ResponseEntity<String> response = restTemplate.postForEntity(url, requestEntity, String.class);
            
            System.out.println("响应状态码: " + response.getStatusCode());
            System.out.println("响应内容: " + response.getBody());
            
        } catch (Exception e) {
            System.out.println("HTTP请求测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
} 