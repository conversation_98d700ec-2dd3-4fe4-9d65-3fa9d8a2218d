package com.js.hszpt.vo;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 工作流节点配置(LawWorkflowNodeSet)实体类
 *
 * <AUTHOR>
 * @since 2020-09-28 19:05:59
 */
public class LawWorkflowNodeSetVo {
    /**
     * ID
     */
    private String lawWorkflowNodeSetId;

    /**
     * 事项编码
     */
    private String itemsCode;
    /**
     * 事项名称
     */
    private String itemsName;
    /**
     * 创建人
     */
    private String createOperId;
    /**
     * 创建部门
     */
    private String createOperDept;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 修改人
     */
    private String modifyOperId;
    /**
     * 修改部门
     */
    private String modifyOperDept;
    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifyTime;
    /**
     * 版本号
     */
    private String recordVersion;

    private List<LawWorkflowNodeInfoTreeVo> nodeInfoTreeVos;

    public String getLawWorkflowNodeSetId() {
        return lawWorkflowNodeSetId;
    }

    public void setLawWorkflowNodeSetId(String lawWorkflowNodeSetId) {
        this.lawWorkflowNodeSetId = lawWorkflowNodeSetId;
    }

    public String getItemsCode() {
        return itemsCode;
    }

    public void setItemsCode(String itemsCode) {
        this.itemsCode = itemsCode;
    }

    public String getItemsName() {
        return itemsName;
    }

    public void setItemsName(String itemsName) {
        this.itemsName = itemsName;
    }

    public String getCreateOperId() {
        return createOperId;
    }

    public void setCreateOperId(String createOperId) {
        this.createOperId = createOperId;
    }

    public String getCreateOperDept() {
        return createOperDept;
    }

    public void setCreateOperDept(String createOperDept) {
        this.createOperDept = createOperDept;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getModifyOperId() {
        return modifyOperId;
    }

    public void setModifyOperId(String modifyOperId) {
        this.modifyOperId = modifyOperId;
    }

    public String getModifyOperDept() {
        return modifyOperDept;
    }

    public void setModifyOperDept(String modifyOperDept) {
        this.modifyOperDept = modifyOperDept;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getRecordVersion() {
        return recordVersion;
    }

    public void setRecordVersion(String recordVersion) {
        this.recordVersion = recordVersion;
    }

    public List<LawWorkflowNodeInfoTreeVo> getNodeInfoTreeVos() {
        return nodeInfoTreeVos;
    }

    public void setNodeInfoTreeVos(List<LawWorkflowNodeInfoTreeVo> nodeInfoTreeVos) {
        this.nodeInfoTreeVos = nodeInfoTreeVos;
    }
}