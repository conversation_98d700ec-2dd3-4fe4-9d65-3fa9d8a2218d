package com.js.hszpt.api;

import com.js.core.api.BaseApiPlus;
import com.js.core.common.utils.ResultUtil;
import com.js.core.common.vo.Result;
import com.js.hszpt.entity.DataReceptionTask;
import com.js.hszpt.service.DataReceptionTaskService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@Api("数据接收")
@RequestMapping("/dataReceptionTask")
public class DataReceptionTaskApi extends BaseApiPlus<DataReceptionTaskService, DataReceptionTask,String> {

    @GetMapping("/receptionTask/{taskName}")
    public Result<String> receptionTask(@PathVariable String taskName) {
        boolean receptionTask = baseService.receptionTask(taskName);
        return receptionTask ?  ResultUtil.success("数据接收完成:"+ taskName)
                : ResultUtil.error("数据接收失败:"+ taskName) ;
    }
}
