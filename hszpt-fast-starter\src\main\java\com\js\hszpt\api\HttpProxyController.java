package com.js.hszpt.api;

import com.js.core.common.utils.ResultUtil;
import com.js.core.common.vo.Result;
import com.js.hszpt.config.ProxyConfig;
import com.js.hszpt.service.HttpProxyService;
import com.js.hszpt.utils.HttpProxyUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;

/**
 * HTTP透传控制器
 * 提供HTTP请求转发功能
 */
@RestController
@RequestMapping("/proxy")
public class HttpProxyController {

    private static final Logger logger = LoggerFactory.getLogger(HttpProxyController.class);

    @Autowired
    private HttpProxyUtil httpProxyUtil;

    @Autowired
    private ProxyConfig proxyConfig;

    /**
     * 处理所有HTTP请求并转发
     * 将接收到的请求转发到配置的目标服务器
     */
    @RequestMapping("/**")
    public void proxyRequest(HttpServletRequest request, HttpServletResponse response) {
        try {
            // 使用带参数对象的HttpProxyUtil转发请求到第三方服务
            httpProxyUtil.forwardRequest(request, response, proxyConfig.getTargetUrl(), true);

            // 由于forwardRequest方法已经处理了响应，这里不需要返回值

        } catch (Exception e) {
            logger.error("处理代理请求时发生错误", e);
            try {
                // 返回错误信息
                Result<?> result = ResultUtil.error("处理代理请求时发生错误: " + e.getMessage());
                response.setStatus(500);
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write(result.toString());
            } catch (IOException ex) {
                logger.error("写入错误响应时发生异常", ex);
            }
        }
    }

    /**
     * 读取请求体
     */
    private byte[] readRequestBody(HttpServletRequest request) throws IOException {
        try (InputStream inputStream = request.getInputStream()) {
            return StreamUtils.copyToByteArray(inputStream);
        }
    }
}