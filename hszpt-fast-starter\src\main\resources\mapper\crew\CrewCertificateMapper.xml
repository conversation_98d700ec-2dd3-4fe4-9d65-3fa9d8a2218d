<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.js.hszpt.mapper.crew.CrewCertificateMapper">
    <select id="selectCrewCertificates" resultType="com.js.hszpt.vo.CrewCertificateQueryVo">
        SELECT
            ROW_NUMBER() OVER(ORDER BY rec_create_date DESC) as serialNo,
--             ship_name as shipName,
            certificate_type_name as certificateType,
            certificate_name as certificateName,
            certificate_number as certificateNumber,
            cert_print_no as permitNumber,
            certificate_holder_name as holderName,
            certificate_holder_code as holderCode,
            certificate_holder_category_name as nationality,
            certificate_issued_date as birthDate,
            certificate_issuing_authority_name as issueType,
            issue_dept_code2 as trainingOrg,
            issue_dept_code3 as foreignOrg,
            certificate_define_authority_name as mainSignOrg,
            certificate_issued_date as issueDate,
            certificate_effective_date as effectiveDate,
            certificate_expiring_date as expiryDate,
            certificate_status as certificateStatus
        FROM dwdz_certificate_data
        WHERE 1=1
        <if test="query.certificateTypeCode != null and query.certificateTypeCode != ''">
            AND certificate_type_code = #{query.certificateTypeCode}
        </if>
        <if test="query.certificateNumber != null and query.certificateNumber != ''">
            AND certificate_number = #{query.certificateNumber}
        </if>
        <if test="query.certificateHolderName != null and query.certificateHolderName != ''">
            AND certificate_holder_name = #{query.certificateHolderName}
        </if>
        <if test="query.certificateHolderCode != null and query.certificateHolderCode != ''">
            AND certificate_holder_code = #{query.certificateHolderCode}
        </if>
        <if test="query.certPrintNo != null and query.certPrintNo != ''">
            AND cert_print_no = #{query.certPrintNo}
        </if>
        <if test="query.permitNumber != null and query.permitNumber != ''">
            AND cert_print_no = #{query.permitNumber}
        </if>
<!--        <if test="query.shipName != null and query.shipName != ''">-->
<!--            AND ship_name = #{query.shipName}-->
<!--        </if>-->
        <if test="query.issueOrgCode != null and query.issueOrgCode != ''">
            AND certificate_issuing_authority_code = #{query.issueOrgCode}
        </if>
        <if test="query.trainingOrgCode != null and query.trainingOrgCode != ''">
            AND issue_dept_code2 = #{query.trainingOrgCode}
        </if>
        <if test="query.foreignOrgCode != null and query.foreignOrgCode != ''">
            AND issue_dept_code3 = #{query.foreignOrgCode}
        </if>
        <if test="query.mainSignOrgCode != null and query.mainSignOrgCode != ''">
            AND certificate_define_authority_code = #{query.mainSignOrgCode}
        </if>
        <if test="query.certStartDate != null and query.certEndDate != null">
            AND certificate_expiring_date
            BETWEEN #{query.certStartDate} AND #{query.certEndDate}
        </if>
        ORDER BY rec_create_date DESC
    </select>

    <!-- 内网船员证书查询 -->
    <select id="certificateIntranetCrewQueryAuth" resultType="com.js.hszpt.vo.CertificateIntranetCrewQueryVo">
        SELECT
            ROW_NUMBER() OVER(ORDER BY certificate_issued_date DESC) as serialNo,
            certificate_number as certificateNumber,
            certificate_status as certificateStatus,
            certificate_holder_name as holderName,
            certificate_holder_code as holderIdentityNumber,
            certificate_issued_date as birthDate,
            cert_print_no as certPrintNo,
            cert_print_no as permitNumber,
            certificate_issuing_authority_name as issuingAuthority,
            issue_dept_code2 as trainingOrg,
            issue_dept_code3 as foreignOrg,
            certificate_define_authority_name as mainSignOrg,
            certificate_issued_date as issuingDate,
            certificate_effective_date as validFromDate,
            certificate_expiring_date as validToDate,
--             ship_name as shipName
        FROM dwdz_certificate_data
        <where>
            <if test="dto.certificateTypeCode != null and dto.certificateTypeCode != ''">
                AND certificate_type_code = #{dto.certificateTypeCode}
            </if>
            <if test="dto.certificateNumber != null and dto.certificateNumber != ''">
                AND certificate_number = #{dto.certificateNumber}
            </if>
            <if test="dto.holderName != null and dto.holderName != ''">
                AND certificate_holder_name = #{dto.holderName}
            </if>
            <if test="dto.holderIdentityNumber != null and dto.holderIdentityNumber != ''">
                AND certificate_holder_code = #{dto.holderIdentityNumber}
            </if>
            <if test="dto.certPrintNo != null and dto.certPrintNo != ''">
                AND cert_print_no = #{dto.certPrintNo}
            </if>
            <if test="dto.permitNumber != null and dto.permitNumber != ''">
                AND cert_print_no = #{dto.permitNumber}
            </if>
            <if test="dto.issuingAuthority != null and dto.issuingAuthority != ''">
                AND certificate_issuing_authority_name = #{dto.issuingAuthority}
            </if>
            <if test="dto.trainingOrg != null and dto.trainingOrg != ''">
                AND issue_dept_code2 = #{dto.trainingOrg}
            </if>
            <if test="dto.foreignOrg != null and dto.foreignOrg != ''">
                AND issue_dept_code3 = #{dto.foreignOrg}
            </if>
            <if test="dto.mainSignOrg != null and dto.mainSignOrg != ''">
                AND certificate_define_authority_name = #{dto.mainSignOrg}
            </if>
            <if test="dto.issuingDateStart != null and dto.issuingDateStart != ''">
                AND certificate_issued_date &gt;= #{dto.issuingDateStart}
            </if>
            <if test="dto.issuingDateEnd != null and dto.issuingDateEnd != ''">
                AND certificate_issued_date &lt;= #{dto.issuingDateEnd}
            </if>
            <if test="dto.validFromDate != null">
                AND certificate_effective_date &gt;= #{dto.validFromDate}
            </if>
            <if test="dto.validToDate != null">
                AND certificate_expiring_date &lt;= #{dto.validToDate}
            </if>
            <if test="dto.certificateStatus != null and dto.certificateStatus != ''">
                <choose>
                    <when test="dto.certificateStatus == '1'">
                        AND certificate_effective_date &lt;= CURRENT_TIMESTAMP
                        AND certificate_expiring_date &gt;= CURRENT_TIMESTAMP
                    </when>
                    <when test="dto.certificateStatus == '2'">
                        AND (certificate_effective_date &gt; CURRENT_TIMESTAMP
                        OR certificate_expiring_date &lt; CURRENT_TIMESTAMP)
                    </when>
                </choose>
            </if>
        </where>
        ORDER BY certificate_issued_date DESC
    </select>

    <!-- 查询内河船舶船员培训合格证的培训项目 -->
        <select id="queryTrainingProjects" resultType="com.js.hszpt.vo.TrainingProjectVo">
            SELECT
                STRING_AGG(i.project_name, '#') as projectNames,
                STRING_AGG(i.sign_date, '#') as certificateIssuedDate,
                STRING_AGG(i.end_date, '#') as certificateExpiringDate
            FROM dwdz_ctf_cert_dtl_nhpxhg n
            JOIN dwdz_ctf_cert_dtl_nhpxhg_item i ON n.nhpxhg_id = i.nhpxhg_id
            WHERE n.data_id = #{dataId}
        </select>

        <!-- 查询证书展示配置 -->
            <select id="queryVerificationDisplayConfigs" resultType="com.js.hszpt.entity.VerificationDisplayConfig">
                SELECT
                    CONFIG_ID as configId,
                    CERTIFICATE_TYPE_CODE as certificateTypeCode,
                    DATA_SOURCE_TABLE_NAME as dataSourceTableName,
                    CHINESE_LABEL_NAME as chineseLabelName,
                    ENGLISH_LABEL_NAME as englishLabelName,
                    CHINESE_DATA_FIELD_NAME as chineseDataFieldName,
                    ENGLISH_DATA_FIELD_NAME as englishDataFieldName,
                    CHINESE_DATA_DEFAULT_VALUE as chineseDataDefaultValue,
                    ENGLISH_DATA_DEFAULT_VALUE as englishDataDefaultValue,
                    HIGHLIGHT as highlight,
                    SORT_ORDER as sortOrder,
                    DISPLAY_SOURCE as displaySource
                FROM dwdz_ctf_verification_display_config
                WHERE CERTIFICATE_TYPE_CODE = (select CERTIFICATE_TYPE_CODE from dwdz_ctf_certificate_config where certificate_name = #{certificateTypeName})
                AND DISPLAY_SOURCE = #{displaySource}
                ORDER BY SORT_ORDER ASC
            </select>
</mapper>
