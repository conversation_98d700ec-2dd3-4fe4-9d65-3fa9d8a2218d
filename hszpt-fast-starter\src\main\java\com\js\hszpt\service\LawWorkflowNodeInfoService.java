package com.js.hszpt.service;

import com.js.hszpt.entity.LawWorkflowNodeSet;
import com.js.hszpt.mapper.LawWorkflowNodeInfoDao;
import com.js.hszpt.entity.LawWorkflowNodeInfo;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.core.common.utils.PageUtil;
import com.js.core.common.vo.PageVo;
import com.js.core.common.vo.SearchVo;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import java.util.Date;
import java.util.List;

import com.js.hszpt.vo.LawWorkflowNodeInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
 
/**
 * 
 * @ClassName:  LawWorkflowNodeInfoService    
 * @Description:TODO(工作流模板节点表接口实现)   
 * @author:  System Generation
 *
 */
@Slf4j
@Service
public class LawWorkflowNodeInfoService extends ServiceImpl<LawWorkflowNodeInfoDao,LawWorkflowNodeInfo> {
	@Autowired
	private LawWorkflowNodeInfoDao lawWorkflowNodeInfoDao;
	/**
	 * 多条件分页查询
	 * @param param
	 * @param searchVo
	 * @param pageVo
	 * @return
	 */
	public Page<LawWorkflowNodeInfo> findByCondition(LawWorkflowNodeInfo param, SearchVo searchVo, PageVo pageVo) {
		Page<LawWorkflowNodeInfo> page = PageUtil.initMpPage(pageVo);
		QueryWrapper<LawWorkflowNodeInfo> queryWrapper = this.getCondition(param,searchVo);
		return this.page(page, queryWrapper);
	}
	
	/**
	 * 
	 * @Title: findByCondition   
	 * @Description: TODO(多条件列表查询)   
	 * @param param
	 * @param searchVo
	 * @return  List<LawWorkflowNodeInfo>      
	 * @throws
	 */
	public List<LawWorkflowNodeInfo> findByCondition(LawWorkflowNodeInfo param, SearchVo searchVo){
		QueryWrapper<LawWorkflowNodeInfo> queryWrapper = this.getCondition(param, searchVo);
		//添加排序字段
		queryWrapper.orderByAsc("create_time");
		
		return this.list(queryWrapper);
	}
	
	
	/**
	 * 
	 * @Title: getCondition   
	 * @Description: TODO(构建自定义查询条件)   
	 * @param param
	 * @param searchVo
	 * @return  QueryWrapper<LawWorkflowNodeInfo>      
	 * @throws
	 */
	private QueryWrapper<LawWorkflowNodeInfo> getCondition(LawWorkflowNodeInfo param, SearchVo searchVo){
		QueryWrapper<LawWorkflowNodeInfo> queryWrapper = new QueryWrapper<LawWorkflowNodeInfo>();
		//请根据实际字段自行扩展
		 
		// 创建时间
		if (StrUtil.isNotBlank(searchVo.getStartDate()) && StrUtil.isNotBlank(searchVo.getEndDate())) {
			Date start = DateUtil.parse(searchVo.getStartDate());
			Date end = DateUtil.parse(searchVo.getEndDate());
			queryWrapper.between("create_time",start, DateUtil.endOfDay(end));
		}
		return queryWrapper;
		
	}

	/**
	 * 查询流程模板
	 * @param nodeInfoId
	 * @return
	 */
    public List<LawWorkflowNodeInfoVo> selectNodeInfoDetails(String nodeInfoId) {
		List<LawWorkflowNodeInfoVo> lawWorkflowNodeInfoVos = lawWorkflowNodeInfoDao.getDaoServiceClient(nodeInfoId);
		return lawWorkflowNodeInfoVos;
    }
}