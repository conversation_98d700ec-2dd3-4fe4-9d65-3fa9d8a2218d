package com.js.hszpt.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.common.entity.CurrentUser;
import com.js.core.common.vo.Result;
import com.js.hszpt.dto.LawWorkflowNodeUserListDto;
import com.js.hszpt.entity.*;
import com.js.hszpt.mapper.CertTypeDirectoryDao;
import com.js.hszpt.util.ApplyNoUtil;
import com.js.hszpt.vo.*;
import com.js.hszpt.enums.CertApprovalApplyStatusEnum;
import com.js.hszpt.enums.CertTypeDirectoryIssueStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 *
 * @ClassName: CertTypeDirectoryService
 * @Description:TODO(证照类型目录表接口实现)
 * @author: System Generation
 *
 */
@Slf4j
@Service
public class CertTypeDirectoryService extends ServiceImpl<CertTypeDirectoryDao, CertTypeDirectory> {

	@Autowired
	private WorkflowService workflowService;

	@Autowired
	private CertTypeApprovalApplyService certApprovalApplyService;

	@Autowired
	private CertTypeApprovalService certTypeApprovalService;

	@Autowired
	private CertTypeDirectoryDao certTypeDirectoryDao;

	@Autowired
	private CertTypeDirSeqService certTypeDirSeqService;

	@Autowired
	private SysUserService sysUserService;

	@Autowired
	private SysDeptIambService sysDeptIambService;

    @Autowired
    private CtfCertificateTypeService certificateTypeService;

	public Page<CertTypeDirectory> findByCondition(CertTypeDirectoryQueryVO queryVO) {
		log.info("开始查询证照类型目录列表，查询条件：{}", queryVO);

		// 构建查询条件
		LambdaQueryWrapper<CertTypeDirectory> wrapper = new LambdaQueryWrapper<>();
		wrapper.eq(CertTypeDirectory::getDelFlag, "0"); // 只查询未删除的记录

		// 证照类型代码
		if (StringUtils.isNotBlank(queryVO.getCertTypeCode())) {
			wrapper.like(CertTypeDirectory::getCertificateTypeCode, queryVO.getCertTypeCode());
		}

		// 证照名称列表
		if (queryVO.getCertName() != null && !queryVO.getCertName().isEmpty()) {
			wrapper.in(CertTypeDirectory::getCertificateTypeName, queryVO.getCertName());
		}

		// 证照定义机构列表
		if (queryVO.getOrgName() != null && !queryVO.getOrgName().isEmpty()) {
			wrapper.in(CertTypeDirectory::getCertificateDefineAuthorityName, queryVO.getOrgName());
		}

		// 关联事项名称
		if (StringUtils.isNotBlank(queryVO.getAffairName())) {
			wrapper.like(CertTypeDirectory::getRelatedItemName, queryVO.getAffairName());
		}

		// 关联事项代码
		if (StringUtils.isNotBlank(queryVO.getAffairNo())) {
			wrapper.like(CertTypeDirectory::getRelatedItemCode, queryVO.getAffairNo());
		}

		// 持证主体类别
		if (StringUtils.isNotBlank(queryVO.getCertificateHolderCategory())) {
			wrapper.eq(CertTypeDirectory::getCertificateHolderCategory, queryVO.getCertificateHolderCategory());
		}

		// 有效期限范围
		if (StringUtils.isNotBlank(queryVO.getValidPeriod())) {
			wrapper.like(CertTypeDirectory::getValidityRange, queryVO.getValidPeriod());
		}

		// 审批状态
		if (StringUtils.isNotBlank(queryVO.getApprovalStatus())) {
			wrapper.eq(CertTypeDirectory::getApprovalStatus, queryVO.getApprovalStatus());
		}

		// 下发状态
		if (StringUtils.isNotBlank(queryVO.getIssueStatus())) {
			wrapper.eq(CertTypeDirectory::getIssueStatus, queryVO.getIssueStatus());
		}

		// 下发日期范围
		if (queryVO.getIssueDateStart() != null && queryVO.getIssueDateEnd() != null) {
			wrapper.between(CertTypeDirectory::getIssueDate,
					queryVO.getIssueDateStart(), queryVO.getIssueDateEnd());
		}

		// 创建日期范围
		if (queryVO.getCreateTimeStart() != null && queryVO.getCreateTimeEnd() != null) {
			wrapper.between(CertTypeDirectory::getCreateTime,
					queryVO.getCreateTimeStart(), queryVO.getCreateTimeEnd());
		}

		// 排序
		wrapper.orderByDesc(CertTypeDirectory::getCreateTime);

		// 执行分页查询
		Page<CertTypeDirectory> page = new Page<>(
				Objects.requireNonNull(queryVO.getPageNumber(), "页码不能为空"),
				Objects.requireNonNull(queryVO.getPageSize(), "每页条数不能为空"));

		return this.page(page, wrapper);
	}

	public Page<CertTypeDirectoryListVO> queryCertTypeDirectoryList(CertTypeDirectoryQueryVO queryVO) {
		// 1. 设置分页参数
		Page<CertTypeDirectoryListVO> page = new Page<>(queryVO.getPageNumber(), queryVO.getPageSize());
		// 2. 拼接机构条件 执行查询
		String orgCode = this.sysUserService.getCurrUserOrgCode();
		queryVO.setApplyOrgCode(orgCode);
		queryVO.setCreateBy(sysUserService.getCurrUsername());
		List<CertTypeDirectoryListVO> records = baseMapper.queryCertTypeDirectoryList(page, queryVO);

		// 3. 处理按钮权限和证照类型名称拼接
		records.forEach(record -> {
			// 处理按钮权限
			this.processButtonPermissions(record);

			// 处理证照类型名称拼接
			List<CertTypeDirectory> treeList = certTypeDirectoryDao.queryCertTypeDirTree(record.getCertTypeDirId());
			if (CollUtil.isNotEmpty(treeList)) {
				String certTypeNamePath = treeList.stream()
						.map(CertTypeDirectory::getCertificateTypeName)
						.collect(Collectors.joining("-"));
				log.info("证照类型：{}", certTypeNamePath);
				record.setCertificateTypeName(certTypeNamePath);
			}
		});

		// 4. 设置返回结果
		page.setRecords(records);
		return page;
	}

	private void processButtonPermissions(CertTypeDirectoryListVO vo) {
		ButtonPermissionVO permission = new ButtonPermissionVO();

		/**
		 * 各表流转相关枚举：
		 * 1、CERT_TYPE_DIRECTORY(证照类型目录表)
		 * APPROVAL_STATUS：审批状态，0-草稿（待提交）1-已提交 2-已审核 3-已审批
		 * ISSUE_STATUS：下发状态，1-未下发 2-已下发 3-已废止
		 *
		 * 2、cert_type_approval_apply(证照类型目录审批申请表)
		 * APPLY_TYPE：申请类型：1-新增 2-废止 3-启用
		 * CURRENT_NODE：当前节点：1-申请 2-审核 3-审批
		 * STATUS：状态：1-待审核 2-审核通过 3-审核不通过 4-审核退回 5-申请撤回 6-待审批 7-审批通过 8-审批不通过 9-审批退回
		 * 10-审核撤回
		 *
		 * 3、CERT_TYPE_APPROVAL(证照类型目录审核审批表)
		 * CURRENT_NODE：当前审批环节：1-审核 2-审批
		 * APPROVAL_TYPE：审批类型：1-提交 2-废止 3-启用
		 * APPROVAL_RESULT：审批结果：1-通过 2-不通过 3-退回
		 */

		/**
		 * 按钮显示逻辑说明：
		 1、状态：已通过、已下发       操作：查看、废止
		 2、状态：已通过、已废止       操作：查看、启用
		 3、状态：待审批、未下发       操作：查看、撤回
		 4、状态：不通过、未下发       操作：查看、编辑、重新提交
		 5、状态：草稿、未下发         操作：查看、编辑、删除、提交审批
		 */
		permission.setCanView(true);
		// 已通过、已下发
		if (StrUtil.equals(vo.getIssueStatus(), "2") && StrUtil.equals("3", vo.getApprovalStatus())) {
			permission.setCanAbolish(true);
		}
		// 已通过、已废止
		if (StrUtil.equals(vo.getIssueStatus(), "3") && StrUtil.equals("3", vo.getApprovalStatus())) {
			permission.setCanEnable(true);
		}
		// 待审批、待审核、未下发
		if (StrUtil.equals(vo.getIssueStatus(), "1") && StrUtil.equalsAny(vo.getStatus(), "1","2","6") && StrUtil.equals(vo.getOperatorName(), vo.getCurrentHandlerName())) {
			permission.setCanRevoke(true);
		}
		// 不通过、未下发
		if (StrUtil.equals(vo.getIssueStatus(), "1") && StrUtil.equalsAny(vo.getStatus(), "3","8")) {
			permission.setCanEdit(true);
			permission.setCanDelete(true);
			permission.setCanResubmit(true);
		}
		// 草稿、未下发
		if (StrUtil.equals(vo.getIssueStatus(), "1") && StrUtil.equals(vo.getApprovalStatus(), "0") && StrUtil.equalsAny(vo.getApplyType(), "1")) {
			permission.setCanEdit(true);
			permission.setCanDelete(true);
			permission.setCanSubmitApproval(true);
		}

        // 草稿、未下发 废止/启用
        if (StrUtil.equals(vo.getIssueStatus(), "1") && StrUtil.equals(vo.getApprovalStatus(), "0") && StrUtil.equalsAny(vo.getApplyType(), "2","3")) {
            permission.setCanEdit(true);
            permission.setCanDelete(true);
            permission.setCanSubmitApproval(true);
        }
		// 新增的废止和启用办件不通过、退回、保存、撤回，新增的废止和启用办件按状态显示按钮可以继续操作，
		// 原办件也可以继续提交废止、启用申请；

		vo.setButtonPermission(permission);
	}

	/**
	 * 生成申请编号
	 */
	private String generateApplyNo() {
		// TODO: 调用公共的获取申请编号方法
		return "APPLY_" + System.currentTimeMillis();
	}

	/**
	 * 保存或提交证照类型目录
	 */
	@Transactional(rollbackFor = Exception.class)
	public Result<String> saveOrSubmit(CertTypeDirectorySaveVO saveVO) {
		log.info("开始保存证照类型目录，参数：{}", saveVO);

		try {
			// 1. 保存证照类型目录
			CertTypeDirectory certTypeDirectory = new CertTypeDirectory();
			BeanUtils.copyProperties(saveVO, certTypeDirectory);
			String username = sysUserService.getCurrUsername();

			// 设置审批状态和下发状态
			certTypeDirectory.setApprovalStatus(saveVO.getSubmit() ? "1" : "0");
			certTypeDirectory.setIssueStatus("1"); // 未下发
			certTypeDirectory.setCertificateDefineAuthorityCode(saveVO.getCertificateDefineAuthorityCode());
			certTypeDirectory.setCertificateDefineAuthorityName(saveVO.getCreateOrgName());

			// 保存或更新证照类型目录
			boolean isUpdate = StringUtils.isNotBlank(saveVO.getCertTypeDirId());
			if (isUpdate) {
				certTypeDirectory.setId(saveVO.getCertTypeDirId());
				certTypeDirectory.setUpdateBy(username);
				certTypeDirectory.setUpdateTime(new Date());
				this.updateById(certTypeDirectory);
			} else {
				certTypeDirectory.setCreateBy(username);
				certTypeDirectory.setCreateTime(new Date());
				this.save(certTypeDirectory);
				// 新增时，需要重新获取ID
				saveVO.setCertTypeDirId(certTypeDirectory.getId());
			}
            // 维护证照配置表
            LambdaQueryWrapper<CtfCertificateType> typeWrapper = new LambdaQueryWrapper<>();
            typeWrapper.eq(CtfCertificateType::getCertTypeDirName, saveVO.getCertificateTypeName());
            List<CtfCertificateType> certificateTypeList = certificateTypeService.list(typeWrapper);
//            if (CollectionUtils.isNotEmpty(certificateTypeList) &&
//                StrUtil.isEmpty(certificateTypeList.get(0).getCertTypeDirCode()) ||
//                CollectionUtils.isNotEmpty(certificateTypeList) &&
//                certificateTypeList.get(0).getCertTypeDirCode().equals(saveVO.getCertificateTypeCode())
//                ) {
//                certificateTypeList.forEach(certificateType ->
//                    certificateType.setCertTypeDirCode(saveVO.getCertificateTypeCode())
//                );
//                certificateTypeService.updateBatchById(certificateTypeList);
//            }else{
//                this.removeById(certTypeDirectory);
//                return Result.failed("该证照类型名称已经绑定");
//            }
			if(CollectionUtils.isNotEmpty(certificateTypeList)){
				certificateTypeList.forEach(certificateType ->
                    certificateType.setCertTypeDirCode(saveVO.getCertificateTypeCode())
                );
				certificateTypeService.updateBatchById(certificateTypeList);
			}

			// 查询是否存在审批申请记录
			LambdaQueryWrapper<CertTypeApprovalApply> queryWrapper = new LambdaQueryWrapper<>();
			queryWrapper.eq(CertTypeApprovalApply::getCertTypeDirId, certTypeDirectory.getId())
					.eq(CertTypeApprovalApply::getDelFlag, "0")
					.orderByDesc(CertTypeApprovalApply::getCreateTime)
					.last("LIMIT 1");

			CertTypeApprovalApply apply = certApprovalApplyService.getOne(queryWrapper);
			boolean isNewApply = (apply == null);

			if (isNewApply) {
				apply = new CertTypeApprovalApply();
				apply.setApplyNo(ApplyNoUtil.getToDayMaxApplyId());
				apply.setCertTypeDirId(certTypeDirectory.getId());
				apply.setApplyType("1"); // 新增
				apply.setCurrentNode("1"); // 申请
				apply.setApplyTime(new Date());
				apply.setIssueStatus("1");
				apply.setCreateBy(username);
				apply.setCreateTime(new Date());
			}
			String orgCode = this.sysUserService.getCurrUserOrgCode();
			boolean isBureauUser = setUserInfo(apply, orgCode);
			// 更新序列值
			log.info("更新证照类型代码序号，机构编码为：{}", orgCode);
			certTypeDirSeqService.updateSeqValueByOrgCode(orgCode);

			// 2. 如果是提交操作，需要保存审批申请记录
			if (saveVO.getSubmit()) {
				LawWorkflowNodeUserListDto dto = new LawWorkflowNodeUserListDto();
				List<String> orgCodes = new ArrayList<>();
				orgCodes.add(orgCode);
				dto.setOrgCode(orgCodes);
				dto.setLawWorkflowNodeSetId("T001");
				dto.setLawWorkflowNodeInfoId(isBureauUser ? "N002" : "N001");
				CurrentUser currUser = sysUserService.getCurrUser();
				// 开启工作流
				String executionId = workflowService.startWorkflow(currUser.getUsername(), dto);
				// 设置/更新审批申请记录
				apply.setExecutionId(executionId);
				log.info("提交审批申请记录成功，申请编号：{}，工作流ID：{}", apply.getApplyNo(), executionId);
			}

			// 保存或更新审批申请记录
			boolean success = isNewApply ? certApprovalApplyService.save(apply)
					: certApprovalApplyService.updateById(apply);

			if (!success) {
				throw new RuntimeException("保存审批申请记录失败");
			}

			log.info("保存证照类型目录成功");
            return Result.success(saveVO.getSubmit() ? "提交成功" : "保存成功");
		} catch (Exception e) {
			log.error("保存证照类型目录失败", e);
			throw new RuntimeException("保存失败：" + e.getMessage());
		}
	}

	private boolean setUserInfo(CertTypeApprovalApply apply, String orgCode) {
		SysDeptIamb dept = this.sysDeptIambService.getByOrgCode(orgCode);
		// 判断机构级别
		boolean isBureauUser = "1".equals(dept.getGovLevel()); // 1表示部海事局
		apply.setStatus(isBureauUser ? CertApprovalApplyStatusEnum.PENDING_APPROVAL.getCode()
				: CertApprovalApplyStatusEnum.PENDING_AUDIT.getCode()); // 部局待审批 直属局待审核
		CurrentUser user = this.sysUserService.getCurrUser();
		apply.setApplyOrgCode(dept.getCode());
		apply.setApplyOrgName(dept.getName());
		apply.setApplicantId(user.getId());
		apply.setApplicantName(user.getNickName());
		return isBureauUser;
	}

	/**
	 * 撤回证照类型目录
	 *
	 * @param certTypeDirId 证照类型目录ID
	 */
	@Transactional(rollbackFor = Exception.class)
	public void revoke(String certTypeDirId) {
		log.info("开始撤回证照类型目录，ID：{}", certTypeDirId);

		// 1. 参数校验
		if (StringUtils.isBlank(certTypeDirId)) {
			throw new IllegalArgumentException("证照类型目录ID不能为空");
		}

		// 2. 获取证照类型目录信息
		CertTypeDirectory certTypeDirectory = this.getById(certTypeDirId);
		if (certTypeDirectory == null) {
			throw new IllegalArgumentException("证照类型目录不存在");
		}

		// 3. 获取审批申请信息
		LambdaQueryWrapper<CertTypeApprovalApply> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(CertTypeApprovalApply::getCertTypeDirId, certTypeDirId)
				.eq(CertTypeApprovalApply::getDelFlag, "0")
				.orderByDesc(CertTypeApprovalApply::getCreateTime)
				.last("LIMIT 1");
		CertTypeApprovalApply apply = certApprovalApplyService.getOne(queryWrapper);
		if (apply == null) {
			throw new IllegalArgumentException("审批申请记录不存在");
		}

		// 4. 根据审批状态处理撤回
		String approvalStatus = certTypeDirectory.getApprovalStatus();
		if ("1".equals(approvalStatus)) {
			// 已提交状态 -> 撤回到草稿状态
			apply.setStatus(CertApprovalApplyStatusEnum.APPLY_REVOKE.getCode()); // 申请撤回
			// 新增将状态改回草稿
			if (StrUtil.equals("1",apply.getApplyType()) || StrUtil.equals("3",apply.getApplyType()) || StrUtil.equals("2",apply.getApplyType())) {
				certTypeDirectory.setApprovalStatus("0"); // 草稿（待提交）
			}

			// 停止工作流
			workflowService.stopWorkflow(apply.getExecutionId());
		} else if ("2".equals(approvalStatus)) {
			// 已审核状态 -> 撤回到已提交状态
			apply.setStatus(CertApprovalApplyStatusEnum.AUDIT_WITHDRAW.getCode()); // 审核撤回
			if (StrUtil.equals("1",apply.getApplyType())) {
				certTypeDirectory.setApprovalStatus("1"); // 已提交
			}
			// 撤回工作流
			workflowService.revokeWorkflow(apply.getExecutionId(), certTypeDirId);
		} else {
			throw new IllegalArgumentException("当前状态不允许撤回");
		}

		// 5. 更新数据库
		try {
			this.updateById(certTypeDirectory);
			certApprovalApplyService.updateById(apply);
			log.info("撤回证照类型目录成功");
		} catch (Exception e) {
			log.error("撤回证照类型目录失败", e);
			throw new RuntimeException("撤回失败：" + e.getMessage());
		}
	}

	/**
	 * 废止证照类型目录
	 *
	 * @param certTypeApprApplyId 证照类型目录ID
	 */
	@Transactional(rollbackFor = Exception.class)
	public void abolish(String certTypeApprApplyId) {
		log.info("开始废止证照类型目录，ID：{}", certTypeApprApplyId);

		// 1. 参数校验
		if (StringUtils.isBlank(certTypeApprApplyId)) {
			throw new IllegalArgumentException("申请表ID不能为空");
		}

		CertTypeApprovalApply certTypeApprovalApply = certApprovalApplyService.getById(certTypeApprApplyId);
		if (certTypeApprovalApply == null) {
			throw new IllegalArgumentException("申请表不存在");
		}
		// 2. 获取证照类型目录信息
		CertTypeDirectory certTypeDirectory = this.getById(certTypeApprovalApply.getCertTypeDirId());
		if (certTypeDirectory == null) {
			throw new IllegalArgumentException("证照类型目录不存在");
		}

		// 3. 校验状态
		String issueStatus = certTypeDirectory.getIssueStatus();
		if (!CertTypeDirectoryIssueStatusEnum.ISSUED.getCode().equals(issueStatus)) {
			throw new IllegalArgumentException("当前证照类型目录状态不能废止，只有已下发状态才能废止");
		}

		try {
			String username = this.sysUserService.getCurrUsername();

			// 将证照类型目录审批状态改为 1-已提交
			LambdaUpdateWrapper<CertTypeDirectory> updateWrapperDir = new LambdaUpdateWrapper<>();
			updateWrapperDir.eq(CertTypeDirectory::getId, certTypeDirectory.getId())
					.set(CertTypeDirectory::getApprovalStatus, "1");
			this.update(updateWrapperDir);

			// 4. 生成申请编号
			String applyNo = ApplyNoUtil.getToDayMaxApplyId();

			// 原机构信息
			String orgCode = certTypeApprovalApply.getApplyOrgCode();

			// 保存审批申请记录
			CertTypeApprovalApply apply = new CertTypeApprovalApply();
			apply.setApplyNo(applyNo);
			apply.setCertTypeDirId(certTypeDirectory.getId());
			apply.setApplyType("2"); // 废止
			apply.setApplyTime(new Date());
			boolean isBureauUser = setUserInfo(apply, orgCode);
			apply.setCurrentNode("1"); // 申请
			apply.setIssueStatus("1");
			apply.setCreateBy(username);
			apply.setCreateTime(new Date());
			// 开启工作流
			LawWorkflowNodeUserListDto dto = new LawWorkflowNodeUserListDto();
			List<String> orgCodes = new ArrayList<>();
			orgCodes.add(orgCode);
			dto.setOrgCode(orgCodes);
			dto.setLawWorkflowNodeSetId("T002");
			dto.setLawWorkflowNodeInfoId(isBureauUser ? "N004" : "N003");
			CurrentUser currUser = sysUserService.getCurrUser();
			String executionId = workflowService.startWorkflow(currUser.getUsername(),dto);
			apply.setExecutionId(executionId);
			// 7. 保存申请记录
			boolean success = certApprovalApplyService.save(apply);
			if (!success) {
				throw new RuntimeException("保存审批申请记录失败");
			}

			log.info("废止证照类型目录申请提交成功，申请编号：{}", applyNo);
		} catch (Exception e) {
			log.error("废止证照类型目录失败", e);
			throw new RuntimeException("废止申请失败：" + e.getMessage());
		}
	}

	/**
	 * 启用证照类型目录
	 *
	 * @param certTypeApprApplyId 证照类型目录ID
	 */
	@Transactional(rollbackFor = Exception.class)
	public void enable(String certTypeApprApplyId) {
		log.info("开始启用证照类型目录，ID：{}", certTypeApprApplyId);

		// 1. 参数校验
		if (StringUtils.isBlank(certTypeApprApplyId)) {
			throw new IllegalArgumentException("证照类型目录ID不能为空");
		}

		CertTypeApprovalApply certTypeApprovalApply = certApprovalApplyService.getById(certTypeApprApplyId);
		if (certTypeApprovalApply == null) {
			throw new IllegalArgumentException("申请表不存在");
		}

		// 2. 获取证照类型目录信息
		CertTypeDirectory certTypeDirectory = this.getById(certTypeApprovalApply.getCertTypeDirId());
		if (certTypeDirectory == null) {
			throw new IllegalArgumentException("证照类型目录不存在");
		}

		// 3. 校验状态
		String issueStatus = certTypeDirectory.getIssueStatus();
		if (!CertTypeDirectoryIssueStatusEnum.ABOLISHED.getCode().equals(issueStatus)) {
			throw new IllegalArgumentException("当前证照类型目录状态不能启用，只有已废止状态才能启用");
		}

		try {
			String username = this.sysUserService.getCurrUsername();

			// 将证照类型目录审批状态改为 1-已提交
			LambdaUpdateWrapper<CertTypeDirectory> updateWrapperDir = new LambdaUpdateWrapper<>();
			updateWrapperDir.eq(CertTypeDirectory::getId, certTypeDirectory.getId())
					.set(CertTypeDirectory::getApprovalStatus, "1");
			this.update(updateWrapperDir);
			// 原机构信息
			String orgCode = certTypeApprovalApply.getApplyOrgCode();

			// 4. 生成申请编号
			String applyNo = ApplyNoUtil.getToDayMaxApplyId();


			// 保存审批申请记录
			CertTypeApprovalApply apply = new CertTypeApprovalApply();
			apply.setApplyNo(applyNo);
			apply.setCertTypeDirId(certTypeDirectory.getId());
			apply.setApplyType("3"); // 启用
			apply.setApplyTime(new Date());
			boolean isBureauUser = setUserInfo(apply, orgCode);
			apply.setCurrentNode("1"); // 申请
			apply.setStatus("1"); // 待申请
			apply.setIssueStatus("1");
			apply.setCreateBy(username);
			apply.setCreateTime(new Date());
			// 开启工作流
			LawWorkflowNodeUserListDto dto = new LawWorkflowNodeUserListDto();
			List<String> orgCodes = new ArrayList<>();
			orgCodes.add(orgCode);
			dto.setOrgCode(orgCodes);
			dto.setLawWorkflowNodeSetId("T003");
			dto.setLawWorkflowNodeInfoId(isBureauUser ? "N006" : "N005");
			CurrentUser currUser = sysUserService.getCurrUser();
			String executionId = workflowService.startWorkflow(currUser.getUsername(),dto);
			apply.setExecutionId(executionId);
			// 7. 保存申请记录
			boolean success = certApprovalApplyService.save(apply);
			if (!success) {
				throw new RuntimeException("保存审批申请记录失败");
			}

			log.info("启用证照类型目录申请提交成功，申请编号：{}", applyNo);
		} catch (Exception e) {
			log.error("启用证照类型目录失败", e);
			throw new RuntimeException("启用申请失败：" + e.getMessage());
		}
	}

	/**
	 * 删除证照类型目录
	 */
	@Transactional(rollbackFor = Exception.class)
	public void deleteCertTypeDirectory(String certTypeDirId) {
		log.info("开始删除证照类型目录，ID：{}", certTypeDirId);

		// 1. 查询证照类型目录
		CertTypeDirectory certTypeDirectory = this.getById(certTypeDirId);
		if (certTypeDirectory == null) {
			throw new IllegalArgumentException("证照类型目录不存在");
		}

		// 2. 校验状态
		if (!"0".equals(certTypeDirectory.getApprovalStatus())) {
			throw new IllegalArgumentException("当前证照类型目录状态不能删除，只有待提交状态才能删除");
		}

		try {
			// 3. 逻辑删除证照类型目录
			certTypeDirectory.setDelFlag("1");
			this.updateById(certTypeDirectory);

			// 4. 逻辑删除关联的审批申请记录
			LambdaUpdateWrapper<CertTypeApprovalApply> applyWrapper = new LambdaUpdateWrapper<>();
			applyWrapper.eq(CertTypeApprovalApply::getCertTypeDirId, certTypeDirId)
					.set(CertTypeApprovalApply::getDelFlag, "1");
			certApprovalApplyService.update(applyWrapper);

			// 5. 逻辑删除关联的审核审批记录
			LambdaUpdateWrapper<CertTypeApproval> approvalWrapper = new LambdaUpdateWrapper<>();
			approvalWrapper.eq(CertTypeApproval::getCertTypeDirId, certTypeDirId)
					.set(CertTypeApproval::getDelFlag, "1");
			certTypeApprovalService.update(approvalWrapper);

			log.info("删除证照类型目录成功，ID：{}", certTypeDirId);
		} catch (Exception e) {
			log.error("删除证照类型目录失败", e);
			throw new RuntimeException("删除证照类型目录失败：" + e.getMessage());
		}
	}

	/**
	 * 查询证照类型目录详情
	 */
	public CertTypeDirectoryDetailVO getCertTypeDirInfo(String certTypeApprApplyId) {
		log.info("开始查询证照类型目录详情，ID：{}", certTypeApprApplyId);

		try {
			// 1. 查询证照类型目录详情
			CertTypeDirectoryDetailVO detailVO = certTypeDirectoryDao.getCertTypeDirInfo(certTypeApprApplyId);

			if (detailVO == null || detailVO.getCertTypeDirectory() == null) {
				log.warn("未找到证照类型目录信息，ID：{}", certTypeApprApplyId);
				return null;
			}

			// 2. 查询对应的树结构
			String certTypeDirId = detailVO.getCertTypeDirectory().getId();
			List<CertTypeDirectory> treeList = certTypeDirectoryDao.queryCertTypeDirTree(certTypeDirId);

			// 3. 取出CERTIFICATE_TYPE_NAME字段并拼接
			if (CollUtil.isNotEmpty(treeList)) {
				String certTypeNamePath = treeList.stream()
						.map(CertTypeDirectory::getCertificateTypeName)
						.collect(Collectors.joining("-"));
				log.info("证照类型：{}", certTypeNamePath);
				// 4. 设置拼接结果到detailVO的certificateTypeName属性
				detailVO.getCertTypeDirectory().setCertificateTypeName(certTypeNamePath);
			}

			log.info("查询证照类型目录详情成功");
			return detailVO;
		} catch (Exception e) {
			log.error("查询证照类型目录详情失败", e);
			throw new RuntimeException("查询证照类型目录详情失败：" + e.getMessage());
		}
	}

	public List<CertTypeDirectoryTreeVO> getCertTypeDirectoryTree() {
		// 新增：一次性查询所有节点
		List<CertTypeDirectory> allNodes = baseMapper.selectList(Wrappers.<CertTypeDirectory>lambdaQuery()
				.eq(CertTypeDirectory::getIssueStatus, "2"));
		Map<String, CertTypeDirectoryTreeVO> nodeMap = new HashMap<>();
		List<CertTypeDirectoryTreeVO> treeList = new ArrayList<>();

		// 第一步：将所有节点转换为VO并存入Map
		for (CertTypeDirectory node : allNodes) {
			CertTypeDirectoryTreeVO vo = convertToTreeVO(node);
			nodeMap.put(node.getId(), vo);
		}

		// 第二步：构建父子关系
		for (CertTypeDirectory node : allNodes) {
			CertTypeDirectoryTreeVO vo = nodeMap.get(node.getId());
			String parentId = node.getParentId();
			if ("-1".equals(parentId)) {
				// 根节点直接加入结果集
				treeList.add(vo);
			} else {
				// 将当前节点添加到父节点的children中
				CertTypeDirectoryTreeVO parentVO = nodeMap.get(parentId);
				if (parentVO != null) {
					parentVO.getChildren().add(vo);
				}
			}
		}

		return treeList;
	}

	private CertTypeDirectoryTreeVO convertToTreeVO(CertTypeDirectory entity) {
		CertTypeDirectoryTreeVO vo = new CertTypeDirectoryTreeVO();
		vo.setCertTypeDirId(entity.getId());
		vo.setCertificateTypeName(entity.getCertificateTypeName());
		vo.setParentId(entity.getParentId());
		vo.setChildren(new ArrayList<>());
		return vo;
	}

	private void buildChildrenTree(CertTypeDirectoryTreeVO parentNode) {
		// Get children nodes
		List<CertTypeDirectory> children = baseMapper.selectList(
				new QueryWrapper<CertTypeDirectory>()
						.eq("PARENT_ID", parentNode.getCertTypeDirId()));

		// Recursively build tree
		for (CertTypeDirectory child : children) {
			CertTypeDirectoryTreeVO childNode = convertToTreeVO(child);
			buildChildrenTree(childNode);
			parentNode.getChildren().add(childNode);
		}
	}

	@Transactional(rollbackFor = Exception.class)
	public void batchAbolish(List<String> certTypeApprApplyIds) {
		log.info("开始批量废止证照类型目录，ID列表：{}", certTypeApprApplyIds);

		if (CollUtil.isEmpty(certTypeApprApplyIds)) {
			throw new IllegalArgumentException("办件ID列表不能为空");
		}

		// 1. 检查所有记录的状态
		List<CertTypeApprovalApply> certTypeApprovalApplyList = certApprovalApplyService
				.listByIds(certTypeApprApplyIds);

		// 检查记录是否都存在
		if (certTypeApprovalApplyList.size() != certTypeApprApplyIds.size()) {
			throw new IllegalArgumentException("部分证照类型目录记录不存在");
		}
		for (CertTypeApprovalApply certTypeApprovalApply : certTypeApprovalApplyList) {
			if (!CertTypeDirectoryIssueStatusEnum.ISSUED.getCode().equals(certTypeApprovalApply.getIssueStatus())) {
				throw new IllegalArgumentException(
						String.format("办件[%s]当前状态不能废止，只有已下发状态才能废止",
								certTypeApprovalApply.getId()));
			}
		}
		// 2. 批量处理
		int successCount = 0;
		int failCount = 0;
		StringBuilder failureMessage = new StringBuilder();
		for (CertTypeApprovalApply certTypeApprovalApply : certTypeApprovalApplyList) {
			try {
				// 复用单条废止逻辑
				this.abolish(certTypeApprovalApply.getId());
				successCount++;
			} catch (Exception e) {
				failCount++;
				failureMessage.append(
						String.format("ID[%s]处理失败：%s; ", certTypeApprovalApply.getCertTypeDirId(), e.getMessage()));
				log.error("废止证照类型目录失败，ID：{}，原因：{}", certTypeApprovalApply.getCertTypeDirId(), e.getMessage());
			}
		}
		// 3. 处理结果
		if (failCount > 0) {
			throw new RuntimeException(String.format(
					"批量废止处理完成，总计：%d，成功：%d，失败：%d。失败详情：%s",
					certTypeApprovalApplyList.size(), successCount, failCount, failureMessage.toString()));
		}

		log.info("批量废止证照类型目录完成，总计：{}，成功：{}，失败：{}",
				certTypeApprovalApplyList.size(), successCount, failCount);
	}

}
