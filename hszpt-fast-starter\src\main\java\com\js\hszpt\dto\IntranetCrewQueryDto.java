package com.js.hszpt.dto;

import lombok.Data;

import java.util.List;

@Data
public class IntranetCrewQueryDto {

    /**
     * 登录机构代码
     */
    private String loginOrgCode;

    /**
     * 证书编号
     */
    private String certificateNum;

    /**
     * 持证人姓名
     */
    private String holderName;

    /**
     * 身份证号
     */
    private String holderIdentityNumber;

    /**
     * 证书类型代码
     */
    private String certificateTypeCode;

    /**
     * 证书类型代码 - 统计
     */
    private List<String> certificateTypeCodes;
    /**
     * 证书类型代码
     */
    private String certificateType;

    /**
     * 证书类型目录代码列表
     */
    private List<String> certTypeDirCodes;

    /**
     * 证书状态
     */
    private String certificateStatus;

    /**
     * 证书印刷号
     */
    private String certPrintNo;

    /**
     * 许可证号
     */
    private String permitNumber;

    /**
     * 发证机关
     */
    private String issuingAuthority;

    /**
     * 培训机构
     */
    private String trainingOrg;

    /**
     * 外派机构
     */
    private String foreignOrg;

    /**
     * 主管签发机构
     */
    private String mainSignOrg;

    /**
     * 发证日期-开始
     */
    private String issueDateStart;

    /**
     * 发证日期-结束
     */
    private String issueDateEnd;

    /**
     * 发证机构代码2.0
     */
    private String issueDeptCode2;

    /**
     * 发证机构代码3.0
     */
    private String issuOrgCode3;

    /**
     * 发证机构代码（统计模块查看明细）
     */
    private String orgCode;

    /**
     * 机构类型
     */
    private String orgType;

    /**
     * 出生年月日-yyyymmdd
     */
    private String birth;

    /**
     * 状态
     */
    private String statusFlag;

    /**
     * 生效日期-开始
     */
    private String effectDateStart;

    /**
     * 生效日期-结束
     */
    private String effectDateEnd;

    /**
     * 失效日期-开始
     */
    private String expireDateStart;

    /**
     * 失效日期-结束
     */
    private String expireDateEnd;

    /**
     * 船名
     */
    private String shipName;

    //出生日期开始时间
    private String birthDateStart;

    //出生日期结束时间
    private String birthDateEnd;

    /**
     * 证书持证人类别
     */
    private String certificateHolderCategory;

    /**
     * 关联事项名称
     */
    private String relatedMatterName;

    /**
     * 统计证书类型代码
     */
    private String statCertTypeCode;

    /**
     * 请求来源（1-内网船员查询 空-统计）
     * 统计会同时查询船员、船舶证照
     */
    private String sourceType;

    private long current = 1;

    private long size = 10;

}
