package com.js.hszpt.until;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @date 20220808
 * @desc 加到形参上控制获取属性的个数，不加此注解仅能自动获取到userId和username
 */
@Retention(value = RetentionPolicy.RUNTIME)
@Target(ElementType.PARAMETER)
public @interface BaseParam {

    /**
     * 默认值为false，设置为true自动获取当前用户身份证号，
     * 设置为true会自动填充idNumber、sysUser
     * @return
     */
    boolean needIdNumber() default false;

    /**
     * 默认值为false,设置为true自动获取当前用户的所属角色
     * 包含needIdNumber
     * @return
     */
    boolean needRoles() default false;

    /**
     * 默认值为false,设置为true自动获取当前用户的所属机构
     * 包含needIdNumber
     * @return
     */
    boolean needSysDept() default false;

    /**
     * 默认值为false，设置为true自动获取所有子机构（包含本机构）
     * 包含needIdNumber
     * @return
     */
//    boolean needChildOrgs() default false;

    /**
     * 默认值为false，设置为true自动获取当前用户的执法证信息
     * 包含needIdNumber
     * @return
     */
    boolean needCertification() default false;

    /**
     * 默认值为false，设置为true自动所有基础数据
     * @return
     */
    boolean needAll() default false;

}
