package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@TableName("CTF_OFFICER_INFO")
@ApiModel(value = "签证官员表")
public class OfficerInfo {

    // id
    @TableId
    private String officerInfoId;

    // 官员姓名(中文)
    private String officerName;

    // 官员姓名(英文)
    private String officialNameEn;

    // 性别： 0：男 1：女
    private String sex;

    // 所在单位名称(中文)
    private String department;

    // 所在单位名称(英文)
    private String departmentEn;

    // 所在机构代码
    private String orgCode;

    // 职务
    private String position;

    // 职务英文
    private String positionEn;

    // 创建人
    private String createOperId;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    // 创建时间
    private Date createDate;

    // 修改人
    private String modifyOperId;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    // 修改时间
    private Date modifyDate;

    // 逻辑删除标记(0--正常 1--删除)
    private String delFlag;
}
