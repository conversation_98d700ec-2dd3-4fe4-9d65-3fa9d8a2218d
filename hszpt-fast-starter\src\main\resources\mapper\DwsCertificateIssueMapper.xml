<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.js.hszpt.mapper.DwsCertificateIssueMapper">

    <!-- 统计证照签发情况 -->
    <select id="statisticsCertificateIssue" resultType="com.js.hszpt.dto.CertificateIssueStatisticsDTO">
        SELECT
        d.DEPT_ID as orgCode,
        d.NAME as orgName,
        SUM(i.issue_count) as issueCount
        FROM
        dws_certificate_issue i
        LEFT JOIN
        sys_dept d ON SUBSTR(i.issuer_code, 1, LENGTH(d.DEPT_ID)) = d.DEPT_ID
        WHERE
        i.issuer_code LIKE CONCAT(#{param.loginUserOrgCode}, '%')
        AND d.PARENT_ID = #{param.loginUserOrgCode}
        <if test="param.issuerCodes != null and param.issuerCodes.size() > 0">
            AND (
            <foreach collection="param.issuerCodes" item="issuerCode" separator=" OR ">
                i.issuer_code LIKE CONCAT(#{issuerCode}, '%')
            </foreach>
            )
        </if>
        <if test="param.certificateTypeCodes != null and param.certificateTypeCodes.size() > 0">
            AND EXISTS (
            SELECT 1 FROM ert_type_directory etd
            WHERE etd.CERTIFICATE_TYPE_CODE = i.certificate_type_code
            AND (
            <foreach collection="param.certificateTypeCodes" item="typeCode" separator=" OR ">
                etd.CERTIFICATE_TYPE_CODE = #{typeCode}
                OR EXISTS (
                SELECT 1 FROM ert_type_directory etd2
                WHERE etd2.CERTIFICATE_TYPE_CODE = #{typeCode}
                AND etd.PARENT_ID = etd2.CERT_TYPE_DIR_ID
                )
            </foreach>
            )
            )
        </if>
        <if test="param.holderCategoryCodes != null and param.holderCategoryCodes.size() > 0">
            AND i.holder_category_code IN
            <foreach collection="param.holderCategoryCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.matterNatureCodes != null and param.matterNatureCodes.size() > 0">
            AND i.matter_nature_code IN
            <foreach collection="param.matterNatureCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.applicationSourceCode != null and param.applicationSourceCode != ''">
            AND i.application_source_code = #{param.applicationSourceCode}
        </if>
        <if test="param.statusCode != null and param.statusCode != ''">
            AND i.status_code = #{param.statusCode}
        </if>
        <if test="param.timeType == 'year' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND SUBSTR(i.ds_certificate_date, 1, 4) BETWEEN #{param.startTime} AND #{param.endTime}
        </if>
        <if test="param.timeType == 'quarter' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND CONCAT(SUBSTR(i.ds_certificate_date, 1, 4),
            CASE
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
            BETWEEN
            CONCAT(SUBSTR(#{param.startTime}, 1, 4),
            CASE
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
            AND
            CONCAT(SUBSTR(#{param.endTime}, 1, 4),
            CASE
            WHEN SUBSTR(#{param.endTime}, 5, 2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(#{param.endTime}, 5, 2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(#{param.endTime}, 5, 2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(#{param.endTime}, 5, 2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
        </if>
        <if test="param.timeType == 'month' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND SUBSTR(i.ds_certificate_date, 1, 6) BETWEEN REPLACE(#{param.startTime}, '-', '') AND REPLACE(#{param.endTime}, '-', '')
        </if>
        <if test="param.timeType == 'day' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND i.ds_certificate_date BETWEEN REPLACE(#{param.startTime}, '-', '') AND REPLACE(#{param.endTime}, '-', '')
        </if>
        <if test="param.relatedMatterName != null and param.relatedMatterName != ''">
            <if test="param.relatedMatterName.startsWith('#') and param.relatedMatterName.endsWith('%')">
                AND EXISTS (
                SELECT 1 FROM ctf_certificate_type cct
                WHERE cct.CERTIFICATE_TYPE_ID = i.certificate_id
                AND cct.DESCRIPTION LIKE #{param.relatedMatterName}
                )
            </if>
            <if test="!param.relatedMatterName.startsWith('#') or !param.relatedMatterName.endsWith('%')">
                AND EXISTS (
                SELECT 1 FROM ctf_certificate_type cct
                WHERE cct.CERTIFICATE_TYPE_ID = i.certificate_id
                AND cct.DESCRIPTION IN
                <foreach collection="param.relatedMatterName.split(',')" item="name" open="(" separator="," close=")">
                    #{name}
                </foreach>
                )
            </if>
        </if>
        <if test="param.certificateIds != null and param.certificateIds.size() > 0">
            AND i.certificate_id IN
            <foreach collection="param.certificateIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        GROUP BY d.DEPT_ID, d.NAME
    </select>

    <!-- 按年统计证照签发情况 -->
    <select id="statisticsCertificateIssueByYear" resultType="com.js.hszpt.dto.CertificateIssueTimeTypeStatisticsDTO">
        SELECT
        d.DEPT_ID as orgCode,
        d.NAME as orgName,
        SUBSTR(i.ds_certificate_date, 1, 4) as year,
        SUM(i.issue_count) as issueCount
        FROM
        dws_certificate_issue i
        LEFT JOIN
        sys_dept d ON SUBSTR(i.issuer_code, 1, LENGTH(d.DEPT_ID)) = d.DEPT_ID
        WHERE
        i.issuer_code LIKE CONCAT(#{param.loginUserOrgCode}, '%')
        AND d.PARENT_ID = #{param.loginUserOrgCode}
        <if test="param.issuerCodes != null and param.issuerCodes.size() > 0">
            AND (
            <foreach collection="param.issuerCodes" item="issuerCode" separator=" OR ">
                i.issuer_code LIKE CONCAT(#{issuerCode}, '%')
            </foreach>
            )
        </if>
        <if test="param.certificateTypeCodes != null and param.certificateTypeCodes.size() > 0">
            AND EXISTS (
            SELECT 1 FROM ert_type_directory etd
            WHERE etd.CERTIFICATE_TYPE_CODE = i.certificate_type_code
            AND (
            <foreach collection="param.certificateTypeCodes" item="typeCode" separator=" OR ">
                etd.CERTIFICATE_TYPE_CODE = #{typeCode}
                OR EXISTS (
                SELECT 1 FROM ert_type_directory etd2
                WHERE etd2.CERTIFICATE_TYPE_CODE = #{typeCode}
                AND etd.PARENT_ID = etd2.CERT_TYPE_DIR_ID
                )
            </foreach>
            )
            )
        </if>
        <if test="param.holderCategoryCodes != null and param.holderCategoryCodes.size() > 0">
            AND i.holder_category_code IN
            <foreach collection="param.holderCategoryCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.matterNatureCodes != null and param.matterNatureCodes.size() > 0">
            AND i.matter_nature_code IN
            <foreach collection="param.matterNatureCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.applicationSourceCode != null and param.applicationSourceCode != ''">
            AND i.application_source_code = #{param.applicationSourceCode}
        </if>
        <if test="param.statusCode != null and param.statusCode != ''">
            AND i.status_code = #{param.statusCode}
        </if>
        <if test="param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND SUBSTR(i.ds_certificate_date, 1, 4) BETWEEN #{param.startTime} AND #{param.endTime}
        </if>
        <if test="param.relatedMatterName != null and param.relatedMatterName != ''">
            <if test="param.relatedMatterName.startsWith('#') and param.relatedMatterName.endsWith('%')">
                AND EXISTS (
                SELECT 1 FROM ctf_certificate_type cct
                WHERE cct.CERTIFICATE_TYPE_ID = i.certificate_id
                AND cct.DESCRIPTION LIKE #{param.relatedMatterName}
                )
            </if>
            <if test="!param.relatedMatterName.startsWith('#') or !param.relatedMatterName.endsWith('%')">
                AND EXISTS (
                SELECT 1 FROM ctf_certificate_type cct
                WHERE cct.CERTIFICATE_TYPE_ID = i.certificate_id
                AND cct.DESCRIPTION IN
                <foreach collection="param.relatedMatterName.split(',')" item="name" open="(" separator="," close=")">
                    #{name}
                </foreach>
                )
            </if>
        </if>
        <if test="param.certificateIds != null and param.certificateIds.size() > 0">
            AND i.certificate_id IN
            <foreach collection="param.certificateIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        GROUP BY d.DEPT_ID, d.NAME, SUBSTR(i.ds_certificate_date, 1, 4)
        ORDER BY d.DEPT_ID, SUBSTR(i.ds_certificate_date, 1, 4)
    </select>

    <!-- 详细统计证照签发情况 -->
    <select id="statisticsCertificateIssueDetail" resultType="com.js.hszpt.dto.CertificateIssueDetailStatisticsDTO">
        SELECT
        d.DEPT_ID as orgCode,
        d.NAME as orgName,
        SUM(i.issue_count) as issueCount,
        SUM(CASE WHEN i.application_source_code = '01' THEN i.issue_count ELSE 0 END) as counterIssueCount,
        SUM(CASE WHEN i.application_source_code = '02' THEN i.issue_count ELSE 0 END) as onlineIssueCount
        FROM
        dws_certificate_issue i
        LEFT JOIN
        sys_dept d ON SUBSTR(i.issuer_code, 1, LENGTH(d.DEPT_ID)) = d.DEPT_ID
        WHERE
        i.issuer_code LIKE CONCAT(#{param.loginUserOrgCode}, '%')
        AND d.PARENT_ID = #{param.loginUserOrgCode}
        <if test="param.issuerCodes != null and param.issuerCodes.size() > 0">
            AND (
            <foreach collection="param.issuerCodes" item="issuerCode" separator=" OR ">
                i.issuer_code LIKE CONCAT(#{issuerCode}, '%')
            </foreach>
            )
        </if>
        <if test="param.certificateTypeCodes != null and param.certificateTypeCodes.size() > 0">
            AND EXISTS (
            SELECT 1 FROM ert_type_directory etd
            WHERE etd.CERTIFICATE_TYPE_CODE = i.certificate_type_code
            AND (
            <foreach collection="param.certificateTypeCodes" item="typeCode" separator=" OR ">
                etd.CERTIFICATE_TYPE_CODE = #{typeCode}
                OR EXISTS (
                SELECT 1 FROM ert_type_directory etd2
                WHERE etd2.CERTIFICATE_TYPE_CODE = #{typeCode}
                AND etd.PARENT_ID = etd2.CERT_TYPE_DIR_ID
                )
            </foreach>
            )
            )
        </if>
        <if test="param.holderCategoryCodes != null and param.holderCategoryCodes.size() > 0">
            AND i.holder_category_code IN
            <foreach collection="param.holderCategoryCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.matterNatureCodes != null and param.matterNatureCodes.size() > 0">
            AND i.matter_nature_code IN
            <foreach collection="param.matterNatureCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.applicationSourceCode != null and param.applicationSourceCode != ''">
            AND i.application_source_code = #{param.applicationSourceCode}
        </if>
        <if test="param.statusCode != null and param.statusCode != ''">
            AND i.status_code = #{param.statusCode}
        </if>
        <if test="param.timeType == 'year' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND SUBSTR(i.ds_certificate_date, 1, 4) BETWEEN #{param.startTime} AND #{param.endTime}
        </if>
        <if test="param.timeType == 'quarter' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND CONCAT(SUBSTR(i.ds_certificate_date, 1, 4),
            CASE
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
            BETWEEN
            CONCAT(SUBSTR(#{param.startTime}, 1, 4),
            CASE
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
            AND
            CONCAT(SUBSTR(#{param.endTime}, 1, 4),
            CASE
            WHEN SUBSTR(#{param.endTime}, 5, 2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(#{param.endTime}, 5, 2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(#{param.endTime}, 5, 2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(#{param.endTime}, 5, 2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
        </if>
        <if test="param.timeType == 'month' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND SUBSTR(i.ds_certificate_date, 1, 6) BETWEEN REPLACE(#{param.startTime}, '-', '') AND REPLACE(#{param.endTime}, '-', '')
        </if>
        <if test="param.timeType == 'day' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND i.ds_certificate_date BETWEEN REPLACE(#{param.startTime}, '-', '') AND REPLACE(#{param.endTime}, '-', '')
        </if>
        <if test="param.relatedMatterName != null and param.relatedMatterName != ''">
            <if test="param.relatedMatterName.startsWith('#') and param.relatedMatterName.endsWith('%')">
                AND EXISTS (
                SELECT 1 FROM ctf_certificate_type cct
                WHERE cct.CERTIFICATE_TYPE_ID = i.certificate_id
                AND cct.DESCRIPTION LIKE #{param.relatedMatterName}
                )
            </if>
            <if test="!param.relatedMatterName.startsWith('#') or !param.relatedMatterName.endsWith('%')">
                AND EXISTS (
                SELECT 1 FROM ctf_certificate_type cct
                WHERE cct.CERTIFICATE_TYPE_ID = i.certificate_id
                AND cct.DESCRIPTION IN
                <foreach collection="param.relatedMatterName.split(',')" item="name" open="(" separator="," close=")">
                    #{name}
                </foreach>
                )
            </if>
        </if>
        <if test="param.certificateIds != null and param.certificateIds.size() > 0">
            AND i.certificate_id IN
            <foreach collection="param.certificateIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        GROUP BY d.DEPT_ID, d.NAME
    </select>

    <!-- 获取上一个周期的签发总量 -->
    <select id="getPreviousPeriodIssueCount" resultType="java.lang.Long">
        SELECT
        SUM(i.issue_count) as issueCount
        FROM
        dws_certificate_issue i
        WHERE
        i.issuer_code LIKE CONCAT(#{orgCode}, '%')
        <if test="param.timeType == 'year' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND SUBSTR(i.ds_certificate_date, 1, 4) BETWEEN
            CAST(#{param.startTime} AS INTEGER) - 1 AND CAST(#{param.endTime} AS INTEGER) - 1
        </if>
        <if test="param.timeType == 'quarter' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND (
            CASE
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '01' AND '03' THEN
            (i.ds_certificate_date BETWEEN
            CONCAT(CAST(SUBSTR(#{param.startTime}, 1, 4) AS INTEGER) - 1, '10', '01') AND
            CONCAT(CAST(SUBSTR(#{param.startTime}, 1, 4) AS INTEGER) - 1, '12', '31'))
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '04' AND '06' THEN
            (i.ds_certificate_date BETWEEN
            CONCAT(SUBSTR(#{param.startTime}, 1, 4), '01', '01') AND
            CONCAT(SUBSTR(#{param.startTime}, 1, 4), '03', '31'))
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '07' AND '09' THEN
            (i.ds_certificate_date BETWEEN
            CONCAT(SUBSTR(#{param.startTime}, 1, 4), '04', '01') AND
            CONCAT(SUBSTR(#{param.startTime}, 1, 4), '06', '30'))
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '10' AND '12' THEN
            (i.ds_certificate_date BETWEEN
            CONCAT(SUBSTR(#{param.startTime}, 1, 4), '07', '01') AND
            CONCAT(SUBSTR(#{param.startTime}, 1, 4), '09', '30'))
            END
            )
        </if>
        <if test="param.timeType == 'month' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND (
            CASE
            WHEN SUBSTR(#{param.startTime}, 5, 2) = '01' THEN
            (i.ds_certificate_date BETWEEN
            CONCAT(CAST(SUBSTR(#{param.startTime}, 1, 4) AS INTEGER) - 1, '12', '01') AND
            CONCAT(CAST(SUBSTR(#{param.startTime}, 1, 4) AS INTEGER) - 1, '12', '31'))
            ELSE
            (SUBSTR(i.ds_certificate_date, 1, 6) =
            CONCAT(
            SUBSTR(#{param.startTime}, 1, 4),
            LPAD(CAST(SUBSTR(#{param.startTime}, 5, 2) AS INTEGER) - 1, 2, '0')
            )
            )
            END
            )
        </if>
        <if test="param.timeType == 'day' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND i.ds_certificate_date BETWEEN
            TO_CHAR(TO_DATE(REPLACE(#{param.startTime}, '-', ''), 'YYYYMMDD') -
            (TO_DATE(REPLACE(#{param.endTime}, '-', ''), 'YYYYMMDD') -
            TO_DATE(REPLACE(#{param.startTime}, '-', ''), 'YYYYMMDD') + 1), 'YYYYMMDD')
            AND
            TO_CHAR(TO_DATE(REPLACE(#{param.endTime}, '-', ''), 'YYYYMMDD') -
            (TO_DATE(REPLACE(#{param.endTime}, '-', ''), 'YYYYMMDD') -
            TO_DATE(REPLACE(#{param.startTime}, '-', ''), 'YYYYMMDD') + 1), 'YYYYMMDD')
        </if>
        <if test="param.certificateTypeCodes != null and param.certificateTypeCodes.size() > 0">
            AND EXISTS (
            SELECT 1 FROM ert_type_directory etd
            WHERE etd.CERTIFICATE_TYPE_CODE = i.certificate_type_code
            AND (
            <foreach collection="param.certificateTypeCodes" item="typeCode" separator=" OR ">
                etd.CERTIFICATE_TYPE_CODE = #{typeCode}
                OR EXISTS (
                SELECT 1 FROM ert_type_directory etd2
                WHERE etd2.CERTIFICATE_TYPE_CODE = #{typeCode}
                AND etd.PARENT_ID = etd2.CERT_TYPE_DIR_ID
                )
            </foreach>
            )
            )
        </if>
        <if test="param.holderCategoryCodes != null and param.holderCategoryCodes.size() > 0">
            AND i.holder_category_code IN
            <foreach collection="param.holderCategoryCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.matterNatureCodes != null and param.matterNatureCodes.size() > 0">
            AND i.matter_nature_code IN
            <foreach collection="param.matterNatureCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.applicationSourceCode != null and param.applicationSourceCode != ''">
            AND i.application_source_code = #{param.applicationSourceCode}
        </if>
        <if test="param.statusCode != null and param.statusCode != ''">
            AND i.status_code = #{param.statusCode}
        </if>
    </select>

    <!-- 获取当前周期的总签发量 -->
    <select id="getTotalIssueCount" resultType="java.lang.Long">
        SELECT
        SUM(i.issue_count) as issueCount
        FROM
        dws_certificate_issue i
        WHERE
        i.issuer_code LIKE CONCAT(#{param.loginUserOrgCode}, '%')
        <if test="param.issuerCodes != null and param.issuerCodes.size() > 0">
            AND (
            <foreach collection="param.issuerCodes" item="issuerCode" separator=" OR ">
                i.issuer_code LIKE CONCAT(#{issuerCode}, '%')
            </foreach>
            )
        </if>
        <if test="param.certificateTypeCodes != null and param.certificateTypeCodes.size() > 0">
            AND EXISTS (
            SELECT 1 FROM ert_type_directory etd
            WHERE etd.CERTIFICATE_TYPE_CODE = i.certificate_type_code
            AND (
            <foreach collection="param.certificateTypeCodes" item="typeCode" separator=" OR ">
                etd.CERTIFICATE_TYPE_CODE = #{typeCode}
                OR EXISTS (
                SELECT 1 FROM ert_type_directory etd2
                WHERE etd2.CERTIFICATE_TYPE_CODE = #{typeCode}
                AND etd.PARENT_ID = etd2.CERT_TYPE_DIR_ID
                )
            </foreach>
            )
            )
        </if>
        <if test="param.holderCategoryCodes != null and param.holderCategoryCodes.size() > 0">
            AND i.holder_category_code IN
            <foreach collection="param.holderCategoryCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.matterNatureCodes != null and param.matterNatureCodes.size() > 0">
            AND i.matter_nature_code IN
            <foreach collection="param.matterNatureCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.applicationSourceCode != null and param.applicationSourceCode != ''">
            AND i.application_source_code = #{param.applicationSourceCode}
        </if>
        <if test="param.statusCode != null and param.statusCode != ''">
            AND i.status_code = #{param.statusCode}
        </if>
        <if test="param.timeType == 'year' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND SUBSTR(i.ds_certificate_date, 1, 4) BETWEEN #{param.startTime} AND #{param.endTime}
        </if>
        <if test="param.timeType == 'quarter' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND CONCAT(SUBSTR(i.ds_certificate_date, 1, 4),
            CASE
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
            BETWEEN
            CONCAT(SUBSTR(#{param.startTime}, 1, 4),
            CASE
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
            AND
            CONCAT(SUBSTR(#{param.endTime}, 1, 4),
            CASE
            WHEN SUBSTR(#{param.endTime}, 5, 2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(#{param.endTime}, 5, 2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(#{param.endTime}, 5, 2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(#{param.endTime}, 5, 2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
        </if>
        <if test="param.timeType == 'month' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND SUBSTR(i.ds_certificate_date, 1, 6) BETWEEN REPLACE(#{param.startTime}, '-', '') AND REPLACE(#{param.endTime}, '-', '')
        </if>
        <if test="param.timeType == 'day' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND i.ds_certificate_date BETWEEN REPLACE(#{param.startTime}, '-', '') AND REPLACE(#{param.endTime}, '-', '')
        </if>
        <if test="param.relatedMatterName != null and param.relatedMatterName != ''">
            <if test="param.relatedMatterName.startsWith('#') and param.relatedMatterName.endsWith('%')">
                AND EXISTS (
                SELECT 1 FROM ctf_certificate_type cct
                WHERE cct.CERTIFICATE_TYPE_ID = i.certificate_id
                AND cct.DESCRIPTION LIKE #{param.relatedMatterName}
                )
            </if>
            <if test="!param.relatedMatterName.startsWith('#') or !param.relatedMatterName.endsWith('%')">
                AND EXISTS (
                SELECT 1 FROM ctf_certificate_type cct
                WHERE cct.CERTIFICATE_TYPE_ID = i.certificate_id
                AND cct.DESCRIPTION IN
                <foreach collection="param.relatedMatterName.split(',')" item="name" open="(" separator="," close=")">
                    #{name}
                </foreach>
                )
            </if>
        </if>
        <if test="param.certificateIds != null and param.certificateIds.size() > 0">
            AND i.certificate_id IN
            <foreach collection="param.certificateIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <!-- 根据机构编码获取机构名称 -->
    <select id="getOrgNameByOrgCode" resultType="java.lang.String">
        SELECT NAME FROM sys_dept WHERE DEPT_ID = #{orgCode}
    </select>

    <!-- 按证照类型统计签发情况 -->
    <select id="statisticsByCertificateType" resultType="com.js.hszpt.dto.CertificateTypeStatisticsDTO">
        SELECT
        i.certificate_type_code as certificateTypeCode,
        etd.CERTIFICATE_TYPE_NAME as certificateTypeName,
        first_level.CERTIFICATE_TYPE_CODE as firstLevelTypeCode,
        first_level.CERTIFICATE_TYPE_NAME as firstLevelTypeName,
        SUM(i.issue_count) as issueCount
        FROM
        dws_certificate_issue i
        LEFT JOIN
        ert_type_directory etd ON i.certificate_type_code = etd.CERTIFICATE_TYPE_CODE
        LEFT JOIN
        (
        WITH RECURSIVE cert_type_hierarchy AS (
        -- 基础查询：获取所有证照类型
        SELECT
        etd1.CERT_TYPE_DIR_ID,
        etd1.CERTIFICATE_TYPE_CODE,
        etd1.CERTIFICATE_TYPE_NAME,
        etd1.PARENT_ID,
        1 as level
        FROM
        ert_type_directory etd1
        WHERE
        etd1.DEL_FLAG = '0'

        UNION ALL

        -- 递归查询：获取父级证照类型
        SELECT
        etd2.CERT_TYPE_DIR_ID,
        etd2.CERTIFICATE_TYPE_CODE,
        etd2.CERTIFICATE_TYPE_NAME,
        etd2.PARENT_ID,
        cth.level + 1
        FROM
        ert_type_directory etd2
        JOIN
        cert_type_hierarchy cth ON etd2.CERT_TYPE_DIR_ID = cth.PARENT_ID
        WHERE
        etd2.DEL_FLAG = '0'
        )
        -- 获取每个证照类型的第一级证照类型（PARENT_ID为空或0的）
        SELECT
        cth1.CERTIFICATE_TYPE_CODE as child_code,
        cth2.CERTIFICATE_TYPE_CODE,
        cth2.CERTIFICATE_TYPE_NAME
        FROM
        cert_type_hierarchy cth1
        JOIN
        cert_type_hierarchy cth2 ON (
        (cth2.PARENT_ID IS NULL OR cth2.PARENT_ID = '0')
        AND EXISTS (
        SELECT 1 FROM cert_type_hierarchy cth3
        WHERE cth3.CERTIFICATE_TYPE_CODE = cth1.CERTIFICATE_TYPE_CODE
        AND (
        cth3.CERTIFICATE_TYPE_CODE = cth2.CERTIFICATE_TYPE_CODE
        OR EXISTS (
        SELECT 1 FROM cert_type_hierarchy cth4
        WHERE cth4.CERTIFICATE_TYPE_CODE = cth3.CERTIFICATE_TYPE_CODE
        START WITH cth4.PARENT_ID = cth2.CERT_TYPE_DIR_ID
        CONNECT BY PRIOR cth4.CERT_TYPE_DIR_ID = cth4.PARENT_ID
        )
        )
        )
        )
        ) first_level ON etd.CERTIFICATE_TYPE_CODE = first_level.child_code
        WHERE
        i.issuer_code LIKE CONCAT(#{param.loginUserOrgCode}, '%')
        <if test="param.issuerCodes != null and param.issuerCodes.size() > 0">
            AND (
            <foreach collection="param.issuerCodes" item="issuerCode" separator=" OR ">
                i.issuer_code LIKE CONCAT(#{issuerCode}, '%')
            </foreach>
            )
        </if>
        <if test="param.certificateTypeCodes != null and param.certificateTypeCodes.size() > 0">
            AND EXISTS (
            SELECT 1 FROM ert_type_directory etd_filter
            WHERE etd_filter.CERTIFICATE_TYPE_CODE = i.certificate_type_code
            AND (
            <foreach collection="param.certificateTypeCodes" item="typeCode" separator=" OR ">
                etd_filter.CERTIFICATE_TYPE_CODE = #{typeCode}
                OR EXISTS (
                SELECT 1 FROM ert_type_directory etd2
                WHERE etd2.CERTIFICATE_TYPE_CODE = #{typeCode}
                AND etd_filter.PARENT_ID = etd2.CERT_TYPE_DIR_ID
                )
            </foreach>
            )
            )
        </if>
        <if test="param.holderCategoryCodes != null and param.holderCategoryCodes.size() > 0">
            AND i.holder_category_code IN
            <foreach collection="param.holderCategoryCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.matterNatureCodes != null and param.matterNatureCodes.size() > 0">
            AND i.matter_nature_code IN
            <foreach collection="param.matterNatureCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.applicationSourceCode != null and param.applicationSourceCode != ''">
            AND i.application_source_code = #{param.applicationSourceCode}
        </if>
        <if test="param.statusCode != null and param.statusCode != ''">
            AND i.status_code = #{param.statusCode}
        </if>
        <if test="param.timeType == 'year' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND SUBSTR(i.ds_certificate_date, 1, 4) BETWEEN #{param.startTime} AND #{param.endTime}
        </if>
        <if test="param.timeType == 'quarter' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND CONCAT(SUBSTR(i.ds_certificate_date, 1, 4),
            CASE
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
            BETWEEN
            CONCAT(SUBSTR(#{param.startTime}, 1, 4),
            CASE
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
            AND
            CONCAT(SUBSTR(#{param.endTime}, 1, 4),
            CASE
            WHEN SUBSTR(#{param.endTime}, 5, 2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(#{param.endTime}, 5, 2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(#{param.endTime}, 5, 2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(#{param.endTime}, 5, 2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
        </if>
        <if test="param.timeType == 'month' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND SUBSTR(i.ds_certificate_date, 1, 6) BETWEEN REPLACE(#{param.startTime}, '-', '') AND REPLACE(#{param.endTime}, '-', '')
        </if>
        <if test="param.timeType == 'day' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND i.ds_certificate_date BETWEEN REPLACE(#{param.startTime}, '-', '') AND REPLACE(#{param.endTime}, '-', '')
        </if>
        <if test="param.relatedMatterName != null and param.relatedMatterName != ''">
            <if test="param.relatedMatterName.startsWith('#') and param.relatedMatterName.endsWith('%')">
                AND EXISTS (
                SELECT 1 FROM ctf_certificate_type cct
                WHERE cct.CERTIFICATE_TYPE_ID = i.certificate_id
                AND cct.DESCRIPTION LIKE #{param.relatedMatterName}
                )
            </if>
            <if test="!param.relatedMatterName.startsWith('#') or !param.relatedMatterName.endsWith('%')">
                AND EXISTS (
                SELECT 1 FROM ctf_certificate_type cct
                WHERE cct.CERTIFICATE_TYPE_ID = i.certificate_id
                AND cct.DESCRIPTION IN
                <foreach collection="param.relatedMatterName.split(',')" item="name" open="(" separator="," close=")">
                    #{name}
                </foreach>
                )
            </if>
        </if>
        <if test="param.certificateIds != null and param.certificateIds.size() > 0">
            AND i.certificate_id IN
            <foreach collection="param.certificateIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        GROUP BY
        i.certificate_type_code,
        etd.CERTIFICATE_TYPE_NAME,
        first_level.CERTIFICATE_TYPE_CODE,
        first_level.CERTIFICATE_TYPE_NAME
        ORDER BY
        first_level.CERTIFICATE_TYPE_CODE,
        i.certificate_type_code
    </select>

    <!-- 按证照类型和状态统计签发情况 -->
    <select id="statisticsByCertificateTypeAndStatus" resultType="com.js.hszpt.dto.CertificateTypeStatusStatisticsDTO">
        SELECT
        i.certificate_type_code as certificateTypeCode,
        etd.CERTIFICATE_TYPE_NAME as certificateTypeName,
        SUM(i.issue_count) as issueCount,
        SUM(CASE WHEN i.status_code = '01' THEN i.issue_count ELSE 0 END) as validCount,
        SUM(CASE WHEN i.status_code = '02' THEN i.issue_count ELSE 0 END) as invalidCount
        FROM
        dws_certificate_issue i
        LEFT JOIN
        ert_type_directory etd ON i.certificate_type_code = etd.CERTIFICATE_TYPE_CODE
        WHERE
        i.issuer_code LIKE CONCAT(#{param.loginUserOrgCode}, '%')
        <if test="param.issuerCodes != null and param.issuerCodes.size() > 0">
            AND (
            <foreach collection="param.issuerCodes" item="issuerCode" separator=" OR ">
                i.issuer_code LIKE CONCAT(#{issuerCode}, '%')
            </foreach>
            )
        </if>
        <if test="param.certificateTypeCodes != null and param.certificateTypeCodes.size() > 0">
            AND EXISTS (
            SELECT 1 FROM ert_type_directory etd_filter
            WHERE etd_filter.CERTIFICATE_TYPE_CODE = i.certificate_type_code
            AND (
            <foreach collection="param.certificateTypeCodes" item="typeCode" separator=" OR ">
                etd_filter.CERTIFICATE_TYPE_CODE = #{typeCode}
                OR EXISTS (
                SELECT 1 FROM ert_type_directory etd_child
                WHERE etd_child.CERTIFICATE_TYPE_CODE = etd_filter.CERTIFICATE_TYPE_CODE
                START WITH etd_child.PARENT_ID = (
                SELECT etd_parent.CERT_TYPE_DIR_ID
                FROM ert_type_directory etd_parent
                WHERE etd_parent.CERTIFICATE_TYPE_CODE = #{typeCode}
                )
                CONNECT BY PRIOR etd_child.CERT_TYPE_DIR_ID = etd_child.PARENT_ID
                )
            </foreach>
            )
            )
        </if>
        <if test="param.holderCategoryCodes != null and param.holderCategoryCodes.size() > 0">
            AND i.holder_category_code IN
            <foreach collection="param.holderCategoryCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.matterNatureCodes != null and param.matterNatureCodes.size() > 0">
            AND i.matter_nature_code IN
            <foreach collection="param.matterNatureCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.applicationSourceCode != null and param.applicationSourceCode != ''">
            AND i.application_source_code = #{param.applicationSourceCode}
        </if>
        <if test="param.statusCode != null and param.statusCode != ''">
            AND i.status_code = #{param.statusCode}
        </if>
        <if test="param.timeType == 'year' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND SUBSTR(i.ds_certificate_date, 1, 4) BETWEEN #{param.startTime} AND #{param.endTime}
        </if>
        <if test="param.timeType == 'quarter' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND CONCAT(SUBSTR(i.ds_certificate_date, 1, 4),
            CASE
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
            BETWEEN
            CONCAT(SUBSTR(#{param.startTime}, 1, 4),
            CASE
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
            AND
            CONCAT(SUBSTR(#{param.endTime}, 1, 4),
            CASE
            WHEN SUBSTR(#{param.endTime}, 5, 2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(#{param.endTime}, 5, 2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(#{param.endTime}, 5, 2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(#{param.endTime}, 5, 2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
        </if>
        <if test="param.timeType == 'month' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND SUBSTR(i.ds_certificate_date, 1, 6) BETWEEN REPLACE(#{param.startTime}, '-', '') AND REPLACE(#{param.endTime}, '-', '')
        </if>
        <if test="param.timeType == 'day' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND i.ds_certificate_date BETWEEN REPLACE(#{param.startTime}, '-', '') AND REPLACE(#{param.endTime}, '-', '')
        </if>
        <if test="param.relatedMatterName != null and param.relatedMatterName != ''">
            <if test="param.relatedMatterName.startsWith('#') and param.relatedMatterName.endsWith('%')">
                AND EXISTS (
                SELECT 1 FROM ctf_certificate_type cct
                WHERE cct.CERTIFICATE_TYPE_ID = i.certificate_id
                AND cct.DESCRIPTION LIKE #{param.relatedMatterName}
                )
            </if>
            <if test="!param.relatedMatterName.startsWith('#') or !param.relatedMatterName.endsWith('%')">
                AND EXISTS (
                SELECT 1 FROM ctf_certificate_type cct
                WHERE cct.CERTIFICATE_TYPE_ID = i.certificate_id
                AND cct.DESCRIPTION IN
                <foreach collection="param.relatedMatterName.split(',')" item="name" open="(" separator="," close=")">
                    #{name}
                </foreach>
                )
            </if>
        </if>
        <if test="param.certificateIds != null and param.certificateIds.size() > 0">
            AND i.certificate_id IN
            <foreach collection="param.certificateIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        GROUP BY
        i.certificate_type_code,
        etd.CERTIFICATE_TYPE_NAME
        ORDER BY
        issueCount DESC
    </select>

    <!-- 获取上一个周期按证照类型统计的签发量 -->
    <select id="getPreviousPeriodIssueCountByType" resultType="java.lang.Long">
        SELECT
        SUM(i.issue_count) as issueCount
        FROM
        dws_certificate_issue i
        WHERE
        i.issuer_code LIKE CONCAT(#{param.loginUserOrgCode}, '%')
        AND i.certificate_type_code = #{certificateTypeCode}
        <if test="param.issuerCodes != null and param.issuerCodes.size() > 0">
            AND (
            <foreach collection="param.issuerCodes" item="issuerCode" separator=" OR ">
                i.issuer_code LIKE CONCAT(#{issuerCode}, '%')
            </foreach>
            )
        </if>
        <if test="param.holderCategoryCodes != null and param.holderCategoryCodes.size() > 0">
            AND i.holder_category_code IN
            <foreach collection="param.holderCategoryCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.matterNatureCodes != null and param.matterNatureCodes.size() > 0">
            AND i.matter_nature_code IN
            <foreach collection="param.matterNatureCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.applicationSourceCode != null and param.applicationSourceCode != ''">
            AND i.application_source_code = #{param.applicationSourceCode}
        </if>
        <if test="param.statusCode != null and param.statusCode != ''">
            AND i.status_code = #{param.statusCode}
        </if>
        <if test="param.timeType == 'year'">
            AND SUBSTR(i.ds_certificate_date, 1, 4) = TO_CHAR(ADD_MONTHS(TO_DATE(#{param.startTime}, 'YYYY'), -12), 'YYYY')
        </if>
        <if test="param.timeType == 'quarter'">
            AND SUBSTR(i.ds_certificate_date, 1, 6) BETWEEN
            TO_CHAR(ADD_MONTHS(TO_DATE(SUBSTR(#{param.startTime}, 1, 6), 'YYYYMM'), -3), 'YYYYMM')
            AND
            TO_CHAR(ADD_MONTHS(TO_DATE(SUBSTR(#{param.endTime}, 1, 6), 'YYYYMM'), -3), 'YYYYMM')
        </if>
        <if test="param.timeType == 'month'">
            AND SUBSTR(i.ds_certificate_date, 1, 6) = TO_CHAR(ADD_MONTHS(TO_DATE(REPLACE(#{param.startTime}, '-', ''), 'YYYYMM'), -1), 'YYYYMM')
        </if>
        <if test="param.timeType == 'day'">
            AND i.ds_certificate_date BETWEEN
            TO_CHAR(TO_DATE(REPLACE(#{param.startTime}, '-', ''), 'YYYYMMDD') -
            (TO_DATE(REPLACE(#{param.endTime}, '-', ''), 'YYYYMMDD') - TO_DATE(REPLACE(#{param.startTime}, '-', ''), 'YYYYMMDD') + 1),
            'YYYYMMDD')
            AND
            TO_CHAR(TO_DATE(REPLACE(#{param.startTime}, '-', ''), 'YYYYMMDD') - 1, 'YYYYMMDD')
        </if>
        <if test="param.relatedMatterName != null and param.relatedMatterName != ''">
            <if test="param.relatedMatterName.startsWith('#') and param.relatedMatterName.endsWith('%')">
                AND EXISTS (
                SELECT 1 FROM ctf_certificate_type cct
                WHERE cct.CERTIFICATE_TYPE_ID = i.certificate_id
                AND cct.DESCRIPTION LIKE #{param.relatedMatterName}
                )
            </if>
            <if test="!param.relatedMatterName.startsWith('#') or !param.relatedMatterName.endsWith('%')">
                AND EXISTS (
                SELECT 1 FROM ctf_certificate_type cct
                WHERE cct.CERTIFICATE_TYPE_ID = i.certificate_id
                AND cct.DESCRIPTION IN
                <foreach collection="param.relatedMatterName.split(',')" item="name" open="(" separator="," close=")">
                    #{name}
                </foreach>
                )
            </if>
        </if>
        <if test="param.certificateIds != null and param.certificateIds.size() > 0">
            AND i.certificate_id IN
            <foreach collection="param.certificateIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <!-- 按申请来源统计签发情况 -->
    <select id="statisticsByApplicationSource" resultType="com.js.hszpt.dto.ApplicationSourceStatisticsDTO">
        SELECT
        i.application_source_code as applicationSourceCode,
        (SELECT b.title FROM t_dict a, t_dict_data b
        WHERE a.type = 'apply_type' AND a.id = b.dict_id AND b.value = i.application_source_code) as applicationSourceName,
        SUM(i.issue_count) as issueCount
        FROM
        dws_certificate_issue i
        WHERE
        i.issuer_code LIKE CONCAT(#{param.loginUserOrgCode}, '%')
        <if test="param.issuerCodes != null and param.issuerCodes.size() > 0">
            AND (
            <foreach collection="param.issuerCodes" item="issuerCode" separator=" OR ">
                i.issuer_code LIKE CONCAT(#{issuerCode}, '%')
            </foreach>
            )
        </if>
        <if test="param.certificateTypeCodes != null and param.certificateTypeCodes.size() > 0">
            AND EXISTS (
            SELECT 1 FROM ert_type_directory etd_filter
            WHERE etd_filter.CERTIFICATE_TYPE_CODE = i.certificate_type_code
            AND (
            <foreach collection="param.certificateTypeCodes" item="typeCode" separator=" OR ">
                etd_filter.CERTIFICATE_TYPE_CODE = #{typeCode}
                OR EXISTS (
                SELECT 1 FROM ert_type_directory etd_child
                WHERE etd_child.CERTIFICATE_TYPE_CODE = etd_filter.CERTIFICATE_TYPE_CODE
                START WITH etd_child.PARENT_ID = (
                SELECT etd_parent.CERT_TYPE_DIR_ID
                FROM ert_type_directory etd_parent
                WHERE etd_parent.CERTIFICATE_TYPE_CODE = #{typeCode}
                )
                CONNECT BY PRIOR etd_child.CERT_TYPE_DIR_ID = etd_child.PARENT_ID
                )
            </foreach>
            )
            )
        </if>
        <if test="param.holderCategoryCodes != null and param.holderCategoryCodes.size() > 0">
            AND i.holder_category_code IN
            <foreach collection="param.holderCategoryCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.matterNatureCodes != null and param.matterNatureCodes.size() > 0">
            AND i.matter_nature_code IN
            <foreach collection="param.matterNatureCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.applicationSourceCode != null and param.applicationSourceCode != ''">
            AND i.application_source_code = #{param.applicationSourceCode}
        </if>
        <if test="param.statusCode != null and param.statusCode != ''">
            AND i.status_code = #{param.statusCode}
        </if>
        <if test="param.timeType == 'year' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND SUBSTR(i.ds_certificate_date, 1, 4) BETWEEN #{param.startTime} AND #{param.endTime}
        </if>
        <if test="param.timeType == 'quarter' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND CONCAT(SUBSTR(i.ds_certificate_date, 1, 4),
            CASE
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
            BETWEEN
            CONCAT(SUBSTR(#{param.startTime}, 1, 4),
            CASE
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
            AND
            CONCAT(SUBSTR(#{param.endTime}, 1, 4),
            CASE
            WHEN SUBSTR(#{param.endTime}, 5, 2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(#{param.endTime}, 5, 2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(#{param.endTime}, 5, 2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(#{param.endTime}, 5, 2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
        </if>
        <if test="param.timeType == 'month' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND SUBSTR(i.ds_certificate_date, 1, 6) BETWEEN REPLACE(#{param.startTime}, '-', '') AND REPLACE(#{param.endTime}, '-', '')
        </if>
        <if test="param.timeType == 'day' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND i.ds_certificate_date BETWEEN REPLACE(#{param.startTime}, '-', '') AND REPLACE(#{param.endTime}, '-', '')
        </if>
        <if test="param.relatedMatterName != null and param.relatedMatterName != ''">
            <if test="param.relatedMatterName.startsWith('#') and param.relatedMatterName.endsWith('%')">
                AND EXISTS (
                SELECT 1 FROM ctf_certificate_type cct
                WHERE cct.CERTIFICATE_TYPE_ID = i.certificate_id
                AND cct.DESCRIPTION LIKE #{param.relatedMatterName}
                )
            </if>
            <if test="!param.relatedMatterName.startsWith('#') or !param.relatedMatterName.endsWith('%')">
                AND EXISTS (
                SELECT 1 FROM ctf_certificate_type cct
                WHERE cct.CERTIFICATE_TYPE_ID = i.certificate_id
                AND cct.DESCRIPTION IN
                <foreach collection="param.relatedMatterName.split(',')" item="name" open="(" separator="," close=")">
                    #{name}
                </foreach>
                )
            </if>
        </if>
        <if test="param.certificateIds != null and param.certificateIds.size() > 0">
            AND i.certificate_id IN
            <foreach collection="param.certificateIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        GROUP BY
        i.application_source_code
        ORDER BY
        issueCount DESC
    </select>

    <!-- 按时间统计签发情况 -->
    <select id="statisticsByTime" resultType="com.js.hszpt.dto.TimeStatisticsDTO">
        SELECT
        <if test="param.timeType == 'year'">
            SUBSTR(i.ds_certificate_date, 1, 4) as timePoint
        </if>
        <if test="param.timeType == 'quarter'">
            CONCAT(SUBSTR(i.ds_certificate_date, 1, 4),
            CASE
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '10' AND '12' THEN 'Q4'
            END) as timePoint
        </if>
        <if test="param.timeType == 'month'">
            CONCAT(SUBSTR(i.ds_certificate_date, 1, 4), '-', SUBSTR(i.ds_certificate_date, 5, 2)) as timePoint
        </if>
        <if test="param.timeType == 'day'">
            TO_CHAR(TO_DATE(i.ds_certificate_date, 'YYYYMMDD'), 'YYYY-MM-DD') as timePoint
        </if>,
        SUM(i.issue_count) as issueCount
        FROM
        dws_certificate_issue i
        WHERE
        i.issuer_code LIKE CONCAT(#{param.loginUserOrgCode}, '%')
        <if test="param.issuerCodes != null and param.issuerCodes.size() > 0">
            AND (
            <foreach collection="param.issuerCodes" item="issuerCode" separator=" OR ">
                i.issuer_code LIKE CONCAT(#{issuerCode}, '%')
            </foreach>
            )
        </if>
        <if test="param.certificateTypeCodes != null and param.certificateTypeCodes.size() > 0">
            AND EXISTS (
            SELECT 1 FROM ert_type_directory etd_filter
            WHERE etd_filter.CERTIFICATE_TYPE_CODE = i.certificate_type_code
            AND (
            <foreach collection="param.certificateTypeCodes" item="typeCode" separator=" OR ">
                etd_filter.CERTIFICATE_TYPE_CODE = #{typeCode}
                OR EXISTS (
                SELECT 1 FROM ert_type_directory etd_child
                WHERE etd_child.CERTIFICATE_TYPE_CODE = etd_filter.CERTIFICATE_TYPE_CODE
                START WITH etd_child.PARENT_ID = (
                SELECT etd_parent.CERT_TYPE_DIR_ID
                FROM ert_type_directory etd_parent
                WHERE etd_parent.CERTIFICATE_TYPE_CODE = #{typeCode}
                )
                CONNECT BY PRIOR etd_child.CERT_TYPE_DIR_ID = etd_child.PARENT_ID
                )
            </foreach>
            )
            )
        </if>
        <if test="param.holderCategoryCodes != null and param.holderCategoryCodes.size() > 0">
            AND i.holder_category_code IN
            <foreach collection="param.holderCategoryCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.matterNatureCodes != null and param.matterNatureCodes.size() > 0">
            AND i.matter_nature_code IN
            <foreach collection="param.matterNatureCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.applicationSourceCode != null and param.applicationSourceCode != ''">
            AND i.application_source_code = #{param.applicationSourceCode}
        </if>
        <if test="param.statusCode != null and param.statusCode != ''">
            AND i.status_code = #{param.statusCode}
        </if>
        <if test="param.timeType == 'year' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND SUBSTR(i.ds_certificate_date, 1, 4) BETWEEN #{param.startTime} AND #{param.endTime}
        </if>
        <if test="param.timeType == 'quarter' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND CONCAT(SUBSTR(i.ds_certificate_date, 1, 4),
            CASE
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
            BETWEEN
            CONCAT(SUBSTR(#{param.startTime}, 1, 4),
            CASE
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
            AND
            CONCAT(SUBSTR(#{param.endTime}, 1, 4),
            CASE
            WHEN SUBSTR(#{param.endTime}, 5, 2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(#{param.endTime}, 5, 2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(#{param.endTime}, 5, 2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(#{param.endTime}, 5, 2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
        </if>
        <if test="param.timeType == 'month' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND SUBSTR(i.ds_certificate_date, 1, 6) BETWEEN REPLACE(#{param.startTime}, '-', '') AND REPLACE(#{param.endTime}, '-', '')
        </if>
        <if test="param.timeType == 'day' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND i.ds_certificate_date BETWEEN REPLACE(#{param.startTime}, '-', '') AND REPLACE(#{param.endTime}, '-', '')
        </if>
        <if test="param.relatedMatterName != null and param.relatedMatterName != ''">
            <if test="param.relatedMatterName.startsWith('#') and param.relatedMatterName.endsWith('%')">
                AND EXISTS (
                SELECT 1 FROM ctf_certificate_type cct
                WHERE cct.CERTIFICATE_TYPE_ID = i.certificate_id
                AND cct.DESCRIPTION LIKE #{param.relatedMatterName}
                )
            </if>
            <if test="!param.relatedMatterName.startsWith('#') or !param.relatedMatterName.endsWith('%')">
                AND EXISTS (
                SELECT 1 FROM ctf_certificate_type cct
                WHERE cct.CERTIFICATE_TYPE_ID = i.certificate_id
                AND cct.DESCRIPTION IN
                <foreach collection="param.relatedMatterName.split(',')" item="name" open="(" separator="," close=")">
                    #{name}
                </foreach>
                )
            </if>
        </if>
        <if test="param.certificateIds != null and param.certificateIds.size() > 0">
            AND i.certificate_id IN
            <foreach collection="param.certificateIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        GROUP BY
        <if test="param.timeType == 'year'">
            SUBSTR(i.ds_certificate_date, 1, 4)
        </if>
        <if test="param.timeType == 'quarter'">
            CONCAT(SUBSTR(i.ds_certificate_date, 1, 4),
            CASE
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
        </if>
        <if test="param.timeType == 'month'">
            CONCAT(SUBSTR(i.ds_certificate_date, 1, 4), '-', SUBSTR(i.ds_certificate_date, 5, 2))
        </if>
        <if test="param.timeType == 'day'">
            TO_CHAR(TO_DATE(i.ds_certificate_date, 'YYYYMMDD'), 'YYYY-MM-DD')
        </if>
        ORDER BY
        timePoint
    </select>
</mapper> 