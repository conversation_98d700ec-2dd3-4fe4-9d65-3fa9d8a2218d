package com.js.hszpt.mapper.crew;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.js.hszpt.entity.CrewCertificateData;
import com.js.hszpt.dto.CrewCertificateQueryDto;
import com.js.hszpt.entity.VerificationDisplayConfig;import com.js.hszpt.vo.CrewCertificateQueryVo;
import com.js.hszpt.dto.IntranetCrewQueryDto;
import com.js.hszpt.vo.CertificateIntranetCrewQueryVo;
import com.js.hszpt.vo.TrainingProjectVo;import org.apache.ibatis.annotations.Param;import java.util.List;

@DS("dzzzdwdz")
public interface CrewCertificateMapper extends BaseMapper<CrewCertificateData> {

//    /**
//     * 外网-海事一网通办-船员证书查询
//     * @param page
//     * @param queryDto
//     * @return
//     */
//    IPage<CrewCertificateQueryVo> selectCrewCertificates(Page page, @Param("query") CrewCertificateQueryDto queryDto);
//
//    /**
//     * 内网-智慧海事-船员证书查询
//     */
//    IPage<CertificateIntranetCrewQueryVo> certificateIntranetCrewQueryAuth(Page page, @Param("dto") IntranetCrewQueryDto dto);


       /**
           * 外网-海事一网通办-船员证书查询
           * @param page
           * @param queryDto
           * @return
           */
          IPage<CrewCertificateQueryVo> selectCrewCertificates(Page page, @Param("query") CrewCertificateQueryDto queryDto);

          /**
           * 内网-智慧海事-船员证书查询
           */
          IPage<CertificateIntranetCrewQueryVo> certificateIntranetCrewQueryAuth(Page page, @Param("dto") IntranetCrewQueryDto dto);

          /**
           * 查询内河船舶船员培训合格证的培训项目
           * @param dataId 证书数据ID
           * @return 培训项目名称，以##分隔
           */
          TrainingProjectVo queryTrainingProjects(String dataId);

          /**
           * 查询证书展示配置
           *
           * @param certificateTypeName 证书类型名称
           * @param displaySource 展示来源
           * @return 展示配置列表
           */
          List<VerificationDisplayConfig> queryVerificationDisplayConfigs(@Param("certificateTypeName") String certificateTypeName,
                                                                         @Param("displaySource") String displaySource);


}
