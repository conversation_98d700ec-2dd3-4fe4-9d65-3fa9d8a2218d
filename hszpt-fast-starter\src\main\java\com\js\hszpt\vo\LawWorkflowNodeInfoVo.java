package com.js.hszpt.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 工作流节点信息(LawWorkflowNodeInfo)实体类
 *
 * <AUTHOR>
 * @since 2020-09-28 19:04:19
 */
@Data
@Entity
@Table(name = "LAW_WORKFLOW_NODE_INFO")
public class LawWorkflowNodeInfoVo {
    private static final long serialVersionUID = 421160175721382568L;
    /**
     * 关联ID
     */
    @Id
    @Column(name = "LAW_WORKFLOW_NODE_INFO_ID")
//    @Generator("snowFlakeGenerator")
    private String lawWorkflowNodeInfoId;

    @Column(name = "LAW_WORKFLOW_NODE_SET_ID")
    private String lawWorkflowNodeSetId;
    /**
     * 节点代码
     */
    @Column(name = "NODE_CODE")
    private String nodeCode;
    /**
     * 节点名称
     */
    @Column(name = "NODE_NAME")
    private String nodeName;
    /**
     * 创建人
     */
    @Column(name = "CREATE_OPER_ID")
    private String createOperId;
    /**
     * 创建部门
     */
    @Column(name = "CREATE_OPER_DEPT")
    private String createOperDept;
    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 修改人
     */
    @Column(name = "MODIFY_OPER_ID")
    private String modifyOperId;
    /**
     * 修改部门
     */
    @Column(name = "MODIFY_OPER_DEPT")
    private String modifyOperDept;
    /**
     * 修改时间
     */
    @Column(name = "MODIFY_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifyTime;
    /**
     * 版本号
     */
    @Column(name = "RECORD_VERSION")
    private String recordVersion;

    /**
     * 是否必填节点
     */
    @Column(name = "NODE_IS_SELECT")
    private String nodeIsSelect;

    /**
     * 节点顺序
     */
    @Column(name = "NODE_SORT")
    private Integer nodeSort;

    /**
     * 节点机构层级 1-部局 2-直属局 3-分支局 4-海事处
     */
    @Column(name = "NODE_ORG_LEVEL")
    private Integer nodeOrgLevel;

    /**
     * 节点是否开启  0-不启用  1-启用
     */
    @Column(name = "NODE_IS_OPEN")
    private String nodeIsOpen;

    /**
     * 节点类型 1-审核节点 2-审批节点
     */
    @Column(name = "NODE_TYPE")
    private String nodeType;

    /**
     * 节点机构关联ID
     */
    private String lawWorkflowNodeOrgId;

    /**
     * 记录唯一标识
     */
    private String mattersDealWithId;

    /**
     * 申请类型 1-执法资格申请 2-初次申请 3-换证 4-遗失补办 5-年审  6-执法工作考核 7-责任追究处分信息 8-注销
     */
    private String applyType;
    /**
     * 办理事项ID
     */
    private String handlingMattersId;
    /**
     * 当前事项状态
     */
    private String currentStatus;
    /**
     * 审核人ID
     */
    private String idNumber;
    /**
     * 审核人真实姓名
     */
    private String userName;
    /**
     * 审核人组织机构ID
     */
    private String orgId;
    /**
     * 审核人组织机构编码
     */
    private String orgCode;
    /**
     * 审核人组织机构名称
     */
    private String orgName;
    /**
     * 审核人部门ID
     */
    private String deptId;
    /**
     * 审核人部门名称
     */
    private String deptName;
    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date checkTime;
    /**
     * 审核结果 0:未启动 1：待审核 2：拒绝 3：通过
     */
    private String checkResult;
    /**
     * 审核结果 0:未启动 1：待审核 2：拒绝 3：通过
     */
    private String checkResultId;
    /**
     * 意见
     */
    private String checkContent;
    /**
     * 是否最后一步
     */
    private String lastStep;

    /**
     * 流程节点代码
     */
    private String dealNodeCode;

    @Transient
    private String dealNodeName;
    /**
     * 节点类型 1-审核节点 2-审批节点
     */
    private Integer dealNodeType;

    /**
     * 操作人岗位编码
     */
    private String dutyCode;

    /**
     * 流程步骤
     */
//    private LawMattersDealWith lawMattersDealWith;
//
//
//    List<LawWorkflowNodeUserVo> lawWorkflowNodeUserList;
//
//    List<LawWorkflowNodeOrgVo> lawWorkflowNodeOrgList;
//
//    public LawMattersDealWith getLawMattersDealWith() {
//        return lawMattersDealWith;
//    }
//
//    public void setLawMattersDealWith(LawMattersDealWith lawMattersDealWith) {
//        this.lawMattersDealWith = lawMattersDealWith;
//    }

    public String getLawWorkflowNodeOrgId() {
        return lawWorkflowNodeOrgId;
    }

    public void setLawWorkflowNodeOrgId(String lawWorkflowNodeOrgId) {
        this.lawWorkflowNodeOrgId = lawWorkflowNodeOrgId;
    }

    public String getNodeIsOpen() {
        return nodeIsOpen;
    }

    public void setNodeIsOpen(String nodeIsOpen) {
        this.nodeIsOpen = nodeIsOpen;
    }

//    public List<LawWorkflowNodeUserVo> getLawWorkflowNodeUserList() {
//        return lawWorkflowNodeUserList;
//    }
//
//    public void setLawWorkflowNodeUserList(List<LawWorkflowNodeUserVo> lawWorkflowNodeUserList) {
//        this.lawWorkflowNodeUserList = lawWorkflowNodeUserList;
//    }
//
//    public List<LawWorkflowNodeOrgVo> getLawWorkflowNodeOrgList() {
//        return lawWorkflowNodeOrgList;
//    }
//
//    public void setLawWorkflowOrgList(List<LawWorkflowNodeOrgVo> lawWorkflowNodeOrgList) {
//        this.lawWorkflowNodeOrgList = lawWorkflowNodeOrgList;
//    }

    public String getLawWorkflowNodeInfoId() {
        return lawWorkflowNodeInfoId;
    }

    public void setLawWorkflowNodeInfoId(String lawWorkflowNodeInfoId) {
        this.lawWorkflowNodeInfoId = lawWorkflowNodeInfoId;
    }

    public String getLawWorkflowNodeSetId() {
        return lawWorkflowNodeSetId;
    }

    public void setLawWorkflowNodeSetId(String lawWorkflowNodeSetId) {
        this.lawWorkflowNodeSetId = lawWorkflowNodeSetId;
    }

    public String getNodeCode() {
        return nodeCode;
    }

    public void setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
    }

    public String getNodeName() {
        return nodeName;
    }

    public void setNodeName(String nodeName) {
        this.nodeName = nodeName;
    }

    public String getCreateOperId() {
        return createOperId;
    }

    public void setCreateOperId(String createOperId) {
        this.createOperId = createOperId;
    }

    public String getCreateOperDept() {
        return createOperDept;
    }

    public void setCreateOperDept(String createOperDept) {
        this.createOperDept = createOperDept;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getModifyOperId() {
        return modifyOperId;
    }

    public void setModifyOperId(String modifyOperId) {
        this.modifyOperId = modifyOperId;
    }

    public String getModifyOperDept() {
        return modifyOperDept;
    }

    public void setModifyOperDept(String modifyOperDept) {
        this.modifyOperDept = modifyOperDept;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getRecordVersion() {
        return recordVersion;
    }

    public void setRecordVersion(String recordVersion) {
        this.recordVersion = recordVersion;
    }

    public String getNodeIsSelect() {
        return nodeIsSelect;
    }

    public void setNodeIsSelect(String nodeIsSelect) {
        this.nodeIsSelect = nodeIsSelect;
    }

    public Integer getNodeSort() {
        return nodeSort;
    }

    public void setNodeSort(Integer nodeSort) {
        this.nodeSort = nodeSort;
    }

    public Integer getNodeOrgLevel() {
        return nodeOrgLevel;
    }

    public void setNodeOrgLevel(Integer nodeOrgLevel) {
        this.nodeOrgLevel = nodeOrgLevel;
    }

    public String getNodeType() {
        return nodeType;
    }

    public void setNodeType(String nodeType) {
        this.nodeType = nodeType;
    }

}