package com.js.hszpt.api;


import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.js.annotation.SystemLog;
import com.js.core.api.BaseApiPlus;
import com.js.core.common.utils.ResultUtil;
import com.js.core.common.vo.PageVo;
import com.js.core.common.vo.Result;
import com.js.core.common.vo.SearchVo;
import com.js.enums.LogType;
import com.js.hszpt.dto.SysUserDto;
import com.js.hszpt.entity.SysUser;
import com.js.hszpt.service.SysDeptIambService;
import com.js.hszpt.service.SysUserService;
import com.js.hszpt.vo.SysUserVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 *
 * @ClassName: SysUserApi
 * @Description:TODO(用户表接口)
 * @author:  System Generation
 *
 */
@Slf4j
@RestController
@Api(description = "用户表接口")
@RequestMapping("/sysUser")
public class SysUserApi extends BaseApiPlus<SysUserService,SysUser,String>{
	@Autowired
	private SysDeptIambService sysDeptIambService;
	@Autowired
	private SysUserService sysUserService;
	@SystemLog(description = "用户表-分页查询", type = LogType.OPERATION)
	@RequestMapping(value = "/getPage", method = RequestMethod.GET)
	@ApiOperation(value = "分页查询")
	public Result<Page<SysUser>> getPage(@ModelAttribute SysUser param, @ModelAttribute SearchVo searchVo, @ModelAttribute PageVo pageVo) {
		Page<SysUser> page = this.baseService.findByCondition(param, searchVo, pageVo);
		return ResultUtil.data(page);
	}

	@SystemLog(description = "用户表-列表查询", type = LogType.OPERATION)
	@RequestMapping(value = "/getList", method = RequestMethod.GET)
	@ApiOperation(value = "列表查询")
	public Result<List<SysUser>> getList(@ModelAttribute SysUser param, @ModelAttribute SearchVo searchVo) {
		List<SysUser> list = this.baseService.findByCondition(param, searchVo);
		return ResultUtil.data(list);
	}

	@PostMapping("/selectUserListForWorkflow")
	@ApiOperation("工作流添加选择用户列表")
	public  Result<IPage<SysUserVo>> selectUserListForWorkflow(@RequestBody SysUserDto sysUserDto) {
		// 参数校验
		if (sysUserDto == null) {
			return Result.failed("参数不能为空");
		}
		if (sysUserDto.getNodeInfoId() != null) {
			// 获取机构代码
			String code = StrUtil.toString(sysUserDto.getDeptId());
		} else {
			String code = StrUtil.toString(sysUserDto.getDeptId());
		}

//		IPage<SysUserVo> sysUserVoPage = sysUserService.selectUserListForWorkflow(sysUserDto);
		// 调用服务
		IPage<SysUserVo> result = sysUserService.selectUserListForWorkflow(sysUserDto);
		return Result.success(result);
	}



}

