package com.js.hszpt.entity;

import com.js.core.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.Builder;
import java.util.Date;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;


/**
 * 
 * @ClassName:  CertShipOwnership   
 * @Description:TODO(船舶证书-所有权登记证书)   
 * @author:   System Generation 
 */
@Data

@TableName("cert_ship_ownership")
@ApiModel(value = "船舶证书-所有权登记证书")
public class CertShipOwnership {

    private static final long serialVersionUID = 1L;


    /**
    * 船舶登记号 船舶管理部门在办理船舶所有权登记时授予的所有权登记号码。由P(表示临时证书号，可选)+4位发证机构编码＋2位年＋6位顺序号构成。
    */
    @ApiModelProperty(value = "船舶登记号 船舶管理部门在办理船舶所有权登记时授予的所有权登记号码。由P(表示临时证书号，可选)+4位发证机构编码＋2位年＋6位顺序号构成。")
    private String shipRegNo;


    /**
    * 船舶识别号 永久识别中国籍船舶的唯一编码。由英文字母CN和11位阿拉伯数字构成。CN代表中国，11位阿拉伯数字的前4位表示船舶安放龙骨的年份，第5至10位是随机编号，第11位是校验码。
    */
    @ApiModelProperty(value = "船舶识别号 永久识别中国籍船舶的唯一编码。由英文字母CN和11位阿拉伯数字构成。CN代表中国，11位阿拉伯数字的前4位表示船舶安放龙骨的年份，第5至10位是随机编号，第11位是校验码。")
    private String shipId;


    /**
    * 初次登记号 船舶初次办理船舶所有权登记时，由船舶管理部门授予的所有权登记号码。由P(表示临时证书号，可选)+4位发证机构编码＋2位年＋6位流水号构成。
    */
    @ApiModelProperty(value = "初次登记号 船舶初次办理船舶所有权登记时，由船舶管理部门授予的所有权登记号码。由P(表示临时证书号，可选)+4位发证机构编码＋2位年＋6位流水号构成。")
    private String shipFirstregNo;


    /**
    * IMO编号 按照国际海事组织A.600决议授予100总吨及以上自航海船的编号。
    */
    @ApiModelProperty(value = "IMO编号 按照国际海事组织A.600决议授予100总吨及以上自航海船的编号。")
    private String shipImo;


    /**
    * MMSI编号 海上移动通信业务标识，船舶无线电通信系统在其无线电信道上发送的，能独特识别各类台站和成组呼叫台站的一列9位数字码。
    */
    @ApiModelProperty(value = "MMSI编号 海上移动通信业务标识，船舶无线电通信系统在其无线电信道上发送的，能独特识别各类台站和成组呼叫台站的一列9位数字码。")
    private String shipMmsi;


    /**
    * 中文船名
    */
    @ApiModelProperty(value = "中文船名")
    private String shipName;


    /**
    * 英文船名
    */
    @ApiModelProperty(value = "英文船名")
    private String shipNameEn;


    /**
    * 原中文船名
    */
    @ApiModelProperty(value = "原中文船名")
    private String origShipName;


    /**
    * 船籍港代码 DICT_DJ_REG_PORT
    */
    @ApiModelProperty(value = "船籍港代码 DICT_DJ_REG_PORT")
    private String regportCode;


    /**
    * 船籍港名称
    */
    @ApiModelProperty(value = "船籍港名称")
    private String regportName;


    /**
    * 原船籍港名称
    */
    @ApiModelProperty(value = "原船籍港名称")
    private String origRegportName;


    /**
    * 船舶种类代码 DICT_DJ_SHIP_TYPE
    */
    @ApiModelProperty(value = "船舶种类代码 DICT_DJ_SHIP_TYPE")
    private String shipTypeCode;


    /**
    * 船舶种类
    */
    @ApiModelProperty(value = "船舶种类")
    private String shipTypeName;


    /**
    * 船体材料代码 DICT_DJ_HULL_MATERIAL
    */
    @ApiModelProperty(value = "船体材料代码 DICT_DJ_HULL_MATERIAL")
    private String shipHullMaterialCode;


    /**
    * 船体材料名称
    */
    @ApiModelProperty(value = "船体材料名称")
    private String shipHullMaterialName;


    /**
    * 船舶总长
    */
    @ApiModelProperty(value = "船舶总长")
    private BigDecimal shipLength;


    /**
    * 船舶型宽
    */
    @ApiModelProperty(value = "船舶型宽")
    private BigDecimal shipBreadth;


    /**
    * 船舶型深
    */
    @ApiModelProperty(value = "船舶型深")
    private BigDecimal shipDepth;


    /**
    * 船舶总吨
    */
    @ApiModelProperty(value = "船舶总吨")
    private BigDecimal shipGrosston;


    /**
    * 船舶净吨
    */
    @ApiModelProperty(value = "船舶净吨")
    private BigDecimal shipNetton;


    /**
    * 参考载重吨
    */
    @ApiModelProperty(value = "参考载重吨")
    private String shipDwt;


    /**
    * 主机种类代码 DICT_DJ_ENGINE_TYPE
    */
    @ApiModelProperty(value = "主机种类代码 DICT_DJ_ENGINE_TYPE")
    private String shipEngineTypeCode;


    /**
    * 主机数量（个）
    */
    @ApiModelProperty(value = "主机数量（个）")
    private BigDecimal shipEngineNum;


    /**
    * 主机功率
    */
    @ApiModelProperty(value = "主机功率")
    private BigDecimal shipEnginePower;


    /**
    * 推进器种类代码 DICT_PROPLLER_TYPE
    */
    @ApiModelProperty(value = "推进器种类代码 DICT_PROPLLER_TYPE")
    private String shipPropellerTypeCode;


    /**
    * 推进器数量（个）
    */
    @ApiModelProperty(value = "推进器数量（个）")
    private BigDecimal shipPropellerNum;


    /**
    * 造船厂名称
    */
    @ApiModelProperty(value = "造船厂名称")
    private String shipyard;


    /**
    * 造船地点
    */
    @ApiModelProperty(value = "造船地点")
    private String shipBuiltAddr;


    /**
    * 船舶建成日期
    */
    @ApiModelProperty(value = "船舶建成日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date shipBuiltDate;


    /**
    * 船舶所有人ID（统一社会引用代码身份证号）
    */
    @ApiModelProperty(value = "船舶所有人ID（统一社会引用代码身份证号）")
    private String shipOwnerIdcardNo;


    /**
    * 船舶所有人
    */
    @ApiModelProperty(value = "船舶所有人")
    private String shipOwner;


    /**
    * 船舶所有人英文
    */
    @ApiModelProperty(value = "船舶所有人英文")
    private String shipOwnerEn;


    /**
    * 船舶所有人地址
    */
    @ApiModelProperty(value = "船舶所有人地址")
    private String shipOwnerAddr;


    /**
    * 船舶所有人英文地址
    */
    @ApiModelProperty(value = "船舶所有人英文地址")
    private String shipOwnerAddrEn;


    /**
    * 所有人法人代表
    */
    @ApiModelProperty(value = "所有人法人代表")
    private String ownerLegalPerson;


    /**
    * 船舶所有人联系电话
    */
    @ApiModelProperty(value = "船舶所有人联系电话")
    private String ownerContactPhone;


    /**
    * 船舶所有人（法人）应急联系电话
    */
    @ApiModelProperty(value = "船舶所有人（法人）应急联系电话")
    private String ownerEmergencyContact;


    /**
    * 所有人邮政编码
    */
    @ApiModelProperty(value = "所有人邮政编码")
    private String ownerPostalCode;


    /**
    * 取得日期
    */
    @ApiModelProperty(value = "取得日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date certObtainDate;


    /**
    * 所有权取得方式代码 DICT_DJ_OBTAIN_WAY
    */
    @ApiModelProperty(value = "所有权取得方式代码 DICT_DJ_OBTAIN_WAY")
    private String shipObtainWayCode;


    /**
    * 所有权取得方式中文名称
    */
    @ApiModelProperty(value = "所有权取得方式中文名称")
    private String shipObtainWayName;


    /**
    * 船舶共有情况
    */
    @ApiModelProperty(value = "船舶共有情况")
    private String shipShareSituation;


    /**
    * 状态标志代码 T：转移；1：登记；3：注销
    */
    @ApiModelProperty(value = "状态标志代码 T：转移；1：登记；3：注销")
    private String statusFlagCode;


    /**
    * 打印标志代码 0：未打印；1：已打印
    */
    @ApiModelProperty(value = "打印标志代码 0：未打印；1：已打印")
    private String printFlagCode;


    /**
    * 印章标志代码 0：未使用；1：已使用
    */
    @ApiModelProperty(value = "印章标志代码 0：未使用；1：已使用")
    private String sealFlagCode;


    /**
    * 校核标志代码 0：未校核；1：校核通过；3：校核未通过
    */
    @ApiModelProperty(value = "校核标志代码 0：未校核；1：校核通过；3：校核未通过")
    private String checkFlagCode;


    /**
    * 发放标志代码 0：未发放；1：已发放
    */
    @ApiModelProperty(value = "发放标志代码 0：未发放；1：已发放")
    private String issueFlagCode;


    /**
    * 证书印刷号 船舶相关证书上的印刷号。10位编码，由4位固定开头+6位序列号构成。
    */
    @ApiModelProperty(value = "证书印刷号 船舶相关证书上的印刷号。10位编码，由4位固定开头+6位序列号构成。")
    private String certPrintNo;


    /**
    * 注销登记号码 船舶注销登记证书的唯一编号。由字母ZX+4位发证机构编码＋2位年＋4位流水号构成。
    */
    @ApiModelProperty(value = "注销登记号码 船舶注销登记证书的唯一编号。由字母ZX+4位发证机构编码＋2位年＋4位流水号构成。")
    private String cancelRegNo;


    /**
    * 海事机构代码 DICT_DJ_ORG
    */
    @ApiModelProperty(value = "海事机构代码 DICT_DJ_ORG")
    private String orgCode;


    /**
    * 登记机构名称
    */
    @ApiModelProperty(value = "登记机构名称")
    private String orgName;


    /**
    * 备注
    */
    @ApiModelProperty(value = "备注")
    private String remark;


    /**
    * 创建人标识
    */
    @ApiModelProperty(value = "创建人标识")
    private String creatorId;


    /**
    * 创建人所属机构代码 DICT_DJ_ORG
    */
    @ApiModelProperty(value = "创建人所属机构代码 DICT_DJ_ORG")
    private String creatorOrgCode;


    /**
    * 创建日期
    */
    @ApiModelProperty(value = "创建日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;


    /**
    * 操作人标识
    */
    @ApiModelProperty(value = "操作人标识")
    private String operatorId;


    /**
    * 操作日期
    */
    @ApiModelProperty(value = "操作日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operateDate;


    /**
    * 操作标志代码 I：新增；U：更新
    */
    @ApiModelProperty(value = "操作标志代码 I：新增；U：更新")
    private String operateFlagCode;


    /**
    * 源系统代码
    */
    @ApiModelProperty(value = "源系统代码")
    private String sourceCode;


    /**
    * 记录创建日期
    */
    @ApiModelProperty(value = "记录创建日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date recCreateDate;


    /**
    * 记录修改日期
    */
    @ApiModelProperty(value = "记录修改日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date recModifyDate;


    /**
    * 数据归属机构代码 DICT_DJ_ORG
    */
    @ApiModelProperty(value = "数据归属机构代码 DICT_DJ_ORG")
    private String msaOrgCode;


}