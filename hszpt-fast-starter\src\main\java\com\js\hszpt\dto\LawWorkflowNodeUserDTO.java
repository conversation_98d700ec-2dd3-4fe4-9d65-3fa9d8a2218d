package com.js.hszpt.dto;

import com.js.hszpt.entity.LawWorkflowNodeUser;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * 工作流用户信息(LawWorkflowNodeUser)实体类
 *
 * <AUTHOR>
 * @since 2020-09-28 19:06:05
 */
public class LawWorkflowNodeUserDTO {
    /**
     * ID
     */
    private String lawWorkflowNodeUserId;

    /**
     * 关联ID
     */
    private String lawWorkflowNodeSetId;
    /**
     * ID
     */
    private String lawWorkflowNodeInfoId;
    /**
     * 用户编码
     */
    private String userCode;
    /**
     * 用户名称
     */
    private String userName;

    public String getCertNo() {
        return certNo;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    private String certNo;
    /**
     * 创建人
     */
    @Column(name = "CREATE_OPER_ID")
    private String createOperId;
    /**
     * 创建部门
     */
    private String createOperDept;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 修改人
     */
    private String modifyOperId;
    /**
     * 修改部门
     */
    private String modifyOperDept;

    /**
     * 工作流模板节点机构自定义配置表ID
     */
    private String lawWorkflowNodeOrgId;

    /**
     * 用户身份证号码
     */
    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifyTime;
    /**
     * 版本号
     */
    private String recordVersion;

    private String orgCode;

    @Override
    public int hashCode() {
        return Objects.hash(lawWorkflowNodeUserId, lawWorkflowNodeSetId, lawWorkflowNodeInfoId, userCode, certNo,userName, createOperId, createOperDept, createTime, modifyOperId, modifyOperDept, lawWorkflowNodeOrgId, modifyTime, recordVersion, orgCode);
    }

    @Override
    public boolean equals(Object o) {
        if(o==null){
            return false;
        }
        if(o.getClass()!=LawWorkflowNodeUser.class){
            return false;
        }
        LawWorkflowNodeUser u=(LawWorkflowNodeUser)o;
        return StringUtils.equals(this.userCode,u.getUserCode());

    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getLawWorkflowNodeUserId() {
        return lawWorkflowNodeUserId;
    }

    public void setLawWorkflowNodeUserId(String lawWorkflowNodeUserId) {
        this.lawWorkflowNodeUserId = lawWorkflowNodeUserId;
    }

    public String getLawWorkflowNodeSetId() {
        return lawWorkflowNodeSetId;
    }

    public void setLawWorkflowNodeSetId(String lawWorkflowNodeSetId) {
        this.lawWorkflowNodeSetId = lawWorkflowNodeSetId;
    }

    public String getLawWorkflowNodeInfoId() {
        return lawWorkflowNodeInfoId;
    }

    public void setLawWorkflowNodeInfoId(String lawWorkflowNodeInfoId) {
        this.lawWorkflowNodeInfoId = lawWorkflowNodeInfoId;
    }

    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getCreateOperId() {
        return createOperId;
    }

    public void setCreateOperId(String createOperId) {
        this.createOperId = createOperId;
    }

    public String getCreateOperDept() {
        return createOperDept;
    }

    public void setCreateOperDept(String createOperDept) {
        this.createOperDept = createOperDept;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getModifyOperId() {
        return modifyOperId;
    }

    public void setModifyOperId(String modifyOperId) {
        this.modifyOperId = modifyOperId;
    }

    public String getModifyOperDept() {
        return modifyOperDept;
    }

    public void setModifyOperDept(String modifyOperDept) {
        this.modifyOperDept = modifyOperDept;
    }

    public String getLawWorkflowNodeOrgId() {
        return lawWorkflowNodeOrgId;
    }

    public void setLawWorkflowNodeOrgId(String lawWorkflowNodeOrgId) {
        this.lawWorkflowNodeOrgId = lawWorkflowNodeOrgId;
    }


    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getRecordVersion() {
        return recordVersion;
    }

    public void setRecordVersion(String recordVersion) {
        this.recordVersion = recordVersion;
    }
}