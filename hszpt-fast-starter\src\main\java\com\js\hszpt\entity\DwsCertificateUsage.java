package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 电子证照使用情况统计实体类
 */
@Data
@TableName("dws_certificate_usage")
public class DwsCertificateUsage implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一标识
     */
    @TableId
    private String usageId;

    /**
     * 业务日期，格式yyyymmdd
     */
    private String dsCertificateDate;

    /**
     * 签发机构编码（关联签发机构表维度表）
     */
    private String issuerCode;

    /**
     * 证照类型代码（关联证照类型目录维度表）
     */
    private String certificateTypeCode;

    /**
     * 持证主体类别代码（字典表）
     */
    private String holderCategoryCode;

    /**
     * 事项性质代码（字典表）
     */
    private String matterNatureCode;

    /**
     * 申请来源（字典表）
     */
    private String applicationSourceCode;

    /**
     * 证照状态（字典表）
     */
    private String statusCode;

    /**
     * 使用类型（1-下载 2-核验）
     */
    private String usageType;

    /**
     * 当日使用数量
     */
    private Long usageCount;

    /**
     * 记录创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    /**
     * 记录创建日期
     */
    private Date recCreateDate;

    /**
     * 记录修改日期
     */
    private Date recModifyDate;

    /**
     * 签发机构名称（非数据库字段）
     */
    @TableField(exist = false)
    private String issuerName;

    /**
     * 证照类型名称（非数据库字段）
     */
    @TableField(exist = false)
    private String certificateTypeName;

    /**
     * 持证主体类别名称（非数据库字段）
     */
    @TableField(exist = false)
    private String holderCategoryName;

    /**
     * 事项性质名称（非数据库字段）
     */
    @TableField(exist = false)
    private String matterNatureName;

    /**
     * 申请来源名称（非数据库字段）
     */
    @TableField(exist = false)
    private String applicationSourceName;

    /**
     * 证照状态名称（非数据库字段）
     */
    @TableField(exist = false)
    private String statusName;
} 