package com.js.hszpt.config;

import com.js.core.conf.restTemplate.WxMappingJackson2HttpMessageConverter;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.ssl.SSLContexts;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import java.util.concurrent.TimeUnit;

import javax.net.ssl.SSLContext;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;

@Configuration
public class ProxyRestTemplateConfig {

    // 连接池参数
    private static final int MAX_TOTAL_CONNECTIONS = 2000;       // 最大连接数
    private static final int DEFAULT_MAX_PER_ROUTE = 2000;        // 每个路由最大连接数
    private static final int CONNECT_TIMEOUT = 5000;            // 连接超时（毫秒）
    private static final int SOCKET_TIMEOUT = 60000;            // 读取超时（毫秒）
    private static final int CONNECTION_REQUEST_TIMEOUT = 5000;  // 连接请求超时（毫秒）
    private static final int VALIDATE_AFTER_INACTIVITY = 30000; // 连接空闲30秒后验证
    private static final int IDLE_CONNECTION_TIMEOUT = 60;      // 空闲连接超时（秒）


    @Bean
    public PoolingHttpClientConnectionManager poolingConnectionManager() {
        PoolingHttpClientConnectionManager manager = new PoolingHttpClientConnectionManager();
        manager.setMaxTotal(MAX_TOTAL_CONNECTIONS);
        manager.setDefaultMaxPerRoute(DEFAULT_MAX_PER_ROUTE);

        // 🔑 关键修复：添加连接回收配置
        manager.setValidateAfterInactivity(VALIDATE_AFTER_INACTIVITY); // 30秒后验证连接

        // 启动后台线程清理过期和空闲连接
        manager.closeExpiredConnections();
        manager.closeIdleConnections(IDLE_CONNECTION_TIMEOUT, TimeUnit.SECONDS);

        return manager;
    }


    @Bean
    public SSLConnectionSocketFactory sslConnectionSocketFactory()
            throws KeyStoreException, NoSuchAlgorithmException, KeyManagementException {
        TrustStrategy acceptingTrustStrategy = (X509Certificate[] chain, String authType) -> true;
        SSLContext sslContext = SSLContexts.custom()
                .loadTrustMaterial(null, acceptingTrustStrategy)
                .build();
        return new SSLConnectionSocketFactory(sslContext);
    }

    @Bean
    public RequestConfig requestConfig() {
        return RequestConfig.custom()
                .setConnectTimeout(CONNECT_TIMEOUT)
                .setSocketTimeout(SOCKET_TIMEOUT)
                .setConnectionRequestTimeout(CONNECTION_REQUEST_TIMEOUT) // 🔑 新增：连接请求超时
                .build();
    }

    @Bean
    public CloseableHttpClient httpClient(
            PoolingHttpClientConnectionManager poolingConnectionManager,
            SSLConnectionSocketFactory sslConnectionSocketFactory,
            RequestConfig requestConfig) {

        return HttpClientBuilder.create()
                .setConnectionManager(poolingConnectionManager)
                .setSSLSocketFactory(sslConnectionSocketFactory)
                .setDefaultRequestConfig(requestConfig)
                // 🔑 新增：禁用连接状态跟踪以提高性能
                .disableConnectionState()
                // 🔑 新增：禁用cookie管理以提高性能
                .disableCookieManagement()
                .build();
    }

    @Bean("proxyRestTemplate")
    public RestTemplate restTemplate(CloseableHttpClient httpClient) {
        HttpComponentsClientHttpRequestFactory factory =
                new HttpComponentsClientHttpRequestFactory(httpClient);
        RestTemplate restTemplate = new RestTemplate(factory);
        restTemplate.getMessageConverters().add(new WxMappingJackson2HttpMessageConverter());
        return restTemplate;
    }

    @Bean("defaultRestTemplate")
    public RestTemplate defaultRestTemplate(CloseableHttpClient httpClient) {
        // 🔑 关键修复：为defaultRestTemplate也配置连接池管理
        HttpComponentsClientHttpRequestFactory factory =
            new HttpComponentsClientHttpRequestFactory(httpClient);
        return new RestTemplate(factory);
    }
}