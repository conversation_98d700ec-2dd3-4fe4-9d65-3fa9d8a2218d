package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Id;
import java.util.Date;

@Data
@TableName("DATA_RECEPTION_TASKS")
@ApiModel(value = "数据接收任务表")
public class DataReceptionTask {

    // 任务名称（主键）
    @Id
    @TableId
    private String taskName;

    // API接口地址
    private String apiEndpoint;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss.SSS"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss.SSS"
    )
    // 上次完成接收任务的时间
    private Date lastCompletedTime;

}
