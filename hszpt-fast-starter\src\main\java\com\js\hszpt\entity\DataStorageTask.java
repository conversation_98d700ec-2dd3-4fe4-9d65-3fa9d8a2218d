package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Id;
import java.util.Date;

@Data
@TableName("DATA_STORAGE_TASKS")
@ApiModel(value = "数据存储任务表")
public class DataStorageTask {

    // 主键id
    @Id
    @TableId
    private String storageTasksId;

    // 任务名称
    private String taskName;

    // 数据存储的目标文件夹的路径
    private String targetFolderPath;

    // 已创建文件数量
    private String createFileCount;

    // 文件总数量
    private String totalFileCount;

    // 数据入库存储状态: 0:数据接收中 1-数据接收完成，等待入库 2:入库成功 3:入库失败
    private String storageStatus;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    // 存储的数据的开始时间
    private Date dataStartTime;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    // 存储的数据的结束时间
    private Date dataEndTime;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    // 创建时间
    private Date createDate;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    // 修改时间
    private Date modityDate;
}
