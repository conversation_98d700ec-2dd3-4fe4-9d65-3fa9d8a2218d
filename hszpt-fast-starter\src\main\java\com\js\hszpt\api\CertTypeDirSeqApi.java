package com.js.hszpt.api;


import cn.hutool.core.util.StrUtil;
import com.js.common.entity.CurrentUser;
import com.js.common.service.SecurityService;
import com.js.hszpt.entity.CertTypeDirSeq;
import com.js.hszpt.service.CertTypeDirSeqService;
 
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.js.core.api.BaseApiPlus;
import com.js.core.common.utils.ResultUtil;
import com.js.core.common.vo.PageVo;
import com.js.core.common.vo.Result;
import com.js.core.common.vo.SearchVo;
import com.js.annotation.SystemLog;
import com.js.enums.LogType;
import com.js.hszpt.service.SysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import com.js.hszpt.service.SysDeptIambService;


 /**
 * 
 * @ClassName: CertTypeDirSeqApi  
 * @Description:TODO(证照类型代码序号表接口)   
 * @author:  System Generation  
 *
 */
@Slf4j
@RestController
@Api(description = "证照类型代码序号表接口")
@RequestMapping("/certTypeDirSeq")
public class CertTypeDirSeqApi extends BaseApiPlus<CertTypeDirSeqService,CertTypeDirSeq,String>{

	@Autowired
	private SysUserService sysUserService;

	@Autowired
	private SysDeptIambService sysDeptIambService;

	@SystemLog(description = "证照类型代码序号表-分页查询", type = LogType.OPERATION)
	@RequestMapping(value = "/getPage", method = RequestMethod.GET)
	@ApiOperation(value = "分页查询")
	public Result<Page<CertTypeDirSeq>> getPage(@ModelAttribute CertTypeDirSeq param, @ModelAttribute SearchVo searchVo, @ModelAttribute PageVo pageVo) {
		Page<CertTypeDirSeq> page = this.baseService.findByCondition(param, searchVo, pageVo);
		return ResultUtil.data(page);
	}
	
	@SystemLog(description = "证照类型代码序号表-列表查询", type = LogType.OPERATION)
	@RequestMapping(value = "/getList", method = RequestMethod.GET)
	@ApiOperation(value = "列表查询")
	public Result<List<CertTypeDirSeq>> getList(@ModelAttribute CertTypeDirSeq param, @ModelAttribute SearchVo searchVo) {
		List<CertTypeDirSeq> list = this.baseService.findByCondition(param, searchVo);
		return ResultUtil.data(list);
	}
	
	@SystemLog(description = "初始化证照类型代码", type = LogType.OPERATION)
	@RequestMapping(value = "/initCertTypeCode", method = RequestMethod.GET)
	@ApiOperation(value = "初始化证照类型代码")
	public Result<String> initCertTypeCode() {

		// 获取用户机构编码，如果未找到则使用默认值"01"
		String orgCode = sysUserService.getCurrUserOrgCode();
		if (StrUtil.isBlank(orgCode)) {
			return ResultUtil.error("获取用户机构信息失败");
		}
		log.info("使用机构编码: {}", orgCode);

		// 查询统一社会信用代码
		String uscc = sysDeptIambService.getUsccByOrgCode(orgCode);
		log.info("获取到的统一社会信用代码: {}", uscc);

		// 查询序列值
		Integer seqValue = this.baseService.getSeqValueByOrgCode(orgCode);
		log.info("获取到的序列值: {}", seqValue);

		// 拼接结果
		String result = uscc + String.format("%03d", seqValue+1);
		log.info("生成的证照类型代码: {}", result);

		return ResultUtil.data(result);
	}
	
}
