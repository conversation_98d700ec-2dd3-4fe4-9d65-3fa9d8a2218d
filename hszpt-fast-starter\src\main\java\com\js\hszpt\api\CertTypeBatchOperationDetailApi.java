package com.js.hszpt.api;


import com.js.hszpt.entity.CertTypeBatchOperationDetail;
import com.js.hszpt.service.CertTypeBatchOperationDetailService;
 
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.js.core.api.BaseApiPlus;
import com.js.core.common.utils.ResultUtil;
import com.js.core.common.vo.PageVo;
import com.js.core.common.vo.Result;
import com.js.core.common.vo.SearchVo;
import com.js.annotation.SystemLog;
import com.js.enums.LogType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import java.util.List;


 /**
 * 
 * @ClassName: CertTypeBatchOperationDetailApi  
 * @Description:TODO(接口)   
 * @author:  System Generation  
 *
 */
@Slf4j
@RestController
@Api(description = "接口")
@RequestMapping("/certTypeBatchOperationDetail")
public class CertTypeBatchOperationDetailApi extends BaseApiPlus<CertTypeBatchOperationDetailService,CertTypeBatchOperationDetail,String>{

	@SystemLog(description = "-分页查询", type = LogType.OPERATION)
	@RequestMapping(value = "/getPage", method = RequestMethod.GET)
	@ApiOperation(value = "分页查询")
	public Result<Page<CertTypeBatchOperationDetail>> getPage(@ModelAttribute CertTypeBatchOperationDetail param, @ModelAttribute SearchVo searchVo, @ModelAttribute PageVo pageVo) {
		Page<CertTypeBatchOperationDetail> page = this.baseService.findByCondition(param, searchVo, pageVo);
		return ResultUtil.data(page);
	}
	
	@SystemLog(description = "-列表查询", type = LogType.OPERATION)
	@RequestMapping(value = "/getList", method = RequestMethod.GET)
	@ApiOperation(value = "列表查询")
	public Result<List<CertTypeBatchOperationDetail>> getList(@ModelAttribute CertTypeBatchOperationDetail param, @ModelAttribute SearchVo searchVo) {
		List<CertTypeBatchOperationDetail> list = this.baseService.findByCondition(param, searchVo);
		return ResultUtil.data(list);
	}
	
}
