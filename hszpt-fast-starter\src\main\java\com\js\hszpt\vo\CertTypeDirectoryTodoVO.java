package com.js.hszpt.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

@Data
@ApiModel("证照类型目录待办VO")
public class CertTypeDirectoryTodoVO {
    @ApiModelProperty("申请ID")
    private String applyId;

    @ApiModelProperty("审批ID")
    private String approvalId;

    @ApiModelProperty("证照类型目录ID")
    private String certTypeDirId;

    @ApiModelProperty("申请编号")
    private String applyNo;

    @ApiModelProperty("申请类型")
    private String applyType;

    @ApiModelProperty("申请类型名称")
    private String applyTypeName;

    @ApiModelProperty("发布机构")
    private String publishOrgName;

    @ApiModelProperty("证照类型")
    private String certificateTypeName;

    @ApiModelProperty("证照类型代码")
    private String certificateTypeCode;

    @ApiModelProperty("证照名称")
    private String certificateName;

    @ApiModelProperty("证照定义机构")
    private String createOrgName;

    @ApiModelProperty("关联事项名称")
    private String relatedItemName;

    @ApiModelProperty("关联事项代码")
    private String relatedItemCode;

    @ApiModelProperty("持证主体类别")
    private String certificateHolderCategory;

    @ApiModelProperty("持证主体类别名称")
    private String certificateHolderCategoryName;

    @ApiModelProperty("有效期限")
    private String validityRange;

    @ApiModelProperty("审批状态")
    private String approvalStatus;

    @ApiModelProperty("审批状态名称")
    private String approvalStatusName;

    @ApiModelProperty("下发状态")
    private String issueStatus;

    @ApiModelProperty("下发状态名称")
    private String issueStatusName;

    @ApiModelProperty("下发日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date publishTime;

    @ApiModelProperty("受理日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date acceptTime;

    @ApiModelProperty("审批日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date approvalTime;

    @ApiModelProperty("受理机构")
    private String acceptOrgName;

    @ApiModelProperty("审批机构")
    private String approvalOrgName;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "当前操作节点", hidden = true)
    private String currentNode;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("状态名称")
    private String statusName;

    @ApiModelProperty("待办人员编码")
    private String userCode;

    @ApiModelProperty("申请人ID")
    private String applicantId;

    @ApiModelProperty("显示处理按钮标志")
    private String showProcessButton;

    @ApiModelProperty("显示查看按钮标志")
    private String showViewButton;

    @ApiModelProperty("证照类型目录审批申请表主键ID")
    private String certTypeApprApplyId;

    @ApiModelProperty("待办任务按钮权限")
    private TodoButtonPermissionVO buttonPermission;
}