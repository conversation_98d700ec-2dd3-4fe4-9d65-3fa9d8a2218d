package com.js.hszpt.api;


import com.js.hszpt.entity.LawWorkflowNodeComponent;
import com.js.hszpt.service.LawWorkflowNodeComponentService;
 
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.js.core.api.BaseApiPlus;
import com.js.core.common.utils.ResultUtil;
import com.js.core.common.vo.PageVo;
import com.js.core.common.vo.Result;
import com.js.core.common.vo.SearchVo;
import com.js.annotation.SystemLog;
import com.js.enums.LogType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import java.util.List;


 /**
 * 
 * @ClassName: LawWorkflowNodeComponentApi  
 * @Description:TODO(工作流前端节点组件展示配置表接口)   
 * @author:  System Generation  
 *
 */
@Slf4j
@RestController
@Api(description = "工作流前端节点组件展示配置表接口")
@RequestMapping("/lawWorkflowNodeComponent")
public class LawWorkflowNodeComponentApi extends BaseApiPlus<LawWorkflowNodeComponentService,LawWorkflowNodeComponent,String>{

	@SystemLog(description = "工作流前端节点组件展示配置表-分页查询", type = LogType.OPERATION)
	@RequestMapping(value = "/getPage", method = RequestMethod.GET)
	@ApiOperation(value = "分页查询")
	public Result<Page<LawWorkflowNodeComponent>> getPage(@ModelAttribute LawWorkflowNodeComponent param, @ModelAttribute SearchVo searchVo, @ModelAttribute PageVo pageVo) {
		Page<LawWorkflowNodeComponent> page = this.baseService.findByCondition(param, searchVo, pageVo);
		return ResultUtil.data(page);
	}
	
	@SystemLog(description = "工作流前端节点组件展示配置表-列表查询", type = LogType.OPERATION)
	@RequestMapping(value = "/getList", method = RequestMethod.GET)
	@ApiOperation(value = "列表查询")
	public Result<List<LawWorkflowNodeComponent>> getList(@ModelAttribute LawWorkflowNodeComponent param, @ModelAttribute SearchVo searchVo) {
		List<LawWorkflowNodeComponent> list = this.baseService.findByCondition(param, searchVo);
		return ResultUtil.data(list);
	}
	
}
