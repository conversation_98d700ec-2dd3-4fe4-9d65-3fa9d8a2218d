package com.js.hszpt.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.hszpt.dto.CertificateIssueDetailStatisticsDTO;
import com.js.hszpt.dto.CertificateIssueStatisticsDTO;
import com.js.hszpt.dto.CertificateIssueTimeTypeStatisticsDTO;
import com.js.hszpt.dto.CertificateTypeStatisticsDTO;
import com.js.hszpt.dto.CertificateTypeStatusStatisticsDTO;
import com.js.hszpt.dto.ApplicationSourceStatisticsDTO;
import com.js.hszpt.dto.TimeStatisticsDTO;
import com.js.hszpt.entity.DwsCertificateIssue;
import com.js.hszpt.mapper.DwsCertificateIssueMapper;
import com.js.hszpt.service.DwsCertificateIssueService;
import com.js.hszpt.vo.CertificateIssueStatisticsVO;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 电子证照签发情况统计Service实现类
 */
@Service
public class DwsCertificateIssueServiceImpl extends ServiceImpl<DwsCertificateIssueMapper, DwsCertificateIssue> implements DwsCertificateIssueService {

    /**
     * 统计证照签发情况
     * @param param 查询参数
     * @return 统计结果
     */
    @Override
    public List<CertificateIssueStatisticsDTO> statisticsCertificateIssue(CertificateIssueStatisticsVO param) {
        // 调用Mapper进行统计
        return baseMapper.statisticsCertificateIssue(param);
    }
    
    /**
     * 按年统计证照签发情况
     * @param param 查询参数
     * @return 按年统计结果
     */
    @Override
    public List<CertificateIssueTimeTypeStatisticsDTO> statisticsCertificateIssueByYear(CertificateIssueStatisticsVO param) {
        // 调用Mapper进行按年统计
        return baseMapper.statisticsCertificateIssueByYear(param);
    }
    
    /**
     * 详细统计证照签发情况
     * @param param 查询参数
     * @return 详细统计结果
     */
    @Override
    public List<CertificateIssueDetailStatisticsDTO> statisticsCertificateIssueDetail(CertificateIssueStatisticsVO param) {
        // 获取基础统计数据
        List<CertificateIssueDetailStatisticsDTO> detailList = baseMapper.statisticsCertificateIssueDetail(param);
        
        // 获取总签发量
        Long totalIssueCount = baseMapper.getTotalIssueCount(param);
        
        // 创建新的结果列表，用于添加汇总数据
        List<CertificateIssueDetailStatisticsDTO> resultList = new ArrayList<>();
        
        // 获取登录人所在机构的名称
        String loginUserOrgName = baseMapper.getOrgNameByOrgCode(param.getLoginUserOrgCode());
        
        // 创建汇总数据对象
        CertificateIssueDetailStatisticsDTO summaryData = new CertificateIssueDetailStatisticsDTO();
        summaryData.setOrgCode(param.getLoginUserOrgCode());
        summaryData.setOrgName(loginUserOrgName);
        summaryData.setIssueCount(0L);
        summaryData.setCounterIssueCount(0L);
        summaryData.setOnlineIssueCount(0L);
        summaryData.setIssueRatio(100.0); // 汇总数据占比为100%
        
        // 获取上一个周期的总签发量
        Long previousTotalIssueCount = baseMapper.getPreviousPeriodIssueCount(param, param.getLoginUserOrgCode());
        
        // 计算环比率
        if (previousTotalIssueCount != null && previousTotalIssueCount > 0) {
            summaryData.setChainRatio(totalIssueCount * 100.0 / previousTotalIssueCount - 100);
        } else {
            summaryData.setChainRatio(0.0);
        }
        
        // 计算占比和环比率，同时累加汇总数据
        if (detailList != null && !detailList.isEmpty()) {
            for (CertificateIssueDetailStatisticsDTO detail : detailList) {
                // 计算占比
                if (totalIssueCount != null && totalIssueCount > 0) {
                    detail.setIssueRatio(detail.getIssueCount() * 100.0 / totalIssueCount);
                } else {
                    detail.setIssueRatio(0.0);
                }
                
                // 获取上一个周期的签发量并计算环比率
                Long previousIssueCount = baseMapper.getPreviousPeriodIssueCount(param, detail.getOrgCode());
                if (previousIssueCount != null && previousIssueCount > 0) {
                    detail.setChainRatio(detail.getIssueCount() * 100.0 / previousIssueCount - 100);
                } else {
                    detail.setChainRatio(0.0);
                }
                
                // 累加汇总数据
                summaryData.setIssueCount(summaryData.getIssueCount() + detail.getIssueCount());
                summaryData.setCounterIssueCount(summaryData.getCounterIssueCount() + detail.getCounterIssueCount());
                summaryData.setOnlineIssueCount(summaryData.getOnlineIssueCount() + detail.getOnlineIssueCount());
            }
        }
        
        // 将汇总数据添加到结果列表的最前面
        resultList.add(summaryData);
        // 将原始数据添加到结果列表
        resultList.addAll(detailList);
        
        return resultList;
    }
    
    /**
     * 按证照类型统计签发情况
     * @param param 查询参数
     * @return 按证照类型统计结果
     */
    @Override
    public List<CertificateTypeStatisticsDTO> statisticsByCertificateType(CertificateIssueStatisticsVO param) {
        // 调用Mapper进行按证照类型统计
        return baseMapper.statisticsByCertificateType(param);
    }

    /**
     * 按证照类型和状态统计签发情况
     * @param param 查询参数
     * @return 按证照类型和状态统计结果
     */
    @Override
    public List<CertificateTypeStatusStatisticsDTO> statisticsByCertificateTypeAndStatus(CertificateIssueStatisticsVO param) {
        // 调用Mapper进行按证照类型和状态统计
        List<CertificateTypeStatusStatisticsDTO> detailList = baseMapper.statisticsByCertificateTypeAndStatus(param);
        
        // 创建新的结果列表，用于添加汇总数据
        List<CertificateTypeStatusStatisticsDTO> resultList = new ArrayList<>();
        
        // 创建汇总数据对象
        CertificateTypeStatusStatisticsDTO summaryData = new CertificateTypeStatusStatisticsDTO();
        summaryData.setCertificateTypeCode("total");
        summaryData.setCertificateTypeName("总计");
        summaryData.setIssueCount(0L);
        summaryData.setValidCount(0L);
        summaryData.setInvalidCount(0L);
        summaryData.setIssueRatio(100.0); // 汇总数据占比为100%
        summaryData.setIsSummary(true);
        
        // 计算总签发量，用于计算占比
        Long totalIssueCount = 0L;
        
        // 累加各证照类型的数据到汇总数据
        if (detailList != null && !detailList.isEmpty()) {
            for (CertificateTypeStatusStatisticsDTO detail : detailList) {
                totalIssueCount += detail.getIssueCount();
                summaryData.setIssueCount(summaryData.getIssueCount() + detail.getIssueCount());
                summaryData.setValidCount(summaryData.getValidCount() + detail.getValidCount());
                summaryData.setInvalidCount(summaryData.getInvalidCount() + detail.getInvalidCount());
            }
        }
        
        // 获取上一个周期的总签发量，用于计算汇总数据的环比率
        Long previousTotalIssueCount = 0L;
        for (CertificateTypeStatusStatisticsDTO detail : detailList) {
            // 获取上一个周期的签发量
            Long previousIssueCount = baseMapper.getPreviousPeriodIssueCountByType(param, detail.getCertificateTypeCode());
            previousTotalIssueCount += (previousIssueCount != null ? previousIssueCount : 0L);
        }
        
        // 计算汇总数据的环比率
        if (previousTotalIssueCount != null && previousTotalIssueCount > 0) {
            summaryData.setChainRatio(summaryData.getIssueCount() * 100.0 / previousTotalIssueCount - 100);
        } else {
            summaryData.setChainRatio(0.0);
        }
        
        // 计算各证照类型的占比和环比率
        if (detailList != null && !detailList.isEmpty()) {
            for (CertificateTypeStatusStatisticsDTO detail : detailList) {
                // 计算占比
                if (totalIssueCount != null && totalIssueCount > 0) {
                    detail.setIssueRatio(detail.getIssueCount() * 100.0 / totalIssueCount);
                } else {
                    detail.setIssueRatio(0.0);
                }
                
                // 获取上一个周期的签发量并计算环比率
                Long previousIssueCount = baseMapper.getPreviousPeriodIssueCountByType(param, detail.getCertificateTypeCode());
                if (previousIssueCount != null && previousIssueCount > 0) {
                    detail.setChainRatio(detail.getIssueCount() * 100.0 / previousIssueCount - 100);
                } else {
                    detail.setChainRatio(0.0);
                }
            }
        }
        
        // 将汇总数据添加到结果列表的最前面
        resultList.add(summaryData);
        // 将原始数据添加到结果列表
        resultList.addAll(detailList);
        
        return resultList;
    }

    /**
     * 按申请来源统计签发情况
     * @param param 查询参数
     * @return 按申请来源统计结果
     */
    @Override
    public List<ApplicationSourceStatisticsDTO> statisticsByApplicationSource(CertificateIssueStatisticsVO param) {
        // 调用Mapper进行按申请来源统计
        List<ApplicationSourceStatisticsDTO> detailList = baseMapper.statisticsByApplicationSource(param);
        
        // 创建新的结果列表，用于添加汇总数据
        List<ApplicationSourceStatisticsDTO> resultList = new ArrayList<>();
        
        // 计算总签发量，用于计算占比
        Long totalIssueCount = 0L;
        
        // 累加各申请来源的数据到总量
        if (detailList != null && !detailList.isEmpty()) {
            for (ApplicationSourceStatisticsDTO detail : detailList) {
                totalIssueCount += detail.getIssueCount();
            }
        }
        
        // 计算各申请来源的占比
        if (detailList != null && !detailList.isEmpty()) {
            for (ApplicationSourceStatisticsDTO detail : detailList) {
                // 计算占比
                if (totalIssueCount != null && totalIssueCount > 0) {
                    detail.setIssueRatio(detail.getIssueCount() * 100.0 / totalIssueCount);
                } else {
                    detail.setIssueRatio(0.0);
                }
            }
        }
        
        // 将原始数据添加到结果列表
        resultList.addAll(detailList);
        
        return resultList;
    }

    /**
     * 按时间统计签发情况
     * @param param 查询参数
     * @return 按时间统计结果
     */
    @Override
    public List<TimeStatisticsDTO> statisticsByTime(CertificateIssueStatisticsVO param) {
        // 调用Mapper进行按时间统计
        List<TimeStatisticsDTO> detailList = baseMapper.statisticsByTime(param);
        
        // 创建新的结果列表，用于添加汇总数据
        List<TimeStatisticsDTO> resultList = new ArrayList<>();
        
        // 创建汇总数据对象
        TimeStatisticsDTO summaryData = new TimeStatisticsDTO();
        summaryData.setTimePoint("总计");
        summaryData.setIssueCount(0L);
        summaryData.setIssueRatio(100.0); // 汇总数据占比为100%
        summaryData.setIsSummary(true);
        
        // 计算总签发量，用于计算占比
        Long totalIssueCount = 0L;
        
        // 累加各时间点的数据到汇总数据
        if (detailList != null && !detailList.isEmpty()) {
            for (TimeStatisticsDTO detail : detailList) {
                totalIssueCount += detail.getIssueCount();
                summaryData.setIssueCount(summaryData.getIssueCount() + detail.getIssueCount());
            }
        }
        
        // 计算各时间点的占比
        if (detailList != null && !detailList.isEmpty()) {
            for (TimeStatisticsDTO detail : detailList) {
                // 计算占比
                if (totalIssueCount != null && totalIssueCount > 0) {
                    detail.setIssueRatio(detail.getIssueCount() * 100.0 / totalIssueCount);
                } else {
                    detail.setIssueRatio(0.0);
                }
            }
        }
        
        // 将汇总数据添加到结果列表的最前面
        resultList.add(summaryData);
        // 将原始数据添加到结果列表
        resultList.addAll(detailList);
        
        return resultList;
    }
} 