package com.js.hszpt.entity;

import com.js.core.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.Builder;
import java.util.Date;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import com.js.util.SnowFlakeUtil;


/**
 * 
 * @ClassName:  LawWorkflowNodeOrg   
 * @Description:TODO(工作流模板节点机构自定义配置表)   
 * @author:   System Generation 
 */
@Data

@TableName("LAW_WORKFLOW_NODE_ORG")
@ApiModel(value = "工作流模板节点机构自定义配置表")
public class LawWorkflowNodeOrg extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "LAW_WORKFLOW_NODE_ORG_ID", type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value = "主键ID")
    private String id = String.valueOf(SnowFlakeUtil.nextId());

    /**
    * 工作流模板节点机构自定义配置表ID
    */
    @ApiModelProperty(value = "工作流模板节点机构自定义配置表ID")
    private String lawWorkflowNodeOrgId;


    /**
    * 工作流模板节点表ID
    */
    @ApiModelProperty(value = "工作流模板节点表ID")
    private String lawWorkflowNodeInfoId;


    /**
    * 工作流模板配置表ID
    */
    @ApiModelProperty(value = "工作流模板配置表ID")
    private String lawWorkflowNodeSetId;


    /**
    * 海事机构
    */
    @ApiModelProperty(value = "海事机构")
    private String orgCode;


    /**
    * 创建人
    */
    @ApiModelProperty(value = "创建人")
    private String createOperId;


    /**
    * 创建部门
    */
    @ApiModelProperty(value = "创建部门")
    private String createOperDept;


    /**
    * 修改人
    */
    @ApiModelProperty(value = "修改人")
    private String modifyOperId;


    /**
    * 修改部门
    */
    @ApiModelProperty(value = "修改部门")
    private String modifyOperDept;


    /**
    * 修改时间
    */
    @ApiModelProperty(value = "修改时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;


    /**
    * 版本号
    */
    @ApiModelProperty(value = "版本号")
    private String recordVersion;


}