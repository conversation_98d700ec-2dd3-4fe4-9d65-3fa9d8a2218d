package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;


/**
 *
 * @ClassName:  CtfCertificateType
 * @Description:TODO(证照分类表)
 * @author:   System Generation
 */
@Data

@TableName("ctf_certificate_type")
@ApiModel(value = "证照分类表")
public class CtfCertificateType {

    private static final long serialVersionUID = 1L;

    /**
    *
    */
    @TableId
    @TableField("CERTIFICATE_TYPE_ID")
    @ApiModelProperty(value = "")
    private String certificateTypeId;


    /**
    *
    */
    @TableField("CERTIFICATE_NAME")
    @ApiModelProperty(value = "")
    private String certificateName;


    /**
    *
    */
    @TableField("CERTIFICATE_TYPE_CODE")
    @ApiModelProperty(value = "")
    private String certificateTypeCode;


    /**
    *
    */
    @TableField("DESCRIPTION")
    @ApiModelProperty(value = "")
    private String description;


    /**
    *
    */
    @TableField("CREATE_OPER_ID")
    @ApiModelProperty(value = "")
    private String createOperId;


    /**
    *
    */
    @TableField("CREATE_DATE")
    @ApiModelProperty(value = "")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;


    /**
    *
    */
    @TableField("MODIFY_OPER_ID")
    @ApiModelProperty(value = "")
    private String modifyOperId;


    /**
    *
    */
    @TableField("MODIFY_DATE")
    @ApiModelProperty(value = "")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyDate;


    /**
    *
    */
    @TableField("DEL_FLAG")
    @ApiModelProperty(value = "")
    private String delFlag;


    /**
    *
    */
    @TableField("CERTIFICATE_CN_EN")
    @ApiModelProperty(value = "")
    private String certificateCnEn;


    /**
    *
    */
    @TableField("CERT_TYPE_DIR_ID")
    @ApiModelProperty(value = "")
    private String certTypeDirId;

    /**
    *
    */
    @TableField("AFFAIR_NAME")
    @ApiModelProperty(value = "")
    private String affairName;


    /**
    *
    */
    @TableField("ORG_VIEW_TITLE")
    @ApiModelProperty(value = "")
    private String orgViewTitle;

    @TableField("CERT_TYPE_DIR_CODE")
    @ApiModelProperty(value = "")
    private String certTypeDirCode;

    @TableField("CERT_TYPE_DIR_NAME")
    @ApiModelProperty(value = "")
    private String certTypeDirName;

    /**
     * 证照分类，1-船舶证照 2-船员证照
     */
    @TableField("CERT_CLASSIFICATION")
    private String certClassification;

}
