package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

@Data
@TableName("biz_affair_apply")
public class BizAffairApply implements Serializable {

    @TableId
    private String applyId;

    private String parentApplyId;

    private String applyNo;

    private String affairName;

    private String applicantId;

    private String applicantName;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private  Date applyDate;

    private String proType;

    private String applyStatus;

    private String proName;

    private String contactName;

    private String contactPhone;

    private String modelType;

    private String affairNo;

    private String legalDate;

    private String acceptOrgCode;

    private String receiveType;

    private String affairId;

    private String applyStatusHall;

    private String applyTimes;

    private String applyType;

    private String apprNo;

    private String uscc;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date acceptDate;

    private String legalPerson;

    private String orgAddr;

    private String operatorNo;

    private String operatorPhone;

    private String ifEntrustCode;

    private String firstAcceptOrg;

    private String acceptOrg;

    private String apprOrgCode;

    private String apprOrgName;

    private String auditOrgCode;

    private String auditOrgName;

    private String shipId;

    private String shipCode;

    private String addInfo;

    private String modifierId;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private  Date modifyDate;

    private String remark;

    private String creatorId;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private  Date createDate;

    private String creatorOrgCode;

    private String sourceCode;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private  Date recCreateDate;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private  Date recModifyDate;

    private String msaOrgCode;

    private String cleanStatus;
}
