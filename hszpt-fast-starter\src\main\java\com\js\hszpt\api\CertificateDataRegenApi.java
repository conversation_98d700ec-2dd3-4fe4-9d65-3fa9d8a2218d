package com.js.hszpt.api;

import cn.hutool.core.collection.CollUtil;
import com.js.core.common.utils.ResultUtil;
import com.js.core.common.vo.Result;
import com.js.hszpt.entity.CertificateDataRegen;
import com.js.hszpt.entity.CertificateType;
import com.js.hszpt.job.CreateCertificateRegenJob;
import com.js.hszpt.service.CertificateDataRegenService;
import com.js.hszpt.service.CertificateTypeService;
import com.js.hszpt.vo.CertificateCallbackRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 证照重新生成API接口
 */
@Slf4j
@RestController
@RequestMapping("/certificateRegen")
@Api(tags = "证照重新生成接口")
public class CertificateDataRegenApi {

    @Autowired
    private CertificateDataRegenService certificateDataRegenService;

    @Autowired
    private CreateCertificateRegenJob createCertificateRegenJob;

    @GetMapping("/regen")
    public Result<String> regenCertificate(String dataIds){
        String[] dataIdArray = dataIds.split(",");
        List<String> list = Arrays.asList(dataIdArray);
        if (CollUtil.isEmpty(list)) {
            return ResultUtil.error("dataIds参数异常");
        }
        List<CertificateDataRegen> certificateDataRegenList = certificateDataRegenService.getByDataIds(list);
        if (CollUtil.isEmpty(certificateDataRegenList)) {
            return ResultUtil.error("未找到对应的证照数据");
        }
        createCertificateRegenJob.regenCertificate(certificateDataRegenList);
        return ResultUtil.success(dataIds + "正在重新生成");
    }

    /**
     * 提供给外部系统的证照生成接口
     * @param certificateDataRegen 证照数据
     * @return 证照ID
     */
    @PostMapping("/create")
    @ApiOperation(value = "生成证照", notes = "接收外部系统传入的证照数据，保存到系统中，由定时任务处理")
    public Result<String> createCertificate(
            @ApiParam(value = "证照数据", required = true)
            @RequestBody CertificateDataRegen certificateDataRegen) {
        try {
//            log.info("【证照生成API】接收到外部系统请求，参数{}",certificateDataRegen);

            // 调用服务层方法创建证照数据
            String certificateId = certificateDataRegenService.createCertificateData(certificateDataRegen);

            log.info("【证照生成API】证照数据创建成功，certificateId：{}", certificateId);
            return ResultUtil.data(certificateId);
        } catch (Exception e) {
            log.error("【证照生成API】证照数据保存异常", e);
            return ResultUtil.error("证照数据保存异常：" + e.getMessage());
        }
    }

    /**
     * 证照生成结果回调接口
     * @param request 回调请求参数
     * @return 处理结果
     */
    @PostMapping("/callback")
    @ApiOperation(value = "证照生成结果回调", notes = "接收证照生成结果回调，更新数据库状态并通知船员系统")
    public Result<String> certificateCallback(
            @ApiParam(value = "回调请求参数", required = true)
            @RequestBody CertificateCallbackRequest request) {
        try {
            log.info("【证照回调API】接收到回调请求，参数：{}", request);

            String certificateId = request.getCertificateId();
            String code = request.getCode();
            String msg = request.getMsg();

            // 调用Service层处理业务逻辑
            String result = certificateDataRegenService.handleCertificateCallback(certificateId, code, msg, false);

            log.info("【证照回调API】处理成功，certificateId：{}", certificateId);
            return ResultUtil.data(result);
        } catch (IllegalArgumentException e) {
            log.error("【证照回调API】参数校验失败：{}", e.getMessage());
            return ResultUtil.error(e.getMessage());
        } catch (Exception e) {
            log.error("【证照回调API】处理回调请求异常", e);
            return ResultUtil.error("处理回调请求异常：" + e.getMessage());
        }
    }

    /**
     * 证照生成结果回调接口（正孚回调）
     * @param certificateId 证照ID
     * @param code 回调状态码：0-成功，1-失败
     * @param msg 回调消息
     * @return 处理结果
     */
    @PostMapping(value = "/callbackMsacert", consumes = "application/x-www-form-urlencoded;charset=UTF-8")
    @ApiOperation(value = "证照生成结果回调", notes = "接收证照生成结果回调，更新数据库状态并通知船员系统")
    public Result<String> certificateCallbackMsacert(
            @ApiParam(value = "证照ID", required = true)
            @RequestParam("certificateId") String certificateId,
            @ApiParam(value = "回调状态码：0-成功，1-失败", required = true)
            @RequestParam("code") String code,
            @ApiParam(value = "回调消息", required = true)
            @RequestParam("msg") String msg) {
        try {
            log.info("【证照回调API】接收到回调请求，certificateId：{}，code：{}，msg：{}", certificateId, code, msg);

            // 调用Service层处理业务逻辑
            String result = certificateDataRegenService.handleCertificateCallback(certificateId, code, msg, true);

            log.info("【证照回调API】处理成功，certificateId：{}", certificateId);
            return ResultUtil.data(result);
        } catch (IllegalArgumentException e) {
            log.error("【证照回调API】参数校验失败：{}", e.getMessage());
            return ResultUtil.error(e.getMessage());
        } catch (Exception e) {
            log.error("【证照回调API】处理回调请求异常", e);
            return ResultUtil.error("处理回调请求异常：" + e.getMessage());
        }
    }

}
