package com.js.hszpt.api;


import com.js.hszpt.entity.LawWorkflowBpm;
import com.js.hszpt.service.LawWorkflowBpmService;
 
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.js.core.api.BaseApiPlus;
import com.js.core.common.utils.ResultUtil;
import com.js.core.common.vo.PageVo;
import com.js.core.common.vo.Result;
import com.js.core.common.vo.SearchVo;
import com.js.annotation.SystemLog;
import com.js.enums.LogType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import java.util.List;

import com.js.hszpt.vo.CertTypeDirectoryTodoVO;
import com.js.hszpt.vo.CertTypeDirectoryQueryVO;


 /**
 * 
 * @ClassName: LawWorkflowBpmApi  
 * @Description:TODO(待办任务表接口)   
 * @author:  System Generation  
 *
 */
@Slf4j
@RestController
@Api(description = "待办任务表接口")
@RequestMapping("/lawWorkflowBpm")
public class LawWorkflowBpmApi extends BaseApiPlus<LawWorkflowBpmService,LawWorkflowBpm,String>{

	@SystemLog(description = "待办任务表-分页查询", type = LogType.OPERATION)
	@RequestMapping(value = "/getPage", method = RequestMethod.GET)
	@ApiOperation(value = "分页查询")
	public Result<Page<LawWorkflowBpm>> getPage(@ModelAttribute LawWorkflowBpm param, @ModelAttribute SearchVo searchVo, @ModelAttribute PageVo pageVo) {
		Page<LawWorkflowBpm> page = this.baseService.findByCondition(param, searchVo, pageVo);
		return ResultUtil.data(page);
	}
	
	@SystemLog(description = "待办任务表-列表查询", type = LogType.OPERATION)
	@RequestMapping(value = "/getList", method = RequestMethod.GET)
	@ApiOperation(value = "列表查询")
	public Result<List<LawWorkflowBpm>> getList(@ModelAttribute LawWorkflowBpm param, @ModelAttribute SearchVo searchVo) {
		List<LawWorkflowBpm> list = this.baseService.findByCondition(param, searchVo);
		return ResultUtil.data(list);
	}
	
	@GetMapping("/queryCertTypeDirectoryTodoList")
	@ApiOperation("查询证照类型目录待办列表")
	@SystemLog(description = "查询证照类型目录待办列表", type = LogType.OPERATION)
	public Result<Page<CertTypeDirectoryTodoVO>> queryCertTypeDirectoryTodoList(CertTypeDirectoryQueryVO queryVO) {
		log.info("开始查询证照类型目录待办列表，查询条件：{}", queryVO);
		try {
			// 1. 参数校验
			if (queryVO.getPageNumber() <= 0) {
				return ResultUtil.error("页码必须大于0");
			}
			if (queryVO.getPageSize() <= 0) {
				return ResultUtil.error("每页条数必须大于0");
			}
			
			// 2. 日期格式校验
			if (!validateDateRange(queryVO)) {
				return ResultUtil.error("日期格式不正确或日期范围无效");
			}

			// 3. 执行查询
			Page<CertTypeDirectoryTodoVO> result = baseService.queryCertTypeDirectoryTodoList(queryVO);
			
			log.info("查询证照类型目录待办列表成功，总记录数：{}，当前页数据量：{}", 
				result.getTotal(), result.getRecords().size());
				
			return ResultUtil.data(result);
		} catch (Exception e) {
			log.error("查询证照类型目录待办列表失败", e);
			return ResultUtil.error("查询证照类型目录待办列表失败：" + e.getMessage());
		}
	}

	private boolean validateDateRange(CertTypeDirectoryQueryVO queryVO) {
		// 下发日期范围校验
		if (queryVO.getIssueDateStart() != null && queryVO.getIssueDateEnd() != null) {
			if (queryVO.getIssueDateStart().after(queryVO.getIssueDateEnd())) {
				return false;
			}
		}
		
		// 创建日期范围校验
		if (queryVO.getCreateTimeStart() != null && queryVO.getCreateTimeEnd() != null) {
			if (queryVO.getCreateTimeStart().after(queryVO.getCreateTimeEnd())) {
				return false;
			}
		}
		
		return true;
	}
	
}
