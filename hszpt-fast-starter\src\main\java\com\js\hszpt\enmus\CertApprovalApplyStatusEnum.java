package com.js.hszpt.enmus;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 证照类型目录审批申请状态枚举
 */
@Getter
@AllArgsConstructor
public enum CertApprovalApplyStatusEnum {
    
    PENDING_AUDIT("1", "待审核"),
    AUDIT_PASS("2", "审核通过"),
    AUDIT_REJECT("3", "审核不通过"),
    AUDIT_RETURN("4", "审核退回"),
    APPLY_REVOKE("5", "申请撤回"),
    PENDING_APPROVAL("6", "待审批"),
    APPROVAL_PASS("7", "审批通过"),
    APPROVAL_REJECT("8", "审批不通过"),
    APPROVAL_RETURN("9", "审批退回"),
    AUDIT_WITHDRAW("10", "审核撤回");
    
    private final String code;
    private final String desc;
    
    /**
     * 根据code获取枚举
     */
    public static CertApprovalApplyStatusEnum getByCode(String code) {
        for (CertApprovalApplyStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
} 