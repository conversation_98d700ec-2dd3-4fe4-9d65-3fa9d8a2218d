package com.js.hszpt.utils;

import lombok.extern.slf4j.Slf4j;
import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;

@Slf4j
public class ChineseToPinyin {

    public static String toPinyin(String chinese) {

        //存英文不需要翻译
        if(isEnglishOrNumberOnly(chinese)){
            return "";
        }

        StringBuilder stringBuilder = new StringBuilder();
        char[] charArray = chinese.replace("、", "").trim().toCharArray();
        for (int i = 0; i < charArray.length; i++) {
            // 可以根据需要添加更多汉字Unicode区块
            if (Character.UnicodeBlock.of(charArray[i]) == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS
                    || Character.UnicodeBlock.of(charArray[i]) == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS
                    || Character.UnicodeBlock.of(charArray[i]) == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A) {
                if ( i>1 && (charArray[i-1]+ "").matches("^\\d+$")){//判断是否是数字
                    stringBuilder.append(" ");
                }
                stringBuilder.append(charArray[i]).append(" ");
            }else {
                stringBuilder.append(charArray[i]);
            }
        }

        chinese = stringBuilder.substring(0,stringBuilder.length()-1);
        StringBuilder sb = null;
        try {
            HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();
            format.setCaseType(HanyuPinyinCaseType.LOWERCASE);
            format.setToneType(HanyuPinyinToneType.WITHOUT_TONE);

            sb = new StringBuilder();
            char[] chars = chinese.toCharArray();
            for (char c : chars) {
                if (c > 128) {
                    try {
                        String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(c, format);
                        sb.append(pinyinArray[0]);
                    } catch (BadHanyuPinyinOutputFormatCombination e) {
                        e.printStackTrace();
                    }
                } else {
                    sb.append(c);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("中文转换拼音异常，文本内容：{}", chinese);
            return "";
        }
        return sb.toString();
    }

    /**
     * 判断字符串是否只包含英文字母、英文符号或数字
     *
     * @param str 待检查的字符串
     * @return true:只包含英文字母、英文符号或数字; false:包含其他字符
     */
    public static boolean isEnglishOrNumberOnly(String str) {
        if (str == null || str.trim().isEmpty()) {
            return false;
        }

        // 正则表达式：匹配英文字母、英文符号和数字
        // ^: 开始
        // $: 结束
        // +: 一个或多个
        // [a-zA-Z]: 英文字母
        // [0-9]: 数字
        // [!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?\\s]: 英文符号和空白字符
        String regex = "^[a-zA-Z0-9!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?\\s]+$";

        return str.matches(regex);
    }

    public static void main(String[] args) {
        String chineseName = "90 FENCHURCH STREET, LONDON,EC3M 4ST,ENGLAND";
        long startTime = System.currentTimeMillis();
        String pinyinName = toPinyin(chineseName);

        System.out.println(pinyinName + "，耗时：" + (System.currentTimeMillis() - startTime)); // zhangsan

        System.out.println("纯英文：" + isEnglishOrNumberOnly(chineseName));

    }
}
