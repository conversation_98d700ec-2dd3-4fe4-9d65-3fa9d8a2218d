package com.js.hszpt.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.js.hszpt.dto.LawWorkflowNodeUserDTO;
import com.js.hszpt.dto.LawWorkflowNodeUserListDto;
import com.js.hszpt.entity.LawWorkflowNodeUser;
import com.js.hszpt.vo.LawWorkflowNodeUserListVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 
 * @ClassName:  LawWorkflowNodeUser   
 * @Description:TODO(工作流用户信息数据处理层)   
 * @author:   System Generation 
 */
public interface LawWorkflowNodeUserDao extends BaseMapper<LawWorkflowNodeUser> {


//    Page<LawWorkflowNodeUserListVo> selectWorkflowNodeUser(
//        @Param("lawWorkflowNodeInfoId") String lawWorkflowNodeInfoId,
//        @Param("selfOrgCodeList") List<String> selfOrgCodeList,
//        @Param("noSelfOrgCode") String noSelfOrgCode,
//        @Param("page") Page<LawWorkflowNodeUserListVo> page
//    );

    Page<LawWorkflowNodeUserListVo> selectUserForWorkflowSet(Page<LawWorkflowNodeUserListVo> page,@Param("dto") LawWorkflowNodeUserListDto lawWorkflowNodeUserListDto);


    int batchSaveWorkflowNodeUsers(List<LawWorkflowNodeUserDTO> lawWorkflowNodeUserDTOS);

    int deleteBylawWorkflowNodeUserId(List<String> lawWorkflowNodeUserId);

    Page<LawWorkflowNodeUserListVo> selectWorkflowNodeUser(Page<LawWorkflowNodeUserListVo> page, @Param("dto")LawWorkflowNodeUserListDto lawWorkflowNodeUserListDto);

    List<LawWorkflowNodeUserListVo> selectWorkflowNodeUserList(@Param("dto")LawWorkflowNodeUserListDto lawWorkflowNodeUserListDto);

    Page<LawWorkflowNodeUserListVo> selectUserForWorkflowSet2(Page<LawWorkflowNodeUserListVo> page,@Param("dto") LawWorkflowNodeUserListDto lawWorkflowNodeUserListDto);

    Page<LawWorkflowNodeUserListVo> selectWorkflowNodeUserbj(Page<LawWorkflowNodeUserListVo> page, @Param("dto")LawWorkflowNodeUserListDto lawWorkflowNodeUserListDto);

    List<LawWorkflowNodeUserListVo> selectWorkflowNodeUserbjList(@Param("dto")LawWorkflowNodeUserListDto lawWorkflowNodeUserListDto);

}