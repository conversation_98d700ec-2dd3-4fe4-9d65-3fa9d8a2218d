# HTTP透传服务

## 项目介绍
本项目是一个HTTP透传服务，负责将接收到的HTTP请求转发到另一个HTTP服务，并将响应返回给客户端。

## 功能特点
- 完整透传HTTP请求，包括请求方法、路径、请求头和请求体
- 支持配置目标服务器的地址和端口
- 支持配置是否需要对目标服务进行鉴权
- 支持所有HTTP方法（GET, POST, PUT, DELETE等）

## 配置说明
在`application.yml`中配置以下参数：
- `proxy.target-url`: 目标服务器的URL
- `proxy.auth-enabled`: 是否启用鉴权（true/false）
- `proxy.auth-token`: 鉴权令牌（当auth-enabled为true时使用）

## 使用方法
1. 启动服务
2. 向本服务发送HTTP请求，请求会被转发到目标服务器
3. 目标服务器的响应会被返回给客户端 