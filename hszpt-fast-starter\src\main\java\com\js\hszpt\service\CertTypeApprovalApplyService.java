package com.js.hszpt.service;

import com.js.hszpt.entity.CertTypeApprovalApply;
import com.js.hszpt.mapper.CertTypeApprovalApplyDao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.core.common.utils.PageUtil;
import com.js.core.common.vo.PageVo;
import com.js.core.common.vo.SearchVo;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 *
 * @ClassName:  CertTypeApprovalApplyService
 * @Description:TODO(证照类型目录审批申请表接口实现)
 * @author:  System Generation
 *
 */
@Slf4j
@Service
public class CertTypeApprovalApplyService extends ServiceImpl<CertTypeApprovalApplyDao, CertTypeApprovalApply> {
	/**
	 * 多条件分页查询
	 * @param param
	 * @param searchVo
	 * @param pageVo
	 * @return
	 */
	public Page<CertTypeApprovalApply> findByCondition(CertTypeApprovalApply param, SearchVo searchVo, PageVo pageVo) {
		Page<CertTypeApprovalApply> page = PageUtil.initMpPage(pageVo);
		QueryWrapper<CertTypeApprovalApply> queryWrapper = this.getCondition(param,searchVo);
		return this.page(page, queryWrapper);
	}

	/**
	 *
	 * @Title: findByCondition
	 * @Description: TODO(多条件列表查询)
	 * @param param
	 * @param searchVo
	 * @return  List<CertTypeApprovalApply>
	 * @throws
	 */
	public List<CertTypeApprovalApply> findByCondition(CertTypeApprovalApply param, SearchVo searchVo){
		QueryWrapper<CertTypeApprovalApply> queryWrapper = this.getCondition(param, searchVo);
		//添加排序字段
		queryWrapper.orderByAsc("create_time");

		return this.list(queryWrapper);
	}


	/**
	 *
	 * @Title: getCondition
	 * @Description: TODO(构建自定义查询条件)
	 * @param param
	 * @param searchVo
	 * @return  QueryWrapper<CertTypeApprovalApply>
	 * @throws
	 */
	private QueryWrapper<CertTypeApprovalApply> getCondition(CertTypeApprovalApply param, SearchVo searchVo){
		QueryWrapper<CertTypeApprovalApply> queryWrapper = new QueryWrapper<CertTypeApprovalApply>();
		//请根据实际字段自行扩展

		// 创建时间
		if (StrUtil.isNotBlank(searchVo.getStartDate()) && StrUtil.isNotBlank(searchVo.getEndDate())) {
			Date start = DateUtil.parse(searchVo.getStartDate());
			Date end = DateUtil.parse(searchVo.getEndDate());
			queryWrapper.between("create_time",start, DateUtil.endOfDay(end));
		}
		return queryWrapper;

	}

	/**
	 * 通过证照类型目录ID查询审批申请
	 * @param certTypeDirId 证照类型目录ID
	 * @return CertTypeApprovalApply 审批申请信息
	 */
	public CertTypeApprovalApply getByCertTypeDirId(String certTypeDirId) {
		log.info("通过证照类型目录ID查询审批申请，certTypeDirId：{}", certTypeDirId);

		QueryWrapper<CertTypeApprovalApply> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq("cert_type_dir_id", certTypeDirId);
		queryWrapper.eq("del_flag", "0");  // 未删除的记录
		queryWrapper.orderByDesc("create_time");  // 按创建时间倒序，取最新的一条
		queryWrapper.last("LIMIT 1");  // 只取一条记录

		CertTypeApprovalApply apply = this.getOne(queryWrapper);

		log.info("查询结果：{}", apply);
		return apply;
	}


    /**
     * 废止/启用重复校验
     * @param certTypeDirId 证照类型目录ID
     * @return CertTypeApprovalApply 审批申请信息
     */
    public CertTypeApprovalApply getCertTypeDirIdIsAbolish(String certTypeDirId,String applyType) {
        log.info("废止/启用重复校验，certTypeDirId：{}", certTypeDirId);

        QueryWrapper<CertTypeApprovalApply> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("cert_type_dir_id", certTypeDirId);
        //申请类型 2-废止 3-启用
        queryWrapper.eq("apply_type", applyType);
        String issueStatus = applyType.equals("2") ? "3" : "2";
        //下发状态，1-未下发 2-已下发 3-已废止
        queryWrapper.eq("issue_status", issueStatus);
        queryWrapper.eq("issue_status", issueStatus);
        //未被处理的办件
//        queryWrapper.ne("status", "7");
        queryWrapper.eq("del_flag", "0");  // 未删除的记录
        queryWrapper.orderByDesc("create_time");  // 按创建时间倒序，取最新的一条
        queryWrapper.last("LIMIT 1");  // 只取一条记录

        CertTypeApprovalApply apply = this.getOne(queryWrapper);

        log.info("查询结果：{}", apply);
        return apply;
    }
}
