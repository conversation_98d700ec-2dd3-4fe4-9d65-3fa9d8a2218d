package com.js.hszpt.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.core.common.utils.ResultUtil;
import com.js.core.common.vo.Result;
import com.js.hszpt.builder.CertificateInfoBuilder;
import com.js.hszpt.config.CertificateDateConfig;
import com.js.hszpt.constants.Common;
import com.js.hszpt.dto.*;
import com.js.hszpt.enmus.*;
import com.js.hszpt.entity.*;
import com.js.hszpt.mapper.CertificateMapper;
import com.js.hszpt.properties.UrlConfig;
import com.js.hszpt.utils.*;
import com.js.hszpt.utils.CertificateDataUtil;
import com.js.hszpt.utils.encrypt.RSAEncryptionUtil;
import com.js.hszpt.utils.encrypt.Sm4Tool;
import com.js.hszpt.vo.*;
import com.js.hszpt.enmus.CertificateName;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.PrivateKey;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.js.hszpt.enmus.AffairApplyCleanStatus.*;
import static com.js.hszpt.enmus.CertificateInfo.*;

@Slf4j
@Service
public class CertificateService extends ServiceImpl<CertificateMapper, Certificate> {

    @Autowired
    private BizAffairApplyService bizAffairApplyService;

    @Autowired
    private CertificateDateConfig certificateDateConfig;

    @Autowired
    private CertificateAttributeService certificateAttributeService;

    @Autowired
    private UrlConfig urlConfig;

    @Autowired
    private BizEgCertCheckService bizEgCertCheckService;

    @Autowired
    private CertificateMapper certificateMapper;

    @Autowired
    private VerificationDisplayConfigService verificationDisplayConfigService;

    @Autowired
    private CertificateUtil certificateUtil;

    @Value("${qrcode.scanUrl}")
    private String scanUrl;

    @Value("${qrcode.security.privateKey}")
    private String privateKeyString;

    @Value("${qrcode.security.sm4SecretKey}")
    private String secretKeySm4;

    @Value("${qrcode.security.sm2PublicKey}")
    private String publicKeySm2;

    @Value("${api.login-check.url:http://198.10.51.2:8090/main/getLoginUser}")
    private String loginCheckUrl;

    @Value("${certificate.search-zf}")
    private boolean searchZf;

    /**
     * 正孚数据url
     */
    @Value("${certificate.zf-url}")
    private String zfUrl = "http://127.0.0.1:8285/certificateData/getFilePathByDataId/";

    @Value("${certificate.downloadPhotoUrl}")
    private String downloadPhotoUrl;

    @Autowired
    private VerifyCodeUtil verifyCodeUtil;
    @Autowired
    private BizEgApplyInfoService bizEgApplyInfoService;
    @Autowired
    private ShipInfoAllService shipInfoAllService;
    @Autowired
    private CertShipOwnershipService certShipOwnershipService;
    @Autowired
    private SysDeptIambService sysDeptIambService;
    @Autowired
    private SysDeptEnService sysDeptEnService;
    @Autowired
    private DictYthOrgMappingService dictYthOrgMappingService;

    @Autowired
    private BizEgAcceptService bizEgAcceptService;

    @Autowired
    private CertificateTypeService certificateTypeService;

    @Autowired
    private CertificateDataRegenService certificateDataRegenService;

    @Value("${spring.profiles.active}")
    private String active;

    @Autowired
    private CertAccessLogService accessLogService;

    public Map<String, Object> getCertificateByIdNumber(CertificateExternalQueryDto queryDto, Integer pageSize,
            Integer pageNumber) {
        log.info("海事通APP列表查询，参数：{}", queryDto);
        Date date = new Date();

        // 创建一个新的查询条件用于计算总记录数
        QueryWrapper<Certificate> totalRecordsWrapper = Wrappers.<Certificate>query();
        totalRecordsWrapper.lambda()
                .eq(Certificate::getCreateStatus, CertificateCreateStatus.CREATED.getCode())
                .eq(StrUtil.isNotBlank(queryDto.getHolderIdentityNumber()), Certificate::getHolderIdentityNumber,
                        queryDto.getHolderIdentityNumber());

        // 计算符合 holderIdentityNumber 限制的总记录数
        long totalRecords = this.count(totalRecordsWrapper); // 使用 count 获取总记录数

        // 查询证照类型
        List<String> certTypes = certificateTypeService.list(
                Wrappers.<CertificateType>lambdaQuery()
                        .in(queryDto.getCertTypes() != null && !queryDto.getCertTypes().isEmpty(),
                                CertificateType::getCertTypeDirId,
                                queryDto.getCertTypes()))
                .stream() // 转换为Stream
                .map(CertificateType::getCertificateName) // 映射为certificateName
                .collect(Collectors.toList()); // 收集为List<String>

        // 优化后代码
        if (CollUtil.isNotEmpty(queryDto.getCertTypes()) && CollUtil.isEmpty(certTypes)) {
            log.info("证照类型查询条件数据为空");
            // 当证照类型查询条件数据为空时，直接返回空数据结构
            Map<String, Object> emptyResponse = new HashMap<>();
            emptyResponse.put("records", new ArrayList<CertificateAppSearchVo>());
            emptyResponse.put("total", 0L);
            emptyResponse.put("size", pageSize);
            emptyResponse.put("current", pageNumber);
            emptyResponse.put("orders", "");
            emptyResponse.put("searchCount", true);
            emptyResponse.put("pages", 0);
            emptyResponse.put("validCount", 0L);
            emptyResponse.put("invalidCount", 0L);
            return emptyResponse;
        }

        // 处理船舶名称集合（逗号分隔字符串转List）
        List<String> shipNamesList = new ArrayList<>();
        String shipNames = queryDto.getShipNames();
        if (StrUtil.isNotBlank(shipNames)) {
            shipNamesList = Arrays.asList(shipNames.split(","));
        }
        // 将shipNamesList声明为最终变量
        final List<String> finalShipNamesList = shipNamesList;

        // 构建查询条件
        QueryWrapper<Certificate> queryWrapper = Wrappers.<Certificate>query();
        LambdaQueryWrapper<Certificate> lambda = queryWrapper.lambda();
        lambda.eq(Certificate::getCreateStatus, CertificateCreateStatus.CREATED.getCode());

        // 处理身份号码和船舶名称的条件
        if (StrUtil.isNotBlank(queryDto.getHolderIdentityNumber()) ||
                (finalShipNamesList != null && !finalShipNamesList.isEmpty())) {
            lambda.and(wrapper -> {
                if (StrUtil.isNotBlank(queryDto.getHolderIdentityNumber())) {
                    wrapper.eq(Certificate::getHolderIdentityNumber, queryDto.getHolderIdentityNumber());
                }
                if (finalShipNamesList != null && !finalShipNamesList.isEmpty()) {
                    if (StrUtil.isNotBlank(queryDto.getHolderIdentityNumber())) {
                        wrapper.or();
                    }
                    wrapper.in(Certificate::getShipName, finalShipNamesList);
                }
            });
        }

        // 添加其他查询条件
        if (queryDto.getCertificateNames() != null && !queryDto.getCertificateNames().isEmpty()) {
            lambda.in(Certificate::getCertificateName, queryDto.getCertificateNames());
        }
        if (StrUtil.isNotBlank(queryDto.getShipImo())) {
            lambda.eq(Certificate::getShipImo, queryDto.getShipImo());
        }
        if (StrUtil.isNotBlank(queryDto.getShipCallSign())) {
            lambda.eq(Certificate::getShipCallSign, queryDto.getShipCallSign());
        }
        if (queryDto.getIssuOrgCode3() != null && !queryDto.getIssuOrgCode3().isEmpty()) {
            lambda.in(Certificate::getIssuOrgCode3, queryDto.getIssuOrgCode3());
        }

        // 修改船舶名称查询逻辑，同时支持中文名和英文名
        if (StrUtil.isNotBlank(queryDto.getShipName())) {
            lambda.and(i -> i.eq(Certificate::getShipName, queryDto.getShipName())
                    .or()
                    .eq(Certificate::getShipNameEn, queryDto.getShipName()));
        }

        // 创建有效证书查询条件

        QueryWrapper<Certificate> validQueryWrapper = Wrappers.<Certificate>query();
        LambdaQueryWrapper<Certificate> validLambda = validQueryWrapper.lambda();
        validLambda.eq(Certificate::getCreateStatus, CertificateCreateStatus.CREATED.getCode()) // create_status = ?
                .ge(Certificate::getExpireDate, date); // expire_date >= 当前日期

        // 添加身份号码和船舶名称的复杂条件
        if (StrUtil.isNotBlank(queryDto.getHolderIdentityNumber()) ||
                (finalShipNamesList != null && !finalShipNamesList.isEmpty())) {
            validLambda.and(wrapper -> {
                if (StrUtil.isNotBlank(queryDto.getHolderIdentityNumber())) {
                    wrapper.eq(Certificate::getHolderIdentityNumber, queryDto.getHolderIdentityNumber()); // holder_identity_number
                                                                                                          // = ?
                }
                if (finalShipNamesList != null && !finalShipNamesList.isEmpty()) {
                    if (StrUtil.isNotBlank(queryDto.getHolderIdentityNumber())) {
                        wrapper.or(); // OR
                    }
                    wrapper.in(Certificate::getShipName, finalShipNamesList) // ship_name IN (?)
                            .ge(Certificate::getExpireDate, date); // AND expire_date >= 当前日期
                }
            });
        }

        // 添加其他查询条件
        if (queryDto.getCertificateNames() != null && !queryDto.getCertificateNames().isEmpty()) {
            validLambda.in(Certificate::getCertificateName, queryDto.getCertificateNames()); // certificate_name IN (?)
        }
        if (StrUtil.isNotBlank(queryDto.getShipImo())) {
            validLambda.eq(Certificate::getShipImo, queryDto.getShipImo());
        }
        if (StrUtil.isNotBlank(queryDto.getShipCallSign())) {
            validLambda.eq(Certificate::getShipCallSign, queryDto.getShipCallSign());
        }
        if (queryDto.getIssuOrgCode3() != null && !queryDto.getIssuOrgCode3().isEmpty()) {
            validLambda.in(Certificate::getIssuOrgCode3, queryDto.getIssuOrgCode3());
        }

        // 处理船舶名称查询逻辑，同时支持中文名和英文名
        if (StrUtil.isNotBlank(queryDto.getShipName())) {
            validLambda.and(i -> i.eq(Certificate::getShipName, queryDto.getShipName())
                    .or()
                    .eq(Certificate::getShipNameEn, queryDto.getShipName()));
        }

        // 处理有效期范围查询
        if (StrUtil.isNotBlank(queryDto.getCertStartDate()) && StrUtil.isNotBlank(queryDto.getCertEndDate())) {
            Date certStart = DateUtil.parseDate(queryDto.getCertStartDate());
            Date certEnd = DateUtil.endOfDay(DateUtil.parseDate(queryDto.getCertEndDate()));
            validLambda.between(Certificate::getExpireDate, certStart, certEnd);
        }

        // 创建无效证书查询条件
        QueryWrapper<Certificate> invalidQueryWrapper = Wrappers.<Certificate>query();
        LambdaQueryWrapper<Certificate> invalidLambda = invalidQueryWrapper.lambda();
        invalidLambda.eq(Certificate::getCreateStatus, CertificateCreateStatus.CREATED.getCode()) // create_status = ?
                .lt(Certificate::getExpireDate, date); // expire_date < 当前日期

        // 添加身份号码和船舶名称的复杂条件
        if (StrUtil.isNotBlank(queryDto.getHolderIdentityNumber()) ||
                (finalShipNamesList != null && !finalShipNamesList.isEmpty())) {
            invalidLambda.and(wrapper -> {
                if (StrUtil.isNotBlank(queryDto.getHolderIdentityNumber())) {
                    wrapper.eq(Certificate::getHolderIdentityNumber, queryDto.getHolderIdentityNumber()); // holder_identity_number
                                                                                                          // = ?
                }
                if (finalShipNamesList != null && !finalShipNamesList.isEmpty()) {
                    if (StrUtil.isNotBlank(queryDto.getHolderIdentityNumber())) {
                        wrapper.or(); // OR
                    }
                    wrapper.in(Certificate::getShipName, finalShipNamesList) // ship_name IN (?)
                            .lt(Certificate::getExpireDate, date); // AND expire_date < 当前日期
                }
            });
        }

        // 添加其他查询条件
        if (queryDto.getCertificateNames() != null && !queryDto.getCertificateNames().isEmpty()) {
            invalidLambda.in(Certificate::getCertificateName, queryDto.getCertificateNames()); // certificate_name IN
                                                                                               // (?)
        }
        if (StrUtil.isNotBlank(queryDto.getShipImo())) {
            invalidLambda.eq(Certificate::getShipImo, queryDto.getShipImo());
        }
        if (StrUtil.isNotBlank(queryDto.getShipCallSign())) {
            invalidLambda.eq(Certificate::getShipCallSign, queryDto.getShipCallSign());
        }
        if (queryDto.getIssuOrgCode3() != null && !queryDto.getIssuOrgCode3().isEmpty()) {
            invalidLambda.in(Certificate::getIssuOrgCode3, queryDto.getIssuOrgCode3());
        }

        // 处理船舶名称查询逻辑，同时支持中文名和英文名
        if (StrUtil.isNotBlank(queryDto.getShipName())) {
            invalidLambda.and(i -> i.eq(Certificate::getShipName, queryDto.getShipName())
                    .or()
                    .eq(Certificate::getShipNameEn, queryDto.getShipName()));
        }

        // 处理有效期范围查询
        if (StrUtil.isNotBlank(queryDto.getCertStartDate()) && StrUtil.isNotBlank(queryDto.getCertEndDate())) {
            Date certStart = DateUtil.parseDate(queryDto.getCertStartDate());
            Date certEnd = DateUtil.endOfDay(DateUtil.parseDate(queryDto.getCertEndDate()));
            invalidLambda.between(Certificate::getExpireDate, certStart, certEnd);
        }

        // 处理船舶名称集合
//        if (finalShipNamesList != null && !finalShipNamesList.isEmpty()) {
//            invalidLambda.or(wrapper -> wrapper.in(Certificate::getShipName, finalShipNamesList)
//                    .lt(Certificate::getExpireDate, date));
//        }

        // 根据搜索条件中的状态标志来决定使用哪个计数
        long validCount, invalidCount;
        if (StrUtil.equals("1", queryDto.getStatusFlag())) {
            validCount = this.count(validQueryWrapper);
            invalidCount = 0;
            // 设置查询条件为有效
            queryWrapper.lambda().ge(Certificate::getExpireDate, date);
        } else if (StrUtil.equals("2", queryDto.getStatusFlag())) {
            validCount = 0;
            invalidCount = this.count(invalidQueryWrapper);
            // 设置查询条件为无效
            queryWrapper.lambda().lt(Certificate::getExpireDate, date);
        } else {
            validCount = this.count(validQueryWrapper);
            invalidCount = this.count(invalidQueryWrapper);
        }
        log.info("有效、无效数量SQL执行了");

        // 如果
        // queryDto.getCertStartDate(),queryDto.getCertEndDate()都不为空，则按照证照有效期开始时间和结束时间进行查询
        if (StrUtil.isNotBlank(queryDto.getCertStartDate()) && StrUtil.isNotBlank(queryDto.getCertEndDate())) {
            Date certStart = DateUtil.parseDate(queryDto.getCertStartDate());
            Date certEnd = DateUtil.endOfDay(DateUtil.parseDate(queryDto.getCertEndDate())); // 包含结束日期的最后一刻
            queryWrapper.lambda()
                    .between(Certificate::getExpireDate, certStart, certEnd);
        }

        // 获取当前查询条件下的总记录数
        long queryTotalRecords = this.count(queryWrapper);

        // 分页查询
        Page<Certificate> page = new Page<>(pageNumber, pageSize);
        IPage<Certificate> certificatePage = this.page(page, queryWrapper);
        List<CertificateAppSearchVo> certificateAppSearchVoList = new ArrayList<>();
        certificatePage.getRecords().forEach(certificate -> {
            CertificateAppSearchVo certificateAppSearchVo = new CertificateAppSearchVo();
            BeanUtils.copyProperties(certificate, certificateAppSearchVo);

            // 确保设置certificateId
            certificateAppSearchVo.setCertificateId(certificate.getCertificateId());

            // 确定有效性
            Date expireDate = certificateAppSearchVo.getExpireDate();
            Date issueDate = certificateAppSearchVo.getIssueDate();
            int end = DateUtil.compare(expireDate, date);
            int start = DateUtil.compare(issueDate, date);

            String statusFlag = certificate.getStatusFlag();

            // 设置状态
            String status = end > 0 && start < 0 ? "有效" : "无效";
            String statusEn = end > 0 && start < 0 ? "Effective" : "Invalid";

            // 如果状态标志为-2 已注销，则设置状态为无效
            if (StrUtil.equals("-2", statusFlag)) {
                status = "无效";
                statusEn = "Invalid";
            }

            certificateAppSearchVo.setStatus(status);
            certificateAppSearchVo.setStatusEn(statusEn);

            // 根据issuOrgNameCn查询对应的英文名称
            if (StrUtil.isNotBlank(certificateAppSearchVo.getIssuOrgNameCn())) {
                SysDeptEn sysDeptEn = sysDeptEnService.getOne(new LambdaQueryWrapper<SysDeptEn>()
                        .eq(SysDeptEn::getOrgName, certificateAppSearchVo.getIssuOrgNameCn()));
                if (sysDeptEn != null) {
                    certificateAppSearchVo.setIssuOrgNameEn(sysDeptEn.getOrgNameEn());
                }
            }

            // 设置证书名称英文
            CertificateName certificateName = CertificateName
                    .getCertificateByName(certificateAppSearchVo.getCertificateName());
            if (certificateName != null) {
                certificateAppSearchVo.setCertificateNameEn(certificateName.getNameEn());
            }

            // 根据 certificateType 设置证书名称和清空字段
            if ("油污损害民事责任保险或其他财务保证证书（国际）".equals(certificate.getCertificateType()) ||
                    "燃油污染损害民事责任保险或其他财务保证证书（国际）".equals(certificate.getCertificateType()) ||
                    "残骸清除责任保险或其他财务保证证书".equals(certificate.getCertificateType())) {
                // 中英文证书
                certificateAppSearchVo.setCertificateType("2"); // 设置为中英文证书
                // 船舶国籍证书设置英文
            } else if (CertificateTypeCode.SHIP_NATIONALITY_CN_EN.getTypeCode()
                    .equals(certificate.getCertificateTypeCode()) ||
                    CertificateTypeCode.SHIP_NATIONALITY_PROV_CN_EN.getTypeCode()
                            .equals(certificate.getCertificateTypeCode())) {
                // 中英文证书
                certificateAppSearchVo.setCertificateType("2"); // 设置为中英文证书
            } else {
                // 中文证书
                certificateAppSearchVo.setCertificateType("1"); // 设置为中文证书
                // 清空字段逻辑
                certificateAppSearchVo.setCertificateNameEn(null);
                certificateAppSearchVo.setIssuOrgNameEn(null);
                certificateAppSearchVo.setShipNameEn(null);
                certificateAppSearchVo.setStatusEn(null);
            }

            certificateAppSearchVoList.add(certificateAppSearchVo);
        });

        // 创建响应结构
        Map<String, Object> response = new HashMap<>();
        response.put("records", certificateAppSearchVoList);
        response.put("total", queryTotalRecords); // 使用总记录数
        response.put("size", pageSize);
        response.put("current", pageNumber);
        response.put("orders", "");
        response.put("searchCount", true);
        response.put("pages", (int) Math.ceil((double) queryTotalRecords / pageSize)); // 使用总记录数计算总页数
        response.put("validCount", validCount);
        response.put("invalidCount", invalidCount);
        // 统计日志
        if (!certificateAppSearchVoList.isEmpty()) {
            accessLogService.certOperationLog(
                    "1", "2", "1", "", certificateAppSearchVoList.get(0).getCertificateId());
        }
        return response; // 返回结果
    }

    private long getValidCertificateCount(String holderIdentityNumber) {
        Date date = new Date();
        return this.count(Wrappers.<Certificate>query().lambda()
                .eq(Certificate::getCreateStatus, CertificateCreateStatus.CREATED.getCode())
                .gt(Certificate::getExpireDate, date) // 过期日期大于当前日期
                .lt(Certificate::getEffectDate, date) // 生效日期小于当前日期
                .eq(Certificate::getHolderIdentityNumber, holderIdentityNumber)); // 添加 holderIdentityNumber 限制
    }

    public String download(String certificateId) {
        // 查看电子证照信息url地址
        String fileUrl = this.getPermitDetail(certificateId);
        if (StrUtil.isBlank(fileUrl)) {
            return null;
        }

        CertificateDownloadVo certificateDownloadVo = new CertificateDownloadVo();
        certificateDownloadVo.setFileUrl(fileUrl);
        certificateDownloadVo.setFileName(IdUtil.fastUUID() + ".jpg");
        certificateDownloadVo.setPictureType("jpg");
        // 转为jpg
        return this.download(certificateDownloadVo);
    }

    /**
     * 请求电子证照下载jpg方法
     *
     * @param certificateDownloadVo 请求参数
     * @return
     */
    public String download(CertificateDownloadVo certificateDownloadVo) {
        String sb = "AccountId=" + urlConfig.getAccountId() +
                "&AccessToken=" + urlConfig.getAccessToken() +
                "&FileName=" + certificateDownloadVo.getFileName() +
                "&FileUrl=" + certificateDownloadVo.getFileUrl() +
                "&PictureType=" + certificateDownloadVo.getPictureType() +
                "&UseOFD=" + certificateDownloadVo.getUseOFD();
        String result = HttpRequestUtil
                .sendPost(urlConfig.getUrlPermitHost() + Common.PIC_FILE_QUERY_INFO_BY_CERTIFICATE_ID, sb, false);
        log.info("电子证照转jpg:{}", result);
        JSONObject jsonObject = JSONObject.parseObject(result);
        if (jsonObject == null) {
            return null;
        }
        return jsonObject.getString("PictureUrl");
    }

    public String getPermitDetail(String certificateId) {
        if (searchZf) {
            // 调用正孚接口获取文件路径
            log.info("【证书下载】使用正孚接口查询，证书ID: {}", certificateId);
            String filePath = CertificateDataUtil.getFilePathByCertificateId(zfUrl, certificateId);
            String fileUrl = downloadPhotoUrl + filePath;
            if (StrUtil.isBlank(fileUrl)) {
                log.error("【证书下载】正孚接口查询失败，证书ID: {}", certificateId);
                return null;
            }
            log.info("【证书下载】正孚接口查询成功，文件路径: {}", fileUrl);
            return fileUrl;
        }
        String sb = "AccountId=" + urlConfig.getAccountId() +
                "&AccessToken=" + urlConfig.getAccessToken() +
                "&CertificateID=" + certificateId +
                "&UseFor=海事电子政务服务用户查询电子证照详细&FromCountry=" + false;
        String result = HttpRequestUtil
                .sendPost(urlConfig.getUrlPermitHost() + Common.FILE_QUERY_INFO_BY_CERTIFICATE_ID, sb, false);
        log.info("电子证照详细:" + result);
        Map<String, Object> resultMap = JSONObject.parseObject(result, Map.class);
        try {
            List<Map<String, Object>> list = this.parseResultMap(resultMap);
            if (CollUtil.isEmpty(list)) {
                return null;
            }
            Map<String, Object> map = list.get(0);
            return (String) map.get("fileUrl");
        } catch (Exception e) {
            log.error("电子证照详细解析失败:{}", e.getMessage());
            return null;
        }
    }

    private List<Map<String, Object>> parseResultMap(Map<String, Object> resultMap) {
        Map<String, Object> dataMap = (Map<String, Object>) resultMap.get("data");
        Map<String, Object> headMap = (Map<String, Object>) resultMap.get("head");
        List<Map<String, Object>> dataList = (List<Map<String, Object>>) dataMap.get("dataList");
        if (CollUtil.isEmpty(dataList)) {
            log.error(String.valueOf(headMap.get("message")));
            return null;
        }
        return dataList;
    }

    public void createDebrisCert() {
        // 查询标准层办件信息表 残骸事项 未清洗数据
        List<BizAffairApply> bizAffairApplyList = bizAffairApplyService.getApplyByModelType(ModelType.MSAQR013,
                UN_CREATE);
        if (CollUtil.isEmpty(bizAffairApplyList)) {
            return;
        }
        bizAffairApplyList.forEach(bizAffairApply -> {
            boolean certificate = certificateDateConfig.createCertificate(bizAffairApply);
            log.info("【残骸事项】办件：{}，电子证照信息生成任务执行结果：{}", bizAffairApply.getApplyId(), certificate);
            if (certificate) {
                String affairApplyCleanStatus = certificate ? CREATED.getCode() : CREATE_FAIL.getCode();
                bizAffairApply.setCleanStatus(affairApplyCleanStatus);
                bizAffairApplyService.updateById(bizAffairApply);
            }
        });
    }

    public Certificate getCertificateByApplyId(String applyId) {
        QueryWrapper<Certificate> queryWrapper = Wrappers.<Certificate>query();
        queryWrapper.lambda()
                .eq(Certificate::getApplyId, applyId).last("LIMIT 1");
        return baseMapper.selectOne(queryWrapper);
    }

    public List<Certificate> getCertificateListByCertificateCreateStatus(
            CertificateCreateStatus certificateCreateStatus) {
        QueryWrapper<Certificate> queryWrapper = Wrappers.<Certificate>query();
        queryWrapper.lambda()
                .eq(Certificate::getCreateStatus, certificateCreateStatus.getCode());
        queryWrapper.last("LIMIT 10");
        return this.list(queryWrapper);
    }

    public void createCertificate(Certificate certificate) {
        // 获取电子证照照面信息
        List<CertificateAttribute> certificateAttributeList = certificateAttributeService
                .getCertificateAttributeListByCertificateId(certificate.getId());
        if (CollUtil.isEmpty(certificateAttributeList)) {
            log.info("【电子证照生成】办件ID：{}，未查询到电子证照照面信息", certificate.getApplyId());
            return;
        }
        // 获取证书校核意见表
        BizEgCertCheck bizEgCertCheck = bizEgCertCheckService.getCertCheckByApplyId(certificate.getApplyId());
        // 系统自动生成的证照需要校核表校验 外部推送的不需要
        if (bizEgCertCheck == null && StrUtil.equals(certificate.getDataType(), "1")) {
            log.error("【电子证照生成】办件ID：{}，未查询到标准层证书校核意见表", certificate.getApplyId());
            return;
        }
        // 封装电子证照请求参数
        CertificateData certificateData = CertificateUtil.createCertificateData(certificate, certificateAttributeList);
        // 编号使用说明
        String remark = Optional.ofNullable(bizEgCertCheck)
                .map(BizEgCertCheck::getRemark)
                .orElse("");
        String useReason = StrUtil.isNotBlank(remark) ? remark
                : CertificateInfo.getCertificateUseReason(certificate.getCertificateName());
        certificateData.setUseReason(useReason);
        // 生成二维码url（加密id）
        try {
            String certId = certificate.getCertificateTypeCode() + "_" + certificate.getId();
            String encryptedId = Sm4Tool.encrypt(certId, "5d3b282609644b4f8992b31a2e92f0f3");
            // URL编码
            String para = URLEncoder.encode(encryptedId, StandardCharsets.UTF_8.toString());
            String qrCode = scanUrl + "?id=" + para;
            // certificateData.setQrCode(qrCode);
            // surfaceList添加 number、qrCodeUrl
            Surface surfaceNumber = new Surface();
            surfaceNumber.setName("编号");
            surfaceNumber.setValue(certificate.getCertificateNum());
            surfaceNumber.setValueType("string");
            surfaceNumber.setColumnName("number");
            certificateData.getSurface().add(surfaceNumber);

            Surface surfaceQrCodeUrl = new Surface();
            surfaceQrCodeUrl.setName("二维码");
            surfaceQrCodeUrl.setValue(qrCode);
            surfaceQrCodeUrl.setValueType("string");
            surfaceQrCodeUrl.setColumnName("qrCodeUrl");
            certificateData.getSurface().add(surfaceQrCodeUrl);

        } catch (Exception e) {
            log.error("【电子证照生成】办件ID：{}，加密失败", certificate.getApplyId());
            return;
        }
        // 调用电子证照生成接口
        String url = StrUtil.isBlank(certificateData.getCertificateNumber()) ? urlConfig.getChromatographyPrintingUrl()
                : urlConfig.getCheckChromatographyPrintingUrl();
        String certificateId = certificateUtil.getCertificate(certificateData, url, null, null);
        if (StrUtil.isBlank(certificateId)) {
            // 生成失败
            log.error("【电子证照生成】办件ID：{}，电子证照生成失败", certificate.getApplyId());
            this.upCertificateStatus(certificate, CertificateCreateStatus.CREATE_FAIL);
            return;
        }
        // 更新电子证照信息表
        certificate.setCertificateId(certificateId);
        boolean update = this.upCertificateStatus(certificate, CertificateCreateStatus.CREATED);
        // if (update) {
        // // 更新证书校核意见表
        // bizEgCertCheck.setCertId(certificateId);
        // bizEgCertCheckService.updateById(bizEgCertCheck);
        // }
        log.info("【电子证照生成】办件ID：{}，电子证照生成成功", certificate.getApplyId());
    }

    public static void main(String[] args) {
        String certId = "0117_0903002022020019";
        String encryptedId = Sm4Tool.encrypt(certId, "5d3b282609644b4f8992b31a2e92f0f3");
        // URL编码
        String para = encryptedId;
        // try {
        // para = URLEncoder.encode(encryptedId, StandardCharsets.UTF_8.toString());
        // } catch (UnsupportedEncodingException e) {
        // e.printStackTrace();
        // }
        System.out.println(para);
    }

    public boolean upCertificateStatus(Certificate certificate, CertificateCreateStatus certificateCreateStatus) {
        certificate.setCreateStatus(certificateCreateStatus.getCode());
        certificate.setModifyDate(new Date());
        return this.updateById(certificate);
    }

    /**
     * 残骸生成电子证照照面信息
     *
     * @param debrisCertDto 残骸电子证照信息
     * @return 证照主表ID
     */
    @Transactional(rollbackFor = Exception.class)
    public String createCertificate(DebrisCertDto debrisCertDto) {
        return this.createCertificate(debrisCertDto, DEBRIS);
    }

    /**
     * 油污生成电子证照照面信息
     *
     * @param oilDamageDto 油污电子证照信息
     * @return 证照主表ID
     */
    @Transactional(rollbackFor = Exception.class)
    public String createCertificate(OilDamageDto oilDamageDto) {
        return this.createCertificate(oilDamageDto, OIL_DAMAGE);
    }

    /**
     * 燃油生成电子证照照面信息
     *
     * @param fuelPollutionDto 燃油电子证照信息
     * @return 证照主表ID
     */
    @Transactional(rollbackFor = Exception.class)
    public String createCertificate(FuelPollutionDto fuelPollutionDto) {
        return this.createCertificate(fuelPollutionDto, FUEL_POLLUTION);
    }

    /**
     * 非持久性生成电子证照照面信息
     *
     * @param nonPersistentDto 非持久性电子证照信息
     * @return 证照主表ID
     */
    @Transactional(rollbackFor = Exception.class)
    public String createCertificate(NonPersistentDto nonPersistentDto) {
        return this.createCertificate(nonPersistentDto, NON_PERSISTENT);
    }

    /**
     * 生成电子证照照面信息
     *
     * @param t               电子接口证照信息
     * @param certificateInfo 电子证照类型枚举
     * @param <T>             电子证照信息
     * @return 证照主表ID
     */
    public <T extends Certificate> String createCertificate(T t, CertificateInfo certificateInfo) {
        // 获取证照属性
        if (certificateInfo == null) {
            log.error("【电子证照生成】类型:{},办件ID:{},未查询到证照信息表", t.getCertificateName(), t.getApplyId());
            return null;
        }

        // 查询受理信息
        BizEgAccept bizEgAccept = bizEgAcceptService.getByApplyId(t.getApplyId());
        if (bizEgAccept != null) {
            t.setAcceptDate(bizEgAccept.getCreateDate());
            t.setAcceptOrgCode2(dictYthOrgMappingService.getSrcOrgCode(bizEgAccept.getMsaOrgCode()));
            t.setAcceptOrgCode3(bizEgAccept.getMsaOrgCode());
        }

        // 保存证照信息
        Certificate certificate = new Certificate();
        BeanUtils.copyProperties(t, certificate);
        certificate.setCreateStatus(UN_CREATE.getCode());
        certificate.setStatusFlag("1");
        boolean save = this.save(certificate);
        if (!save) {
            log.error("【电子证照生成】类型:{},办件ID:{}，保存电子证照信息表失败", certificate.getCertificateName(), certificate.getApplyId());
            return null;
        }
        CertificateInfoBuilder certificateInfoBuilder = certificateInfo.getCertificateInfoBuilder();
        if (certificateInfoBuilder == null) {
            log.error("【电子证照生成】类型:{},办件ID:{}，构建电子证照照面信息失败", certificate.getCertificateName(), certificate.getApplyId());
            return null;
        }
        // 获取电子证照属性
        List<CertificateAttribute> certificateAttributeList = certificateInfoBuilder
                .buildCertificateAttribute(t.getClass());
        certificateAttributeList
                .forEach(certificateAttribute -> certificateAttribute.setCertificateId(certificate.getId()));
        // 保存证照属性
        certificateAttributeService.saveBatch(certificateAttributeList);
        return certificate.getId();
    }

    public String getCertificateId(String id) {
        QueryWrapper<Certificate> queryWrapper = Wrappers.<Certificate>query();
        queryWrapper.lambda().eq(Certificate::getId, id)
                .eq(Certificate::getCreateStatus, CertificateCreateStatus.CREATED.getCode())
                .isNotNull(Certificate::getCertificateId)
                .last("LIMIT 1");
        Certificate certificate = this.getOne(queryWrapper);
        if (certificate == null) {
            return "";
        }
        return certificate.getCertificateId();
    }

    /**
     * 外网查询
     *
     * @param externalNetworkQueryDto 查询参数
     * @param page                    分页参数
     * @return 查询结果
     */
    public Result<IPage<certificateExternalNetworkQueryVo>> certificateExternalNetworkQuery(
            ExternalNetworkQueryDto externalNetworkQueryDto, Page page) {
        log.info("海事一网通办列表查询，参数：{}", externalNetworkQueryDto);
        StringBuilder stringBuilder = new StringBuilder();
        if (StrUtil.isNotBlank(externalNetworkQueryDto.getCertificateNum())) {
            String certificateNum = externalNetworkQueryDto.getCertificateNum().replaceAll(" ", "");
            char[] chars = certificateNum.toCharArray();
            for (int i = 0; i < chars.length; i++) {// 判断是否是英文
                if (i > 1 && (chars[i - 1] + "").matches("^[a-zA-Z\\s,.!?;:'\"-]+$")
                        && (chars[i] + "").matches("^\\d+$")) {// 判断是否是数字
                    stringBuilder.append(" ").append(chars[i]);
                } else {
                    stringBuilder.append(chars[i]);
                }
            }
            externalNetworkQueryDto.setCertificateNum(stringBuilder.toString());
        }

        log.info("外网查询接口certificateExternalNetworkQuery，请求参数externalNetworkQueryDto:{}", externalNetworkQueryDto);
        String id = externalNetworkQueryDto.getCertificateId();
        if (StringUtils.isNotBlank(id)) {// 扫码查询
            externalNetworkQueryDto = new ExternalNetworkQueryDto();
            externalNetworkQueryDto.setId(id);
        } else {
            if (!verifyCodeUtil.check(externalNetworkQueryDto.getCaptchaId(), externalNetworkQueryDto.getCode())) {
                return Result.failed(205, "验证码错误");
            }
        }
        IPage<certificateExternalNetworkQueryVo> certificateExternalNetworkQueryVoIPage = certificateMapper
                .certificateExternalNetworkQuery(page, externalNetworkQueryDto);
        certificateExternalNetworkQueryVoIPage.getRecords().forEach(externalNetworkQueryVo -> {
            Date issueDate = externalNetworkQueryVo.getIssueDate();
            Date expireDate = externalNetworkQueryVo.getExpireDate();
            String statusFlag = externalNetworkQueryVo.getStatusFlag();
            Date date = new Date();
            if (date.after(issueDate) && date.before(expireDate)) {
                externalNetworkQueryVo.setStatusFlag("1");
                externalNetworkQueryVo.setNoticeContent("正常");
            } else {
                externalNetworkQueryVo.setStatusFlag("2");
                externalNetworkQueryVo.setNoticeContent("该证照已过期，证照状态为无效");
            }
            // 状态为注销：-2时，显示无效
            if (StrUtil.equals(statusFlag, "-2")) {
                externalNetworkQueryVo.setStatusFlag("2"); // 无效
                externalNetworkQueryVo.setNoticeContent("该证照已注销，证照状态为无效");
            }

            // 获取证照类型 如果是国籍中文证书，去掉机构英文
            // CertificateType certificateType = certificateTypeService.getOne(new
            // QueryWrapper<CertificateType>().lambda()
            // .eq(StrUtil.isNotBlank(externalNetworkQueryVo.getCertificateTypeCode()),CertificateType::getCertificateTypeCode,externalNetworkQueryVo.getCertificateTypeCode()));
            // log.info("证照类型：{},传的类型code{}",certificateType,externalNetworkQueryVo.getCertificateTypeCode());
            // if(StrUtil.contains(certificateType.getCertificateName(), "_中文")){
            // externalNetworkQueryVo.setIssuOrgNameEn("");
            // }
        });
        return Result.success(certificateExternalNetworkQueryVoIPage);
    }

    public IPage<CertificateIntranetQueryQueryVo> certificateIntranetQueryQuery(
            IntranetQueryQueryDto intranetQueryQueryDto, Page page) {
        log.info("智慧海事列表查询，参数：{}", intranetQueryQueryDto);

        // 校验证照类型代码是否为空
        // if (StrUtil.isBlank(intranetQueryQueryDto.getCertificateType())) {
        // log.error("证照类型不能为空");
        // throw new RuntimeException("证照类型不能为空");
        // }

        // 处理证书编号格式 - 14RYG 0018460 中间加空格
        if (StrUtil.isNotBlank(intranetQueryQueryDto.getCertificateNum())) {
            String certificateNum = intranetQueryQueryDto.getCertificateNum().replaceAll(" ", "");
            char[] chars = certificateNum.toCharArray();
            StringBuilder stringBuilder = new StringBuilder();
            for (int i = 0; i < chars.length; i++) {// 判断是否是英文
                if (i > 1 && (chars[i - 1] + "").matches("^[a-zA-Z\\s,.!?;:'\"-]+$")
                        && (chars[i] + "").matches("^\\d+$")) {// 判断是否是数字
                    stringBuilder.append(" ").append(chars[i]);
                } else {
                    stringBuilder.append(chars[i]);
                }
            }
            intranetQueryQueryDto.setCertificateNum(stringBuilder.toString());
        }

        // 记录查询参数
        if (StrUtil.isNotBlank(intranetQueryQueryDto.getCertPrintNo())) {
            log.info("按证书印刷号查询：{}", intranetQueryQueryDto.getCertPrintNo());
        }

        if (StrUtil.isNotBlank(intranetQueryQueryDto.getHolderIdentityNumber())) {
            log.info("按持有人身份标识号码查询：{}", intranetQueryQueryDto.getHolderIdentityNumber());
        }

        log.info("内网查询接口certificateIntranetQueryQuery，请求参数intranetQueryQueryDto:{}", intranetQueryQueryDto);

        try {
            // 执行查询
            IPage<CertificateIntranetQueryQueryVo> certificateIntranetQueryQueryVoIPage = certificateMapper
                    .certificateIntranetQueryQuery(page, intranetQueryQueryDto);

            // 处理证书状态
            certificateIntranetQueryQueryVoIPage.getRecords().forEach(intranetQueryQueryVo -> {
                Date issueDate = intranetQueryQueryVo.getIssueDate();
                Date expireDate = intranetQueryQueryVo.getExpireDate();
                Date date = new Date();
                if (date.after(issueDate) && date.before(expireDate)
                        && !"-2".equals(intranetQueryQueryVo.getStatusFlag())) {
                    intranetQueryQueryVo.setStatusFlag("有效"); // 有效
                } else {
                    intranetQueryQueryVo.setStatusFlag("无效"); // 无效
                }
            });

            return certificateIntranetQueryQueryVoIPage;
        } catch (Exception e) {
            log.error("内网查询执行异常：", e);
            throw new RuntimeException("查询失败：" + e.getMessage());
        }
    }

    /**
     * 内网查询接口（调智慧海事接口鉴权）
     *
     * @param queryDto
     * @param page
     * @return
     */
    public IPage<CertificateIntranetQueryQueryVo> certificateIntranetQueryQueryAuth(IntranetQueryQueryDto queryDto,
            Page page) {
        // 添加参数校验
        if (queryDto == null) {
            log.error("查询参数不能为空");
            throw new RuntimeException("查询参数不能为空");
        }
        log.info("Request parameters - intranetQueryQueryDto: {}, onlineDepOrg: {}",
                queryDto, queryDto.getOnlineDepOrg());

        // 1. 验证必填参数
        if (StrUtil.isEmpty(queryDto.getOnlineDepOrg())) {
            log.error("用户所属部门参数onlineDepOrg为空");
            throw new RuntimeException("登录会话超时");
        }

        // 2. 验证登录状态
        if (!validateZhhsLogin(queryDto.getZhhsCode())) {
            throw new RuntimeException("登录会话超时");
        }

        // 处理orgCode
        String orgCode = queryDto.getOnlineDepOrg();
        String processedOrgCode = ZptUtil.processOrgCode(orgCode);
        log.info("原始orgCode: {}, 处理后orgCode: {}", orgCode, processedOrgCode);
        if("01".equals(processedOrgCode)){
            queryDto.setOnlineDepOrg(null);
        }else{
            queryDto.setOnlineDepOrg(processedOrgCode);
        }

        // 3. 执行查询
        return this.certificateIntranetQueryQuery(queryDto, page);
    }

    /**
     * 验证智慧海事登录状态
     */
    private boolean validateZhhsLogin(String zhhsCode) {
        // 本地 开发环境 不进行登录验证
        if (StrUtil.equalsAny(active, "dev", "local")) {
            return true;
        }

        if (StrUtil.isEmpty(zhhsCode)) {
            log.error("zhhsCode参数为空");
            return false;
        }

        try {
            HttpResponse response = HttpRequest.get(loginCheckUrl)
                    .header("Authorization", "Bearer " + zhhsCode)
                    .timeout(10000)
                    .execute();

            if (!response.isOk()) {
                log.error("登录验证接口调用失败, status: {}", response.getStatus());
                return false;
            } else {
                log.info("登录验证接口调用成功, status: {}", response.getStatus());
            }

            String body = response.body();
            JSONObject result = JSONObject.parseObject(body);
            log.info("登录验证接口调用成功, response: {}", result);

            if (result == null || result.getInteger("code") != 200) {
                log.error("登录验证失败, response: {}", body);
                return false;
            }

            return true;
        } catch (Exception e) {
            log.error("登录验证接口调用异常", e);
            return false;
        }
    }

    public List<Map<String, Object>> CertificateVerify(String id) throws IllegalAccessException {
        // 主表
        Certificate certificate = getOne(new QueryWrapper<Certificate>().lambda()
                .eq(Certificate::getId, id)
                .eq(Certificate::getCreateStatus, CertificateCreateStatus.CREATED.getCode())
                .eq(Certificate::getDelFlag, "0")
                .last("LIMIT 1"));
        Map<String, Object> certificateMap = new HashMap<>();
        for (Field field : certificate.getClass().getDeclaredFields()) {
            field.setAccessible(true);
            certificateMap.put(field.getName(), field.get(certificate));
        }
        // 照面表
        List<CertificateAttribute> list = certificateAttributeService
                .list(new LambdaQueryWrapper<CertificateAttribute>()
                        .eq(CertificateAttribute::getCertificateId, id));
        Map<String, Object> attributeMap = new HashMap<>();
        list.forEach(attribute -> attributeMap.put(attribute.getAttributeColumnName(), attribute.getAttributeValue()));
        // 配置表
        String certificateTypeCode = "";
        if (certificate.getCertificateType().equals("燃油污染损害民事责任保险或其他财务保证证书（国内）")) {
            certificateTypeCode = "0101_1";
        } else if (certificate.getCertificateType().equals("油污损害民事责任保险或其他财务保证证书（国内）")) {
            certificateTypeCode = "0100_1";
        } else {
            certificateTypeCode = certificate.getCertificateTypeCode();
        }

        List<VerificationDisplayConfig> configs = verificationDisplayConfigService
                .list(new LambdaQueryWrapper<VerificationDisplayConfig>()
                        .eq(VerificationDisplayConfig::getCertificateTypeCode, certificateTypeCode)
                        .orderBy(true, true, VerificationDisplayConfig::getSortOrder));
        List<Map<String, Object>> mapList = new ArrayList<>();
        // 根据配置表取出要返回前端的数据
        String applyId = certificate.getApplyId();
        configs.forEach(config -> {
            if ("CTF_CERTIFICATE".equals(config.getDataSourceTableName())) {// 主表就取主表数据、否则就是照面表数据
                Map<String, Object> map = getMap(certificateMap, config, applyId);
                String labelName = (String) map.get("label");
                if (StrUtil.equals("证照状态", labelName)) {
                    Map<String, Object> noticeContentMap = new HashMap<>();
                    noticeContentMap.put("label", "提醒内容");
                    String statusFlag = certificate.getStatusFlag();
                    String value = (String) map.get("value");
                    if (StrUtil.equals("有效", value)) {
                        noticeContentMap.put("value", "正常");
                    }
                    // 过期提醒
                    if (StrUtil.equals("无效", value) && !StrUtil.equals("-2", statusFlag)) {
                        noticeContentMap.put("value", "该证照已过期，证照状态为无效");
                    }
                    // 注销状态
                    if (StrUtil.equals("-2", statusFlag)) {
                        map.put("value", "无效");
                        noticeContentMap.put("value", "该证照已注销，证照状态为无效");
                    }
                    mapList.add(noticeContentMap);
                }
                mapList.add(map);
            } else {// 照面表
                /**
                 * 燃油污染损害民事责任保险或其他财务保证证书（国内）、油污损害民事责任保险或其他财务保证证书（国内）
                 * 过滤 颁发或签证官员的姓名，颁发或签证官员的职务 字段
                 */
                if ((certificate.getCertificateType().equals("燃油污染损害民事责任保险或其他财务保证证书（国内）") &&
                        (config.getConfigId().equals("CT2_21") || config.getConfigId().equals("CT2_22")))
                        ||
                        (certificate.getCertificateType().equals("油污损害民事责任保险或其他财务保证证书（国内）") &&
                                (config.getConfigId().equals("CT1_21") || config.getConfigId().equals("CT1_22")))) {
                    log.debug("燃油污染损害民事责任保险或其他财务保证证书（国内） 过滤 颁发或签证官员的姓名，颁发或签证官员的职务 字段");
                } else {
                    mapList.add(getMap(attributeMap, config, applyId));
                }
            }
        });
        return mapList;
    }

    public Map<String, Object> getMap(Map<String, Object> objMap, VerificationDisplayConfig config, String applyId) {
        Map<String, Object> hashMap = new HashMap<>();
        // 取中文
        if (StringUtils.isNotBlank(config.getChineseDataDefaultValue())) {// 有默认值
            hashMap.put("label", config.getChineseLabelName());
            hashMap.put("value", config.getChineseDataDefaultValue());
        } else {// 无默认值则从照面表找
            if ("statusFlag".equals(config.getChineseDataFieldName())) {// 有效值单独处理
                String status;
                Date issueDate = (Date) objMap.get("issueDate");
                Date expireDate = (Date) objMap.get("expireDate");
                log.info("issueDate: {}, expireDate: {}", issueDate, expireDate);
                if (issueDate != null && expireDate != null) {
                    Date date = new Date();
                    if (date.after(issueDate) && date.before(expireDate)) {
                        status = "有效";
                    } else {
                        status = "无效";
                    }
                    hashMap.put("label", config.getChineseLabelName());
                    hashMap.put("value", status);
                }
            } else {
                hashMap.put("label", config.getChineseLabelName());
                hashMap.put("value", objMap.get(config.getChineseDataFieldName()));
            }
        }
        // 取英文
        if (StringUtils.isNotBlank(config.getEnglishDataDefaultValue())) {
            hashMap.put("labelEn", config.getEnglishLabelName());
            hashMap.put("valueEn", config.getEnglishDataDefaultValue());
        } else {
            if ("converPinyin".equals(config.getEnglishDataFieldName())) {
                hashMap.put("labelEn", config.getEnglishLabelName());
                hashMap.put("valueEn",
                        ChineseToPinyin.toPinyin(objMap.get(config.getChineseDataFieldName()).toString()));
            } else if (StringUtils.equalsAny(config.getEnglishDataFieldName(), "shipsAddress2", "address2",
                    "converShipOwner")) {// 船舶所有人名称及其地址 转英文
                if (ObjectUtil.isEmpty(objMap.get(config.getEnglishDataFieldName()))) {
                    // 申请受理审批信息
                    BizEgApplyInfo bizEgApplyInfo = bizEgApplyInfoService.getBizEgApplyInfoByApplyId(applyId);
                    if (bizEgApplyInfo == null) {
                        log.info("bizEgApplyInfo 为空,applyId:{}", applyId);
                        return hashMap;
                    }
                    // 船舶基本信息
                    ShipInfoAll shipInfoAll = shipInfoAllService.getShipInfoByShipId(bizEgApplyInfo.getShipId());
                    if (shipInfoAll == null) {
                        log.info("shipInfoAll 为空,applyId:{}", applyId);
                        return hashMap;
                    }
                    // 船舶证书-所有权证书
                    CertShipOwnership certShipOwnership = certShipOwnershipService.getById(shipInfoAll.getShipRegNo());
                    if (certShipOwnership == null) {
                        log.info("certShipOwnership 为空,applyId:{}", applyId);
                        return hashMap;
                    }
                    // 加空格
                    hashMap.put("labelEn", config.getEnglishLabelName());
                    hashMap.put("valueEn",
                            certShipOwnership.getShipOwnerEn() + " " + certShipOwnership.getShipOwnerAddrEn());
                } else {
                    hashMap.put("labelEn", config.getEnglishLabelName());
                    hashMap.put("valueEn", objMap.get(config.getEnglishDataFieldName()));
                }
            } else if (StringUtils.equalsAny(config.getEnglishDataFieldName(), "issueDept2", "IssuedGovernment2",
                    "converOrg")) {
                if (ObjectUtil.isEmpty(objMap.get(config.getEnglishDataFieldName()))) {
                    // 燃油国内 和 油污国内写照面信息表时，是按照非持久的字段
                    SysDeptEn sysDeptEn = sysDeptEnService.getOne(new LambdaQueryWrapper<SysDeptEn>()
                            .eq(SysDeptEn::getOrgName, objMap.get(config.getChineseDataFieldName())).last("LIMIT 1"));
                    hashMap.put("labelEn", config.getEnglishLabelName());
                    hashMap.put("valueEn", sysDeptEn.getOrgNameEn());
                } else {
                    hashMap.put("labelEn", config.getEnglishLabelName());
                    hashMap.put("valueEn", objMap.get(config.getEnglishDataFieldName()));
                }
            } else {
                Object obj = objMap.get(config.getEnglishDataFieldName());
                if (ObjectUtil.isNotEmpty(obj)) {// 如果找得到就取出来，没有就要手动转换了，例如有些字段是没有英文的，需要手动转。
                    hashMap.put("labelEn", config.getEnglishLabelName());
                    hashMap.put("valueEn", obj);
                } else {// 不需要转换,返回中文值
                    hashMap.put("labelEn", config.getEnglishLabelName());
                    hashMap.put("valueEn", null);// ?
                }
            }
        }
        // 中文证书不展示英文字段了
        if ("statusFlag".equals(config.getChineseDataFieldName())
                && StrUtil.isNotBlank(config.getEnglishLabelName())) {
            if ("有效".equals(hashMap.get("value"))) {
                hashMap.put("valueEn", "Effective");
            } else {
                hashMap.put("valueEn", "Invalid");
            }
        }
        hashMap.put("highlight", config.getHighlight());
        // 日期格式化
        if ("CTF_CERTIFICATE".equals(config.getDataSourceTableName())) {// 主表 去掉时分秒
            hashMap.forEach((k, v) -> {
                if (StringUtils.equalsAny(config.getChineseDataFieldName(), "issueDate", "effectDate", "expireDate",
                        "applyDate", "acceptDate", "apprDate")) {
                    if (v instanceof Date) {
                        hashMap.put(k, new SimpleDateFormat("yyyy-MM-dd").format(v));
                    }
                }
            });
        }
        return hashMap;
    }

    /**
     * app扫码核验
     *
     * @return
     */
    public Result hstQrcodeVerification(String qrcode, String source) throws IllegalAccessException {
        log.info("app扫码核验接口hstQrcodeVerification 请求参数qrcode:{}", qrcode);
        List<Map<String, Object>> certificateVerifyVo = null;
        // 1.验签
        // log.info("传参信息，qrcode:{},source:{},sign:{}", qrcode, source, sign);
        // try {
        // boolean verifySign = Sm2Tool.verifySign(qrcode, sign, publicKeySm2, null);
        // if (verifySign) {
        // log.error("验签不通过.");
        // return ResultUtil.error("验签不通过");
        // }
        // } catch (Exception e) {
        // log.error("验签异常：{}", e.getMessage());
        // e.printStackTrace();
        // return ResultUtil.error("验签失败");
        // }

        // 2.解密二维码信息，获取电子证照id
        String id = null;
        try {
            PrivateKey privateKey = RSAEncryptionUtil.getPrivateKey(privateKeyString);
            String certId = qrcode.substring(qrcode.lastIndexOf("id=") + 3);
            // String param = RSAEncryptionUtil.decryptWithRSA(certId, privateKey);
            String param = Sm4Tool.decrypt(certId, "5d3b282609644b4f8992b31a2e92f0f3");
            if (StrUtil.isNotEmpty(param)) {
                id = param.split("_")[1];
                log.info("app扫码核验接口hstQrcodeVerification 请求参数qrcode解密后:{}", id);
            }

        } catch (Exception e) {
            log.error("解密异常：{}", e.getMessage());
            e.printStackTrace();
            return ResultUtil.error("解密失败");
        }

        // 3.根据电子证照id，获取电子证照照面信息
        if (StrUtil.isNotEmpty(id)) {
            certificateVerifyVo = this.CertificateVerify(id);
        }
        accessLogService.certOperationLog("1", "3", "1", "", id);
        return ResultUtil.data(certificateVerifyVo);
    }

    /**
     * 微信扫码核验方法
     *
     * @return
     */
    public String decryptCertId(String id) throws IllegalAccessException {
        log.info("微信扫码获取跳转url接口 请求参数id:{}", id);

        // 2.解密二维码信息，获取电子证照id
        try {
            id = id.replaceAll(" ", "+"); // 微信、手机扫码会将+处理为空格，特殊处理
            PrivateKey privateKey = RSAEncryptionUtil.getPrivateKey(privateKeyString);
            // String param = RSAEncryptionUtil.decryptWithRSA(id, privateKey);
            String param = Sm4Tool.decrypt(id, "5d3b282609644b4f8992b31a2e92f0f3");
            if (StrUtil.isNotEmpty(param)) {
                log.info("微信扫码获取跳转url接口 请求参数id解密后:{}", param);
                return param;
            }
        } catch (Exception e) {
            log.error("解密异常：{}", e.getMessage());
            e.printStackTrace();
            return null;
        }

        return null;
    }

    public Map<String, Object> certificateDetail(String certificateId) {
        Map<String, Object> certificateMap = getMap(new QueryWrapper<Certificate>().lambda()
                .eq(Certificate::getCertificateId, certificateId)
                .eq(Certificate::getCreateStatus, CertificateCreateStatus.CREATED.getCode())
                .eq(Certificate::getDelFlag, "0"));
        List<CertificateAttribute> list = certificateAttributeService
                .list(new QueryWrapper<CertificateAttribute>().lambda()
                        .eq(CertificateAttribute::getCertificateId, certificateMap.get("id")));
        list.forEach(attribute -> {
            certificateMap.put(attribute.getAttributeColumnName(), attribute.getAttributeValue());
        });
        return certificateMap;
    }

    /**
     * 根据certificateId查询电子证照信息
     * 
     * @param certificateId 电子证照ID
     * @return 电子证照实体
     */
    public Certificate getByCertificateId(String certificateId) {
        QueryWrapper<Certificate> queryWrapper = Wrappers.<Certificate>query();
        queryWrapper.lambda()
                .eq(Certificate::getCertificateId, certificateId)
                .eq(Certificate::getCreateStatus, CertificateCreateStatus.CREATED.getCode())
                .last("LIMIT 1");
        return this.getOne(queryWrapper, false);
    }

    /**
     * 根据电子证照id构建文件名
     * 
     * @param certificateId 电子证照id
     * @param suffix        文件后缀
     * @return 文件名
     */
    public String buildFileName(String certificateId, String suffix) {
        // 获取证书信息
        Certificate certificate = this.getByCertificateId(certificateId);
        if (certificate == null) {
            CertificateDataRegen certificateDataRegen = this.certificateDataRegenService
                    .getByCertificateId(certificateId);

            if (certificateDataRegen == null) {
                log.error("[电子证照{}下载]查询电子证照信息异常：{}", suffix, certificateId);
                return null;
            }
            String issueDate = certificateDataRegen.getIssueDate();
            return String.format("%s-%s.%s",
                    certificateDataRegen.getCatalogName(),
                    issueDate != null ? issueDate.substring(0, 4) : DateUtil.year(new Date()),
                    suffix);
        }
        return String.format("%s-%s-%d.%s",
                certificate.getShipName(),
                certificate.getCertificateName(),
                DateUtil.year(certificate.getIssueDate()),
                suffix);
    }

    public long countCertificates(CertificateExternalQueryDto queryDto) {
        QueryWrapper<Certificate> queryWrapper = Wrappers.<Certificate>query();
        queryWrapper.lambda()
                .eq(Certificate::getCreateStatus, CertificateCreateStatus.CREATED.getCode())
                .eq(StrUtil.isNotBlank(queryDto.getHolderIdentityNumber()), Certificate::getHolderIdentityNumber,
                        queryDto.getHolderIdentityNumber())
                .in(queryDto.getCertificateNames() != null && !queryDto.getCertificateNames().isEmpty(),
                        Certificate::getCertificateName, queryDto.getCertificateNames())
                .eq(StrUtil.isNotBlank(queryDto.getShipImo()), Certificate::getShipImo, queryDto.getShipImo())
                .eq(StrUtil.isNotBlank(queryDto.getShipCallSign()), Certificate::getShipCallSign,
                        queryDto.getShipCallSign());

        // 修改船舶名称查询逻辑，同时支持中文名和英文名
        if (StrUtil.isNotBlank(queryDto.getShipName())) {
            queryWrapper.lambda().and(i -> i.eq(Certificate::getShipName, queryDto.getShipName())
                    .or()
                    .eq(Certificate::getShipNameEn, queryDto.getShipName()));
        }

        // 处理签发机关的查询
        if (queryDto.getIssuOrgCode3() != null && !queryDto.getIssuOrgCode3().isEmpty()) {
            queryWrapper.lambda().in(Certificate::getIssuOrgCode3, queryDto.getIssuOrgCode3());
        }

        // 处理证照状态
        if (StrUtil.equals("1", queryDto.getStatusFlag())) {
            queryWrapper.lambda().ge(Certificate::getExpireDate, new Date());
        } else if (StrUtil.equals("2", queryDto.getStatusFlag())) {
            queryWrapper.lambda().le(Certificate::getExpireDate, new Date());
        }

        return this.count(queryWrapper); // 返回符合条件的总记录数
    }
}
