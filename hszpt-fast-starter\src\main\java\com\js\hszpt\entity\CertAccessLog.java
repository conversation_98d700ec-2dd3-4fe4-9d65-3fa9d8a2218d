package com.js.hszpt.entity;

import com.js.core.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.js.util.SnowFlakeUtil;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.Builder;
import java.util.Date;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;


/**
 * 
 * @ClassName:  CertAccessLog   
 * @Description:TODO(电子证照访问记录表)   
 * @author:   System Generation 
 */
@Data
@TableName("CERT_ACCESS_LOG")
@ApiModel(value = "电子证照访问记录表")
public class CertAccessLog{


    /**
    * 访问日志ID
    */
    @TableId(value = "ACCESS_LOG_ID")
    @ApiModelProperty(value = "访问日志ID")
    private String id = String.valueOf(SnowFlakeUtil.nextId());


    /**
    * 访问来源：1-海事通APP 2-海事一网通办 3-智慧海事
    */
    @TableField("ACCESS_SOURCE")
    @ApiModelProperty(value = "访问来源：1-海事通APP 2-海事一网通办 3-智慧海事")
    private String accessSource;

    /**
     * 操作类型：1-电子证照下载 2-查询 3-海事通APP扫描核验 4-其他APP扫码核验
     */
    @TableField("OPER_TYPE")
    @ApiModelProperty(value = "操作类型：1-电子证照下载 2-查询 3-海事通APP扫描核验 4-其他APP扫码核验")
    private String OperType;


    /**
    * 访问用户ID
    */
    @ApiModelProperty(value = "访问用户ID")
    private String accessUserId;


    /**
    * 访问用户名称
    */
    @ApiModelProperty(value = "访问用户名称")
    private String accessUserName;


    /**
    * 证照ID
    */
    @ApiModelProperty(value = "证照ID")
    private String certificateId;


    /**
    * 证照类型代码
    */
    @ApiModelProperty(value = "证照类型代码")
    private String certTypeCode;


    /**
    * 证照名称
    */
    @ApiModelProperty(value = "证照名称")
    private String certName;


    /**
    * 访问时间
    */
    @TableField(value = "ACCESS_TIME", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "访问时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date accessTime;


    /**
    * 访问IP
    */
    @ApiModelProperty(value = "访问IP")
    private String accessIp;


    /**
    * 访问结果：1-成功 0-失败
    */
    @ApiModelProperty(value = "访问结果：1-成功 0-失败")
    private String accessResult;


    /**
    * 失败原因
    */
    @ApiModelProperty(value = "失败原因")
    private String failReason;


    /**
    * 备注
    */
    @ApiModelProperty(value = "备注")
    private String remarks;

    /**
    * 创建时间
    */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @TableField(exist = false)
    private String createBy;

    @TableField(exist = false)
    private String updateBy;

    @TableField(exist = false)
    private Date updateTime;

}
