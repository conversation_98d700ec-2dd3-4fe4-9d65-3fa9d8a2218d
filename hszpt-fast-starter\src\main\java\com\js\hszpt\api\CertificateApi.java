package com.js.hszpt.api;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.DeserializationFeature;import com.fasterxml.jackson.databind.ObjectMapper;
import com.js.core.api.BaseApiPlus;
import com.js.core.common.utils.ResultUtil;
import com.js.core.common.vo.Result;
import com.js.hszpt.config.*;
import com.js.hszpt.dto.*;
import com.js.hszpt.enmus.CertificateTypeCode;
import com.js.hszpt.entity.BizAffairApply;
import com.js.hszpt.entity.Certificate;
import com.js.hszpt.entity.CtfCertificate;
import com.js.hszpt.entity.CtfCertificateType;
import com.js.hszpt.properties.ZptProperties;
import com.js.hszpt.service.*;
import com.js.hszpt.utils.FileDownloadUtil;
import com.js.hszpt.utils.HttpProxyUtil;
import com.js.hszpt.utils.VerifyCodeUtil;
import com.js.hszpt.utils.ZptUtil;
import com.js.hszpt.utils.encrypt.RSAEncryptionUtil;
import com.js.hszpt.utils.encrypt.Sm4Tool;
import com.js.hszpt.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.PrivateKey;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/certificate")
@Slf4j
public class CertificateApi extends BaseApiPlus<CertificateService, Certificate, String> {

    @Autowired
    private BizAffairApplyService bizAffairApplyService;

    @Autowired
    private CertificateDateConfig certificateDateConfig;

    @Autowired
    private ZptProperties zptProperties;

    @Autowired
    private GenerateCertificateService generateCertificateService;

    @Value("${qrcode.redirectUrl}")
    private String redirectUrl;

    @Value("${qrcode.security.privateKey}")
    private String privateKeyString;

    @Autowired
    private HttpProxyUtil httpProxyUtil;

    //@Value("${proxy.crew-certificate-url}")
    @Value("${proxy.target-url}")
    private String crewCertificateUrl;

    @Value("${certificate.targetUrl}")
    private String targetUrl;

    @Value("${certificate.certOfd}")
    private String certOfd;

    @Autowired
    private ProxyConfig proxyConfig;

    @Autowired
    private VerifyCodeUtil verifyCodeUtil;

    @Autowired
    private CertAccessLogService accessLogService;
    @Autowired
    private CertificateService certificateService;

    @Value("${proxy.cert-org-query-url}")
    private String certOrgQueryUrl;

    @Autowired
    private CtfCertificateTypeService ctfCertificateTypeService;

    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private FileDownloadUtil fileDownloadUtil;

    private ObjectMapper objectMapper = new ObjectMapper();

    @Value("${spring.profiles.active:prod}")
    private String active;

    /**
     * 保存证书信息和照面属性
     *
     * @param certificateDto 证书数据传输对象
     * @return 保存结果
     */
    @PostMapping("/saveCertificateAndAttribute")
    public Result<?> saveCertificateAndAttribute(@RequestBody CertificateDto certificateDto) {
        log.info("【外部推送证照数据】接收到证照保存请求，证照名称：{}", certificateDto != null ? certificateDto.getCertificateName() : "空");
        try {
            Result<?> result = generateCertificateService.saveCertificateAndAttribute(certificateDto);
            log.info("【外部推送证照数据】证照保存处理完成，结果：{}", result.isSuccess() ? "成功" : "失败");
            return result;
        } catch (Exception e) {
            log.error("【外部推送证照数据】保存证书信息和照面属性异常", e);
            return ResultUtil.error("系统异常");
        }
    }

    /**
     * 下载电子证照jpg
     *
     * @param certificateId 电子证照ID
     * @return 包含文件名和下载地址的map
     */
    @GetMapping("/download/{certificateId}")
    public Result<Map<String, String>> download(@PathVariable("certificateId") String certificateId) {
        log.info("下载请求的证照ID: {}", certificateId);
        String url = baseService.download(certificateId);
        if (StrUtil.isBlank(url)) {
            return ResultUtil.error("电子证照查看失败");
        }

        // 截取电子证照服务IP替换为外网域名
        String target = "DCS";
        String newPrefix = targetUrl;
        int index = url.indexOf(target);
        String jpgUrl = "";
        if (index != -1) {
            String suffix = url.substring(index);
            jpgUrl = newPrefix + suffix;
            log.info(jpgUrl);
        } else {
            log.error("目标字符串未找到。");
            return ResultUtil.error("电子证照查看失败");
        }

        if (StrUtil.isBlank(jpgUrl)) {
            return ResultUtil.error("电子证照查看失败");
        }

        Map<String, String> resultMap = new HashMap<>(2);
        String fileName = baseService.buildFileName(certificateId, "jpg");
        log.info("生成的文件名: {}", fileName);
        if (StrUtil.isBlank(fileName)) {
            return ResultUtil.error("电子证照查看失败");
        }
        resultMap.put("url", jpgUrl);
        resultMap.put("fileName", fileName);
        accessLogService.certOperationLog("2", "1", "1", "",certificateId);
        return ResultUtil.data(resultMap);
    }

    /**
     * 返回base64图片
     *
     * @param certificateId
     * @return
     */
    @GetMapping("/downloadBase64/{certificateId}")
    public Result<String> downloadBase64(@PathVariable("certificateId") String certificateId) {
        String url = baseService.download(certificateId);
        if (StrUtil.isBlank(url)) {
            return ResultUtil.error("电子证照查看失败");
        }
        // 获取图片base64
        String base64 = ZptUtil.convertImageUrlToBase64(url);
        if (StrUtil.isNotBlank(base64)) {
            accessLogService.certOperationLog("3", "1", "1", "",certificateId);
            return ResultUtil.data(base64);
        } else {
            return ResultUtil.error("电子证照查看失败");
        }
    }

    @GetMapping("/createCertificate")
    public Result<String> createCertificate() {
        baseService.createDebrisCert();
        return ResultUtil.data("【残骸事项】证书照面信息生成已执行");
    }

    /**
     * 海事通app电子证照查询接口
     *
     * @param idNumber 身份证号
     * @return 电子证照信息
     */
    // @GetMapping("/getCertificateByIdNumber/{idNumber}")
    // public Result<List<CertificateAppVo>> getCertificateByIdNumber(@PathVariable
    // String idNumber, String validCode) {
    // List<CertificateAppVo> certificateAppVoList =
    // baseService.getCertificateByIdNumber(idNumber, validCode);
    // return ResultUtil.data(certificateAppVoList);
    // }

    /**
     * 获取电子证照详细-后台管理证照预览
     *
     * @param certificateId 电子证照生成的id
     * @return 电子证照接口生成信息
     */
    @GetMapping("/getPermitDetail/{certificateId}")
    public Result<Map<String, String>> getPermitDetail(@PathVariable("certificateId") String certificateId) {
        log.info("获取证照详情请求的证照ID: {}", certificateId);
        // 获取OFD文件URL
        String url = baseService.getPermitDetail(certificateId);
        if (StrUtil.isBlank(url)) {
            return ResultUtil.error("电子证照查看失败");
        }

        // 截取电子证照服务IP替换为外网域名
        String target = "group1";
        String newPrefix = targetUrl;
        int index = url.indexOf(target);
        String ofdUrl = "";
        if (index != -1) {
            String suffix = url.substring(index);
            ofdUrl = newPrefix + suffix;
            log.info("生成的OFD文件URL: {}", ofdUrl);
        } else {
            log.error("目标字符串未找到。");
            return ResultUtil.error("电子证照查看失败");
        }

        // 使用buildFileName方法生成符合规范的文件名
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String subPrefix = certOfd + sdf.format(date) + "/";

        // 创建目录
        String dirPath = zptProperties.getFilePath() + subPrefix;
        File dir = new File(dirPath);
        if (!dir.exists()) {
            boolean created = dir.mkdirs();
            if (!created) {
                log.error("目录创建失败: {}", dirPath);
                return ResultUtil.error("文件存储目录创建失败");
            }
            log.info("已创建存储目录: {}", dirPath);
        }

        String fileName = baseService.buildFileName(certificateId, "ofd"); // 调用buildFileName方法
        log.info("生成的文件名: {}", fileName);
        String cachedFilePath = dirPath + fileName; // 使用已创建的目录路径
        log.info("文件存放路径: {}", cachedFilePath);
        // 🔑 修复：使用优化的文件下载工具，防止句柄泄露
        boolean downloadSuccess = fileDownloadUtil.downloadFile(url, cachedFilePath);
        if (!downloadSuccess) {
            return ResultUtil.error("电子证照查看失败");
        }
        // 返回结果
        Map<String, String> result = new HashMap<>(2);
        result.put("url", newPrefix + subPrefix + fileName); // 返回下载后的文件 URL
        log.info("返回的文件URL: {}", newPrefix + fileName);
        result.put("fileName", fileName);

        return ResultUtil.data(result);
    }

    // 已移除原downloadFile方法，使用FileDownloadUtil替代以防止资源泄露

    /**
     * 生成电子证照照面信息
     *
     * @param applyId 申请id
     * @return 生成结果
     */
    @GetMapping("/createCertificate/{applyId}")
    public Result<String> createCertificate(@PathVariable String applyId) {
        BizAffairApply bizAffairApply = bizAffairApplyService.getById(applyId);
        certificateDateConfig.createCertificate(bizAffairApply);
        return ResultUtil.success("电子证照照面信息已生成");
    }

    /**
     * 残骸事项生成电子证照照面信息
     *
     * @param debrisCertDto 残骸事项信息
     * @return 生成结果
     */
    @PostMapping("/createDebrisCertificate")
    public Result<String> createDebrisCertificate(@RequestBody DebrisCertDto debrisCertDto) {
        String id = baseService.createCertificate(debrisCertDto);
        return StrUtil.isNotBlank(id) ? ResultUtil.data(id, "残骸电子证照照面信息已生成") : ResultUtil.error("残骸电子证照照面信息生成失败");
    }

    /**
     * 油污事项生成电子证照照面信息
     *
     * @param oilDamageDto 油污事项信息
     * @return 生成结果
     */
    @PostMapping("/createOilDamageCertificate")
    public Result<String> createOilDamageCertificate(@RequestBody OilDamageDto oilDamageDto) {
        String id = baseService.createCertificate(oilDamageDto);
        return StrUtil.isNotBlank(id) ? ResultUtil.data(id, "油污电子证照照面信息已生成") : ResultUtil.error("油污电子证照照面信息生成失败");
    }

    /**
     * 燃油事项生成电子证照照面信息
     *
     * @param fuelPollutionDto 燃油事项信息
     * @return 生成结果
     */
    @PostMapping("/createFuelPollutionCertificate")
    public Result<String> createFuelPollutionCertificate(@RequestBody FuelPollutionDto fuelPollutionDto) {
        String id = baseService.createCertificate(fuelPollutionDto);
        return StrUtil.isNotBlank(id) ? ResultUtil.data(id, "燃油电子证照照面信息已生成") : ResultUtil.error("燃油电子证照照面信息生成失败");
    }

    /**
     * 非持久性燃油事项生成电子证照照面信息
     *
     * @param nonPersistentDto 非持久性燃油事项信息
     * @return 生成结果
     */
    @PostMapping("/createNonPersistentCertificate")
    public Result<String> createNonPersistentCertificate(@RequestBody NonPersistentDto nonPersistentDto) {
        String id = baseService.createCertificate(nonPersistentDto);
        return StrUtil.isNotBlank(id) ? ResultUtil.data(id, "非持久性燃油电子证照照面信息已生成")
                : ResultUtil.error("非持久性燃油电子证照照面信息生成失败");
    }

    /**
     * 查看生成电子证照生成状态
     *
     * @param id 主表id
     * @return 电子证照id
     */
    @GetMapping("/getCertificateId/{id}")
    public Result<String> getCertificateId(@PathVariable String id) {
        String certificateId = baseService.getCertificateId(id);
        return StrUtil.isNotBlank(certificateId) ? ResultUtil.data(certificateId, "电子证照已生成")
                : ResultUtil.error("电子证照未生成");
    }

    /**
     * 船舶保险电子证照外网查询接口
     *
     * 支持根据多种条件查询电子证照信息，包括：
     * - 证书编号（certificateNum）
     * - 船舶名称（shipName）
     * - 船舶IMO号（shipImo）
     * - 船舶呼号（shipCallSign）
     * - 证书印刷号（certPrintNo）- 新增
     * - 许可证号（licenseNum）- 新增
     * - 身份证号（holderIdentityNumber）- 新增
     * - 证照类型（certificateTypeCode）- 新增，必填
     *
     * @param externalNetworkQueryDto 查询参数DTO
     * @param page                    分页参数
     * @return 查询结果，包含电子证照信息列表
     */
    @GetMapping("/certificateExternalNetworkQuery")
    public Result<IPage<certificateExternalNetworkQueryVo>> certificateExternalNetworkQuery(
            ExternalNetworkQueryDto externalNetworkQueryDto, Page page) {
        log.info("开始处理外网查询请求，参数：{}", externalNetworkQueryDto);
        //Certificate certificate = certificateService.getOne(new QueryWrapper<Certificate>().eq("CERTIFICATE_NUM", externalNetworkQueryDto.getCertificateNum()));
        try {

            Result<IPage<certificateExternalNetworkQueryVo>> result = baseService
                    .certificateExternalNetworkQuery(externalNetworkQueryDto, page);
            // 简化日志，避免使用getData()方法
            // 统计功能进行改造，记录次数，
            if (result != null && result.getResult() != null) {
                IPage<certificateExternalNetworkQueryVo> pageVo = result.getResult();
                if (pageVo.getTotal() != 0 && pageVo.getRecords() != null && !pageVo.getRecords().isEmpty()) {
                    accessLogService.certOperationLog("2", "2", "1", "", pageVo.getRecords().get(0).getCertificateId());
                } else {
                    log.warn("证书记录为空，跳过日志记录。Total: {}, Records: {}",
                            pageVo.getTotal(),
                            pageVo.getRecords() == null ? "null" : "empty");
                }
            }
            log.info("外网查询请求处理完成");
            return result;
        } catch (Exception e) {
            log.error("处理外网查询请求异常：", e);
            return ResultUtil.error("系统异常：" + e.getMessage());
        }
    }

    /**
     * 内网查询新
     */
    @GetMapping("/certificateIntranetQueryQuery")
    public Result<IPage<CertificateIntranetQueryQueryVo>> certificateIntranetQueryQueryAuth(
            IntranetQueryQueryDto intranetQueryQueryDto,
            Page page) {
        try {
            IPage<CertificateIntranetQueryQueryVo> pageData = baseService
                    .certificateIntranetQueryQueryAuth(intranetQueryQueryDto, page);
            // 统计功能进行改造，记录次数，
            if (pageData.getTotal() != 0) {
                accessLogService.certOperationLog("3", "2", "1", "",pageData.getRecords().get(0).getCertificateId());
            }
            return Result.success(pageData);
        } catch (RuntimeException e) {
            log.error("证照查询失败: {}", e.getMessage());
            return Result.failed(500, e.getMessage());
        } catch (Exception e) {
            log.error("证照查询失败", e);
            return Result.failed(500, "系统异常");
        }
    }

    /**
     * 证照核验查询接口
     */
    @GetMapping("/certificateVerify/{id}")
    public Result<List<Map<String, Object>>> certificateVerify(@PathVariable String id) throws IllegalAccessException {
        try {
            List<Map<String, Object>> listData = baseService.CertificateVerify(id);
            accessLogService.certOperationLog("2", "3", "1", "",id);
            return Result.success(listData);
        } catch (Exception e) {
            return ResultUtil.error("系统异常：" + e.getMessage());
        }
    }

    /**
     * 证照核验查询接口
     */
    @GetMapping("/hstQrcodeVerification")
    public Result hstQrcodeVerification(@RequestParam("qrcode") String qrcode,
            @RequestParam("source") String source) throws IllegalAccessException {
        try {
            Result result = baseService.hstQrcodeVerification(qrcode, source);
            return Result.success(result);
        } catch (Exception e) {
            return ResultUtil.error("系统异常：" + e.getMessage());
        }
    }

    @GetMapping("/cert")
    public RedirectView redirectToUrl(@RequestParam("id") String id) {
        String url = redirectUrl;
        try {
            String certId = baseService.decryptCertId(id);
            if (StrUtil.isNotEmpty(certId)) {
                String[] array = certId.split("_");
                certId = array[1];
                String certificateTypeCode = array[0];
                CtfCertificateType certificateType = ctfCertificateTypeService.getByCertificateTypeCode(certificateTypeCode);
                if (certificateType != null) {
                    String certClassification = certificateType.getCertClassification();
                    log.info("微信扫码获取跳转url接口，查询到certificateType为： {}", certClassification);
                    if("1".equals(certClassification)){
                        //船舶证照
                        url = url + "?id=" + certId + "&certClassification=" + certClassification;
                    }else {
                        //船员证照
                        if("prod".equals(active)){
                            url = "https://dzzz.msa.gov.cn/dzzz-web/#/certQuery";
                        }else{
                            url = "https://dzzz.msa.gov.cn/dzzz-web-dev/#/certQuery";
                        }
                        url = url + "?id=" + certId + "&certClassification=2";
                    }
                }else{
                    //默认船员证照
                    if("prod".equals(active)){
                        url = "https://dzzz.msa.gov.cn/dzzz-web/#/certQuery";
                    }else{
                        url = "https://dzzz.msa.gov.cn/dzzz-web-dev/#/certQuery";
                    }
                    url = url + "?id=" + certId + "&certClassification=2";
                }
                log.info("微信扫码获取跳转url接口，重定向URL为： {}", url);
                accessLogService.certOperationLog("2", "4", "1", "",certId);
            }
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
        RedirectView redirectView = new RedirectView();
        redirectView.setUrl(url);
        return redirectView;
    }

    /**
     * 查看详情
     *
     * @param certificateId
     * @return
     */
    @GetMapping("/certificateDetail/{certificateId}")
    public Result<Map<String, Object>> certificateDetail(@PathVariable String certificateId) {
        return Result.success(baseService.certificateDetail(certificateId));
    }

    /**
     * 外网船员证书查询接口 - 透传调用第三方服务
     *
     * 支持根据多种条件查询船员电子证照信息，包括：
     * - 证书编号（certificateNumber）
     * - 持证人姓名（certificateHolderName）
     * - 身份证号（certificateHolderCode）
     * - 证照类型（certificateTypeCode）- 必填
     * - 证书印刷号（certPrintNo）
     *
     * @param crewCertificateQueryDto 查询参数DTO
     * @param page                    分页参数
     * @return 查询结果，包含船员电子证照信息列表
     */
    @GetMapping("/crewCertificateQuery")
    public Result<Map<String, Object>> crewCertificateQuery(
            CrewCertificateQueryDto crewCertificateQueryDto,
            Page page,
            HttpServletRequest request,
            HttpServletResponse response) {
        log.info("开始透传调用外网船员证书查询服务，参数：{}", crewCertificateQueryDto);
        String certificateNumber = crewCertificateQueryDto.getCertificateNumber();
        if (StrUtil.isNotBlank(certificateNumber)) {
            crewCertificateQueryDto.setCertificateNumber(URLUtil.decode(certificateNumber,StandardCharsets.UTF_8));
        }
        try {
            // 外网查询校验验证码
            if (!verifyCodeUtil.check(crewCertificateQueryDto.getCaptchaId(), crewCertificateQueryDto.getCode())
                    && !StrUtil.equals(crewCertificateQueryDto.getDisplaySource(), "2")
                    && StrUtil.isBlank(crewCertificateQueryDto.getCertificateId())) {
                return Result.failed(205, "验证码错误");
            }
            // 解密二维码信息，获取电子证照id
            String id = null;
            String certTypeCode = null;
            String qrcode = crewCertificateQueryDto.getQrcode();
            boolean isAPP = false;
            log.info("app扫码核验请求参数qrcode:{}", qrcode);
            if (StrUtil.isNotEmpty(qrcode)) {
                isAPP = true;   //记录APP核验
                PrivateKey privateKey = RSAEncryptionUtil.getPrivateKey(privateKeyString);
                String certId = qrcode.substring(qrcode.lastIndexOf("id=") + 3);
                certId = certId.replace(" ", "+");
                String param = Sm4Tool.decrypt(certId, "5d3b282609644b4f8992b31a2e92f0f3");
                if (StrUtil.isNotEmpty(param)) {
                    certTypeCode = param.split("_")[0];
                    id = param.split("_")[1];
                    log.info("app扫码核验接口hstQrcodeVerification 请求参数qrcode解密后:{}", id);
                    crewCertificateQueryDto.setCertificateId(id);
                }
            }
            // 获取所有配置的证书类型编码
            String[] typeCodeArray = Arrays.stream(CertificateTypeCode.values())
                    .map(CertificateTypeCode::getTypeCode)
                    .toArray(String[]::new);
            // 枚举配置的类型不进行透传 直接查询业务
            if (StrUtil.equalsAny(certTypeCode, typeCodeArray)) {
                List<Map<String, Object>> listData = baseService.CertificateVerify(id);
                response.setContentType("application/json;charset=UTF-8");
                String result = objectMapper.writer().writeValueAsString(ResultUtil.data(listData));
                response.getWriter().write(JSONUtil.toJsonStr(result));
                return null;
            }
            // 使用HttpProxyUtil转发请求到第三方服务
            // 这里使用配置的外网船员证书查询服务URL，并启用鉴权
            // 由于是GET请求，使用新增的forwardGetRequest方法，确保修改的参数能被正确传递
//            httpProxyUtil.forwardGetRequest(request, response, proxyConfig.getTargetUrl(), true,
//                    crewCertificateQueryDto);
             Map<String, Object> pageResult = new HashMap<>();
             byte[] responseData = httpProxyUtil.forwardRequestAndGetResponse
                                    (request, response, crewCertificateUrl, true, crewCertificateQueryDto);
            // 由于forwardRequest方法已经处理了响应，这里不需要返回值
            // 但由于方法签名需要返回Result，返回null（实际上这个返回值不会被使用）
            // 处理响应数据
            if (responseData != null) {
                String responseStr = new String(responseData, "UTF-8");
                log.info("获取到的响应数据: {}", responseStr);

                // 将响应数据转换为对象
                ObjectMapper objectMapper = new ObjectMapper()
                                    .setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"))
                                    .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);;
                // 创建TypeReference来指定复杂的泛型类型
                Map<String, Object> resultMap = objectMapper.readValue(responseStr, Map.class);
                Map<String,Object> records = (Map<String, Object>) resultMap.get("result");
                // 提取分页信息
                int total = (int) records.get("total");
                int current = (int) records.get("current");
                int size = (int) records.get("size");
                boolean searchCount = (boolean) records.get("searchCount");
                int validCount = (int) records.get("validCount");

                List<Map<String, Object>> recordsList = (List<Map<String, Object>>) records.get("records");
                // 创建分页对象
                pageResult.put("current",current);
                pageResult.put("size",size);
                pageResult.put("total",total);
                pageResult.put("searchCount",searchCount);
                pageResult.put("validCount",validCount);

                List<CrewCertificateQueryVo> list = new ArrayList<>();
                if (recordsList != null && !recordsList.isEmpty()) {
                    for (Map<String, Object> record : recordsList) {
                        CrewCertificateQueryVo vo = objectMapper.convertValue(record, CrewCertificateQueryVo.class);
                        list.add(vo);
                    }
                    log.info("获取到的集合数据: {}", list.get(0).getCertificateId());
                    pageResult.put("records",list);
                    accessLogService.certOperationLog(isAPP?"1":"2", isAPP?"3":"2", "1", "",list.get(0).getCertificateId());
                } else {
                    log.info("未获取到记录数据");
                }
            }
            return Result.success(pageResult);
        } catch (Exception e) {
            log.error("透传调用外网船员证书查询服务失败", e);
            e.printStackTrace();
            return ResultUtil.error("系统异常：" + e.getMessage());
        }
    }

    /**
     * 内网船员证书查询 - 透传调用第三方服务
     */
    @GetMapping("/certificateIntranetCrewQuery")
    public Result<IPage<CertificateIntranetCrewQueryVo>> certificateIntranetCrewQueryAuth(
            IntranetCrewQueryDto intranetCrewQueryDto,
            HttpServletRequest request,
            HttpServletResponse response) {
        try {
            String orgCode = sysUserService.getCurrUserOrgCode();
            intranetCrewQueryDto.setLoginOrgCode(orgCode);
            log.info("开始透传调用船员证书查询服务，参数：{}", intranetCrewQueryDto);
            // 使用HttpProxyUtil转发请求到第三方服务
            // 这里使用配置的船员证书查询服务URL，并启用鉴权
            // httpProxyUtil.forwardRequest(request, response, crewCertificateUrl, true);
            // 使用修改后的方法获取响应数据
            Page<CertificateIntranetCrewQueryVo> pageResult = new Page<>();
            byte[] responseData = httpProxyUtil.forwardRequestAndGetResponse
                                    (request, response, crewCertificateUrl, true, intranetCrewQueryDto);
            // 处理响应数据
            if (responseData != null) {
                String responseStr = new String(responseData, "UTF-8");
                log.info("获取到的响应数据: {}", responseStr);

                // 将响应数据转换为对象
                ObjectMapper objectMapper = new ObjectMapper()
                                    .setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"))
                                    .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);;
                // 创建TypeReference来指定复杂的泛型类型
                Map<String, Object> resultMap = objectMapper.readValue(responseStr, Map.class);
                Map<String,Object> records = (Map<String, Object>) resultMap.get("result");
                if (records == null) {
                    return Result.success(pageResult);
                }
                // 提取分页信息
                int total = (int) records.get("total");
                int current = (int) records.get("current");
                int size = (int) records.get("size");

                List<Map<String, Object>> recordsList = (List<Map<String, Object>>) records.get("records");
                // 创建分页对象
                pageResult.setCurrent(current);
                pageResult.setSize(size);
                pageResult.setTotal(total);

                List<CertificateIntranetCrewQueryVo> list = new ArrayList<>();
                if (recordsList != null && !recordsList.isEmpty()) {
                    for (Map<String, Object> record : recordsList) {
                        CertificateIntranetCrewQueryVo vo = objectMapper.convertValue(record, CertificateIntranetCrewQueryVo.class);
                        list.add(vo);
                    }
                    log.info("获取到的集合数据: {}", list.get(0).getCertificateId());
                    pageResult.setRecords(list);
                    accessLogService.certOperationLog("3", "2", "1", "",list.get(0).getCertificateId());
                } else {
                    log.info("未获取到记录数据");
                }
            }
            return Result.success(pageResult);
        } catch (Exception e) {
            log.error("透传调用船员证书查询服务失败", e);
            return Result.failed(500, "系统异常：" + e.getMessage());
        }
    }

    /**
     * 查询证书机构 - 透传调用第三方服务
     *
     * @param request HTTP请求
     * @param response HTTP响应
     * @return 证书机构列表
     */
    @GetMapping("/queryCertOrgs")
    public Result<?> queryCertOrgs(HttpServletRequest request, HttpServletResponse response, CertQueryOrgVO queryVO) {
        try {
            log.info("开始透传调用证书机构查询服务");

            // 使用HttpProxyUtil转发请求到第三方服务
            // 这里使用配置的证书机构查询服务URL，并启用鉴权
            httpProxyUtil.forwardGetRequest(request, response, proxyConfig.getTargetUrl(), true, queryVO);

            // 由于forwardRequest方法已经处理了响应，这里不需要返回值
            return null;
        } catch (Exception e) {
            log.error("透传调用证书机构查询服务失败", e);
            return ResultUtil.error("系统异常：" + e.getMessage());
        }
    }
}
