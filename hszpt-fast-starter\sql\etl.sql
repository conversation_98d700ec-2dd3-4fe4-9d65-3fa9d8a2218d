-- 电子证照签发情况统计
SELECT MD5(
               CONCAT_WS('_', -- 用下划线连接防止字段粘连
                         src.certificate_issuing_authority_code,
                         LEFT(src.certificate_type_code, 10),
                         src.certificate_holder_category,
                         '1',
                         src.certificate_status
               )
       )::VARCHAR(64)                                                          AS issue_id,           -- MD5生成32位字符
       to_char(to_date(src.certificate_issued_date, 'yyyy-mm-dd'), 'yyyyMMdd') AS ds_certificate_date,
       (case
            when src.certificate_type_name in
                 ('船上厨师培训合格证明', '船上膳食服务辅助人员培训证明', '船员培训质量管理体系证书', '海船船员健康证明')
                then '2'
            else '1' end)                                                      as org_type,
       src.issue_dept_code3                                                    AS issuer_code,
       src.certificate_type_code                                               AS certificate_type_code,
       src.certificate_holder_category                                         AS holder_category_code,
       '1'                                                                     AS matter_nature_code, -- 示例：假设事项性质关联事项类型
       '1'                                                                     AS application_source_code,
       (case
            when to_date(src.certificate_expiring_date, 'yyyy-mm-dd') >= current_date then '1'
            else '2' end)                                                      AS certificate_status_code,
       (case when cfg.CERTIFICATE_NAME is not null
                 then cfg.CERTIFICATE_TYPE_CODE
             else 'ctf-' || substr(md5(random()::text), 0, 4) end)               AS certificate_code,
       src.affair_id                                                           AS ctf_affair_id,
       src.related_item_name                                                   AS ctf_affair_name,
       src.certificate_name                                                    AS certificate_name,
       src.certificate_type_name                                               AS certificate_type_name,
       src.certificate_issuing_authority_name                                  AS issuer_name,
       dict.title                                                              AS holder_category_name,
       '行政许可'                                                              AS matter_nature_name,
       '在线办理'                                                              AS application_source_name,
       (case
            when to_date(src.certificate_expiring_date, 'yyyy-mm-dd') >= current_date then '有效'
            else '无效' end)                                                   AS certificate_status_name,
       COUNT(*)                                                                AS issue_count,
       CURRENT_TIMESTAMP                                                       AS create_date
FROM dwdz_certificate_data src
         left join (select dcdd.title,dcdd.value from (select * from dwdz_ctf_dict where type = 'CERTIFICATE_HOLDER_CATEGORY') dcd
                                                          inner join dwdz_ctf_dict_data dcdd on dcd.id = dcdd.dict_id) dict
                   on dict.value = src.certificate_holder_category
         left join dwdz_ctf_certificate_config cfg
                   on cfg.CATALOG_NAME = src.certificate_name
WHERE src.certificate_status NOT IN ('-5', '-4', '-3', '-2', '-1') -- 排除异常状态
--   and to_timestamp(src.create_time,'yyyy-mm-dd hh24:mi:ss') >= to_timestamp('$date_format(${day_id},yyyy-MM-dd 00:00:00)','yyyy-mm-dd hh24:mi:ss') and to_timestamp(src.create_time,'yyyy-mm-dd hh24:mi:ss') <= to_timestamp('$date_format(${day_id},yyyy-MM-dd 23:59:59)','yyyy-mm-dd hh24:mi:ss') -- 增量抽取签发数据
GROUP BY ds_certificate_date,
         issuer_code,
         src.certificate_type_code,
         holder_category_code,
         matter_nature_code,
         application_source_code,
         certificate_status_code,
         certificate_code,
         ctf_affair_id,
         ctf_affair_name,
         src.certificate_name,
         certificate_type_name,
         issuer_name,
         holder_category_name,
         matter_nature_name,
         application_source_name,
         certificate_status_name,
         src.related_item_name,
         src.certificate_issuing_authority_code,
         src.certificate_holder_category,
         src.certificate_status
;
		 
-- 电子证照使用情况统计
WITH usage_data AS (SELECT
                        -- 维度字段
                        d.issue_dept_code3                   AS issuer_code,
                        d.certificate_type_code              AS certificate_type_code,
                        d.certificate_holder_category        AS holder_category_code,
                        d.affair_type                        AS matter_nature_code,
                        '1'                                  AS application_source_code,
                        (case
                             when to_date(d.certificate_expiring_date, 'yyyy-mm-dd') >= current_date then '1'
                             else '2' end)                   AS status_code,
                        to_char(a.ACCESS_TIME, 'yyyymmdd')   as ds_certificate_date,
                        (case
                             when d.certificate_type_name not in
                                  ('船上厨师培训合格证明', '船上膳食服务辅助人员培训证明', '船员培训质量管理体系证书',
                                   '海船船员健康证明')
                                 then '1'
                             else '2' end)                   as org_type,
                        (case
                             when ctf.CERTIFICATE_TYPE_CODE is not null
                                 then ctf.CERTIFICATE_TYPE_CODE
                             else 'ctf-' || substr(md5(random()::text), 0, 8)
                            end)                             AS certificate_code,
                        d.certificate_name                   AS certificate_name,
                        d.affair_id                          as ctf_affair_id,
                        d.affair_type                        as ctf_affair_name,
                        d.certificate_type_name              AS certificate_type_name,
                        d.certificate_issuing_authority_name AS issuer_name,
                        dict.title                           AS holder_category_name,
                        (case
                             when to_date(d.certificate_expiring_date, 'yyyy-mm-dd') >= current_date then '有效'
                             else '无效' end)                as certificate_status_name,
                        -- 使用类型映射
                        CASE
                            WHEN a.OPER_TYPE = '1' THEN '1' -- 下载
                            WHEN a.OPER_TYPE IN ('3', '4') THEN '2' -- 核验
                            END                              AS uasge_type,
                        COUNT(*)                             AS uasge_count
                    FROM dwdz_cert_access_log a
                             JOIN dwdz_certificate_data d ON a.CERTIFICATE_ID = d.certificate_id -- 关联签发信息表获取维度
                             left join (select dcdd.title, dcdd.value
                                        from (select * from dwdz_ctf_dict where type = 'CERTIFICATE_HOLDER_CATEGORY') dcd
                                                 inner join dwdz_ctf_dict_data dcdd on dcd.id = dcdd.dict_id) dict
                                       on dict.value = d.certificate_holder_category
                             left join dwdz_ctf_certificate_config ctf ON d.certificate_name = ctf.CATALOG_NAME
--                         and a.ACCESS_TIME >= to_timestamp('$date_format(${day_id},yyyy-MM-dd 00:00:00)','yyyy-mm-dd hh24:mi:ss') and a.ACCESS_TIME <= to_timestamp('$date_format(${day_id},yyyy-MM-dd 23:59:59)','yyyy-mm-dd hh24:mi:ss')
                    WHERE a.ACCESS_RESULT = '1'          -- 仅统计成功访问
                      AND a.OPER_TYPE IN ('1', '3', '4') -- 有效操作类型
                    GROUP BY issuer_code,
                             d.certificate_type_code,
                             ctf.CERTIFICATE_TYPE_CODE,
                             holder_category_code,
                             matter_nature_code,
                             application_source_code,
                             status_code,
                             ds_certificate_date,
                             d.certificate_name,
                             ctf_affair_id,
                             ctf_affair_name,
                             certificate_type_name,
                             issuer_name,
                             holder_category_name,
                             certificate_status_name,
                             uasge_type,
                             certificate_status_name)
SELECT MD5(CONCAT_WS('_', -- 生成唯一ID
                     ds_certificate_date,
                     issuer_code,
                     certificate_type_code,
                     holder_category_code,
                     matter_nature_code,
                     application_source_code,
                     status_code,
                     uasge_type
           ))::VARCHAR(64)                    as uasge_id,
       ds_certificate_date                    as ds_certificate_date,
       issuer_code                            as issuer_code,
       org_type                               as org_type,
       certificate_type_code                  as certificate_type_code,
       holder_category_code                   as holder_category_code,
       '1'                                    as matter_nature_code, -- 空值处理
       COALESCE(application_source_code, '0') as application_source_code,
       COALESCE(status_code, '0')             as certificate_status_code,
       certificate_code                       as certificate_code,
       certificate_name                       as certificate_name,
       ctf_affair_id                          as ctf_affair_id,
       ctf_affair_name                        as ctf_affair_name,
       certificate_type_name                  as certificate_type_name,
       issuer_name                            as issuer_name,
       holder_category_name                   as holder_category_name,
       '行政许可'                             as matter_nature_name,
       '线上办理'                             as application_source_name,
       certificate_status_name                as certificate_status_name,
       uasge_type                             as uasge_type,
       uasge_count                            as uasge_count,
       CURRENT_TIMESTAMP                      as create_date
FROM usage_data;