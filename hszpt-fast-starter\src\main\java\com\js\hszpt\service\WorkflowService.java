package com.js.hszpt.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.js.hszpt.dto.LawWorkflowNodeUserListDto;
import com.js.hszpt.entity.LawWorkflowBpm;
import com.js.hszpt.mapper.LawWorkflowBpmDao;
import com.js.hszpt.mapper.LawWorkflowNodeUserDao;
import com.js.hszpt.vo.LawWorkflowNodeUserListVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

@Slf4j
@Service
public class WorkflowService {
    
    @Autowired
    private LawWorkflowBpmDao lawWorkflowBpmDao;

    @Autowired
    private LawWorkflowBpmService lawWorkflowBpmService;

    @Autowired
    private LawWorkflowNodeUserDao lawWorkflowNodeUserDao;
    
    /**
     * 开启工作流
     * @param businessKey 业务主键
     * @return 工作流实例ID
     */
    public String startWorkflow(String businessKey,LawWorkflowNodeUserListDto dto) {
        log.info("开启工作流，业务主键：{}", businessKey);
        List<LawWorkflowNodeUserListVo> lawWorkflowNodeUserListVoList = lawWorkflowNodeUserDao.selectWorkflowNodeUserList(dto);
        // TODO: 调用实际的工作流服务
        String executionId = "FLOW_" + System.currentTimeMillis();
        insertWorkflowData(executionId, businessKey,lawWorkflowNodeUserListVoList);
        // 插入测试数据
//        if (businessKey.startsWith("sh_")) {
//            insertTestWorkflowDataSh(executionId, businessKey);
//        }else {
//            insertTestWorkflowData(executionId, businessKey);
//        }

        return executionId;
    }
    
    /**
     * 撤回工作流
     * @param executionId 工作流实例ID
     */
    public void revokeWorkflow(String executionId, String businessKey) {
        log.info("撤回工作流，实例ID：{}", executionId);
        // TODO: 调用实际的工作流服务
        insertTestWorkflowData(executionId, businessKey);
    }
    
    /**
     * 停止工作流
     * @param executionId 工作流实例ID
     */
    public void stopWorkflow(String executionId) {
        log.info("停止工作流，实例ID：{}", executionId);
        // 先删除历史待办数据
        lawWorkflowBpmDao.deleteByExecutionId(executionId);
        // TODO: 调用实际的工作流服务
        // 停止工作流不插入测试数据
    }

    /**
     * 处理工作流 - 用于审批通过
     * @param workflowBpmId 工作流ID
     * @param operatorId 操作人ID
     */
    public void handleWorkflow(String workflowBpmId, String operatorId, String businessKey,LawWorkflowNodeUserListDto dto) {
        log.info("处理工作流，实例ID：{}，操作人：{}", workflowBpmId, operatorId);
        List<LawWorkflowNodeUserListVo> lawWorkflowNodeUserListVoList = lawWorkflowNodeUserDao.selectWorkflowNodeUserList(dto);
        // TODO: 实现工作流处理逻辑
        insertWorkflowData(workflowBpmId, businessKey,  lawWorkflowNodeUserListVoList);
//        insertTestWorkflowData(workflowBpmId, businessKey);
    }

    /**
     * 退回工作流 - 用于审批退回
     * @param workflowBpmId 工作流ID
     * @param operatorId 操作人ID
     * @param reason 原因
     */
    public void returnWorkflow(String workflowBpmId, String operatorId, String reason, String businessKey) {
        log.info("退回工作流，实例ID：{}，操作人：{}，原因：{}", workflowBpmId, operatorId, reason);
        // 删除待办数据
        lawWorkflowBpmDao.deleteByExecutionId(workflowBpmId);
        // TODO: 实现工作流退回逻辑
//        insertTestWorkflowData(workflowBpmId, businessKey);
    }

    public void insertWorkflowData(String executionId, String businessKey,List<LawWorkflowNodeUserListVo> lawWorkflowNodeUserListVoList) {
        // 先删除历史待办数据
        lawWorkflowBpmDao.deleteByExecutionId(executionId);
        List<LawWorkflowBpm> bpmList = new ArrayList<>();
        if (CollUtil.isEmpty(lawWorkflowNodeUserListVoList)) {
            throw new RuntimeException("未查询到待办审核审批人员信息");
        }
        lawWorkflowNodeUserListVoList.forEach(lawWorkflowNodeUserListVo -> {
            LawWorkflowBpm bpm = new LawWorkflowBpm();
            bpm.setWorkflowBpmId(UUID.randomUUID().toString());
            bpm.setBpmType("1");
            bpm.setExecutionId(executionId);
            bpm.setBpmNum(businessKey);
            bpm.setBusinessDate(new Date());
            bpm.setUserCode(lawWorkflowNodeUserListVo.getUserName());
            bpm.setBpmStatus("1");
            bpmList.add(bpm);
            log.info("已插入工作流测试数据，executionId：{}", executionId);
        });
        lawWorkflowBpmService.saveBatch(bpmList);
    }
    
    /**
     * 插入工作流测试数据
     * @param executionId 工作流执行ID
     */
    public void insertTestWorkflowData(String executionId, String businessKey) {
        // 先删除历史待办数据
        lawWorkflowBpmDao.deleteByExecutionId(executionId);
        
        LawWorkflowBpm bpm = new LawWorkflowBpm();
        bpm.setWorkflowBpmId(UUID.randomUUID().toString());
        bpm.setBpmType("1");
        bpm.setExecutionId(executionId);
        bpm.setBpmNum(businessKey);
        bpm.setUserCode("1906993533420703744");
        bpm.setBusinessDate(new Date());
        bpm.setBpmStatus("1");
        lawWorkflowBpmDao.insert(bpm);
        log.info("已插入工作流测试数据，executionId：{}", executionId);
    }

    public void insertTestWorkflowDataSh(String executionId, String businessKey) {
        // 先删除历史待办数据
        lawWorkflowBpmDao.deleteByExecutionId(executionId);

        LawWorkflowBpm bpm = new LawWorkflowBpm();
        bpm.setWorkflowBpmId(UUID.randomUUID().toString());
        bpm.setBpmType("1");
        bpm.setExecutionId(executionId);
        bpm.setBpmNum(businessKey);
        bpm.setUserCode("1906993881485021184");
        bpm.setBusinessDate(new Date());
        bpm.setBpmStatus("1");
        lawWorkflowBpmDao.insert(bpm);
        log.info("已插入工作流测试数据，executionId：{}", executionId);
    }
} 