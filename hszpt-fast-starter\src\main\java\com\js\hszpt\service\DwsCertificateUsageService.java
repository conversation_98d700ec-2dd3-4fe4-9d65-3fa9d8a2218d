package com.js.hszpt.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.js.hszpt.dto.SubOrgUsageStatisticsDTO;
import com.js.hszpt.dto.TimeTypeSubOrgUsageStatisticsDTO;
import com.js.hszpt.entity.DwsCertificateUsage;
import com.js.hszpt.dto.SubOrgUsageRatioStatisticsDTO;
import com.js.hszpt.dto.HolderCategoryUsageStatisticsDTO;
import com.js.hszpt.vo.CertificateUsageStatisticsVO;
import com.js.hszpt.dto.TimeUsageStatisticsDTO;

import java.util.List;

/**
 * 电子证照使用情况统计Service接口
 */
public interface DwsCertificateUsageService extends IService<DwsCertificateUsage> {

    /**
     * 按下级机构统计使用情况
     * @param param 查询参数
     * @return 按下级机构统计结果
     */
    List<SubOrgUsageStatisticsDTO> statisticsBySubOrg(CertificateUsageStatisticsVO param);

    /**
     * 按年份和下级机构统计使用情况
     * @param param 查询参数
     * @return 按年份和下级机构统计结果
     */
    List<TimeTypeSubOrgUsageStatisticsDTO> statisticsByYearAndSubOrg(CertificateUsageStatisticsVO param);

    /**
     * 按下级机构统计使用情况并计算占比
     * @param param 查询参数
     * @return 按下级机构统计结果带占比
     */
    List<SubOrgUsageRatioStatisticsDTO> statisticsBySubOrgWithRatio(CertificateUsageStatisticsVO param);

    /**
     * 按持证主体类别统计使用情况并计算占比
     * @param param 查询参数
     * @return 按持证主体类别统计结果带占比
     */
    List<HolderCategoryUsageStatisticsDTO> statisticsByHolderCategoryWithRatio(CertificateUsageStatisticsVO param);

    /**
     * 按时间统计使用情况并计算占比
     * @param param 查询参数
     * @return 按时间统计结果带占比
     */
    List<TimeUsageStatisticsDTO> statisticsByTimeWithRatio(CertificateUsageStatisticsVO param);
} 