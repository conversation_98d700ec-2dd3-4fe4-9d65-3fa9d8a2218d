package com.js.hszpt.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

@Data
@ApiModel(value = "证照类型目录审核审批VO")
public class CertTypeApprovalVO {
    @ApiModelProperty("主键ID")
    private String id;

    @ApiModelProperty("证照类型目录ID")
    private String certTypeDirId;

    @ApiModelProperty("证照类型目录审批申请ID")
    private String certApprApplyId;

    @ApiModelProperty("申请人ID")
    private String applicantId;

    @ApiModelProperty("申请人姓名")
    private String applicantName;

    @ApiModelProperty("申请提交时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date applyTime;

    @ApiModelProperty("当前审批环节：1-申请、2-审核、3-审批")
    private String currentNode;

    @ApiModelProperty("审批类型：1-提交 2-废止 3-启用")
    private String applyType;

    @ApiModelProperty("审批结果：1-通过 2-不通过 3-退回")
    private String approvalResult;

    @ApiModelProperty("审批意见")
    private String approvalOpinion;

    @ApiModelProperty("审批完成时间")
    private Date completeTime;

    @ApiModelProperty("删除标志：0-正常，1-已删除")
    private String delFlag;

    @ApiModelProperty("处理状态：1-提交 2-保存")
    private String handleStatus;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("受理机构名称")
    private String acceptOrgName;

    @ApiModelProperty("审批机构名称")
    private String approvalOrgName;
}