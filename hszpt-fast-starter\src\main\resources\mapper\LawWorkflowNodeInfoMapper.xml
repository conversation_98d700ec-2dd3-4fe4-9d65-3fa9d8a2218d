<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- 修改这里的namespace为正确的Mapper接口路径 -->
<mapper namespace="com.js.hszpt.mapper.LawWorkflowNodeInfoDao">

<!--     修改resultType为正确的包路径 -->
    <select id="getDaoServiceClient" resultType="com.js.hszpt.vo.LawWorkflowNodeInfoVo">
        select LAW_WORKFLOW_NODE_INFO_ID,
               LAW_WORKFLOW_NODE_SET_ID,
               NODE_CODE,
               NODE_NAME,
               NODE_IS_SELECT,
               NODE_ORG_LEVEL,
               NODE_SORT,
               case NODE_TYPE
                   when 1 then '审核节点'
                    when 2 then '审批节点'
                   end NODE_TYPE
        from LAW_WORKFLOW_NODE_INFO
        where LAW_WORKFLOW_NODE_INFO_ID = #{nodeInfoId}
    </select>

</mapper>