package com.js.hszpt.enmus;

import cn.hutool.core.util.StrUtil;
import com.js.hszpt.builder.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Arrays;

@NoArgsConstructor
@AllArgsConstructor
@Getter
public enum CertificateInfo {

    OIL_DAMAGE("油污损害民事责任保险或其他财务保证证书", "残骸制证使用",new OilDamageCertBuilder()),
    FUEL_POLLUTION("燃油污染损害民事责任保险或其他财务保证证书", "油污制证使用",new FuelPollutionCertBuilder()),
    NON_PERSISTENT("非持久性油类污染损害民事责任保险或其他财务保证证书","油污制证使用",new NonPersistentCertBuilder()),
    DEBRIS("残骸清除责任保险或其他财务保证证书", "油污制证使用",new DebrisCertBuilder()),
    ;
    private String name;
    private String useReason;
    private CertificateInfoBuilder certificateInfoBuilder;

    public static CertificateInfo getCertificateNameByName(String name) {
        return Arrays.stream(CertificateInfo.values())
                .filter(certificateInfo -> StrUtil.equals(certificateInfo.getName(),name))
                .findFirst()
                .orElse(null);
    }

    public static String getCertificateUseReason(String name) {
        CertificateInfo certificateInfo = Arrays.stream(CertificateInfo.values())
                .filter(c -> StrUtil.equals(c.getName(), name))
                .findFirst()
                .orElse(null);
        if (certificateInfo == null) {
            return "";
        }
        return certificateInfo.getUseReason();
    }
}
