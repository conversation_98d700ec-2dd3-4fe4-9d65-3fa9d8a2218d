package com.js.hszpt.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/8/12 16:34
 */
@ApiModel("工作流用户信息列表查询入参")
public class LawWorkflowNodeUserListDto {

    private static final long serialVersionUID = 4900622329279241286L;

    @ApiModelProperty("工作流模板ID")
    private String lawWorkflowNodeSetId;


    public List<String> getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(List<String> orgCode) {
        this.orgCode = orgCode;
    }

    @ApiModelProperty("机构编码")
    private List<String> orgCode;

    @ApiModelProperty("本级机构编码")
    private String defaultOrgId;

    @ApiModelProperty("节点ID")
    private String lawWorkflowNodeInfoId;

    @ApiModelProperty("当前页码")
    @Min(value = 1, message = "页码最小为1")
    private Integer currentPage = 1;

    @ApiModelProperty("每页条数")
    @Min(value = 1, message = "每页条数最小为1")
    @Max(value = 10, message = "每页条数最大为100")
    private Integer pageSize = 10;

    public void setCurrentPage(Integer currentPage) {
        this.currentPage = currentPage;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    /**
     * 用来区分本级和非本级   本级code
     */
    private List<String> selfOrgCode;
    /**
     * 用来区分本级和非本级   非本级code
     */
    private String noSelfOrgCode;

    public String getLawWorkflowNodeSetId() {
        return lawWorkflowNodeSetId;
    }

    public void setLawWorkflowNodeSetId(String lawWorkflowNodeSetId) {
        this.lawWorkflowNodeSetId = lawWorkflowNodeSetId;
    }



    public String getDefaultOrgId() {
        return defaultOrgId;
    }

    public void setDefaultOrgId(String defaultOrgId) {
        this.defaultOrgId = defaultOrgId;
    }

    public String getLawWorkflowNodeInfoId() {
        return lawWorkflowNodeInfoId;
    }

    public void setLawWorkflowNodeInfoId(String lawWorkflowNodeInfoId) {
        this.lawWorkflowNodeInfoId = lawWorkflowNodeInfoId;
    }

    public int getCurrentPage() {
        return currentPage;
    }


    public int getPageSize() {
        return pageSize;
    }


    public List<String> getSelfOrgCode() {
        return selfOrgCode;
    }

    public void setSelfOrgCode(List<String> selfOrgCode) {
        this.selfOrgCode = selfOrgCode;
    }

    public String getNoSelfOrgCode() {
        return noSelfOrgCode;
    }

    public void setNoSelfOrgCode(String noSelfOrgCode) {
        this.noSelfOrgCode = noSelfOrgCode;
    }
}
