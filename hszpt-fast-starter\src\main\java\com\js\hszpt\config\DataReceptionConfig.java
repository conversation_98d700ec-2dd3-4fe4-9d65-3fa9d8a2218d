package com.js.hszpt.config;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.js.hszpt.properties.ZptProperties;
import com.js.hszpt.utils.ZptUtil;
import com.js.hszpt.vo.SyncDataReceiveVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

import static com.js.hszpt.constants.DataReceiveConstant.MOCK_DATA;

@Configuration
@Slf4j
public class DataReceptionConfig {

    @Autowired
    private ZptProperties zptProperties;

    /**
     * 请求接口接收数据
     * @param syncDataReceiveVo 请求参数
     * @return 响应json字符串
     */
    public String getData(SyncDataReceiveVo syncDataReceiveVo) {
        // 模拟数据
        if (zptProperties.isMock()) {
            return StrUtil.format(MOCK_DATA,syncDataReceiveVo.getPageIndex());
        }
        String taskName = syncDataReceiveVo.getTaskName();
        try {
            // 请求参数
            Map<String, Object> paramMap = MapUtil.builder(new HashMap<String, Object>())
                    .put("pageIndex", syncDataReceiveVo.getPageIndex())
                    .put("pageSize", syncDataReceiveVo.getPageSize())
                    .put("startDate", syncDataReceiveVo.getStartDate())
                    .put("endDate", syncDataReceiveVo.getEndDate())
                    .build();
            // 调用接口接收数据
            String baseUrl = zptProperties.getUrl();
            if (StrUtil.equals("officer_info", taskName) || StrUtil.equals("apply_officer_info", taskName)) {
                baseUrl = zptProperties.getUrlOracle();
            }
            String url = baseUrl + syncDataReceiveVo.getSyncUrl();
            String data = ZptUtil.getDataReceived(url, paramMap);
            JSONObject jsonObject = JSONUtil.parseObj(data);
            Integer resultCode = jsonObject.getInt("resultCode");
            // 请求成功 0 失败 -1
            if (resultCode != 0) {
                log.warn("【数据接收】接口调用失败:{}", taskName);
                return "";
            }
            return data;
        } catch (Exception e) {
            log.error("【数据接收】任务:{},接口调用异常:{}", taskName, e.getMessage());
            return "";
        }
    }

}
