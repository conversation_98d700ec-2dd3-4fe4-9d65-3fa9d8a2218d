package com.js.hszpt.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.hszpt.config.DataReceptionConfig;
import com.js.hszpt.entity.DataReceptionTask;
import com.js.hszpt.entity.DataStorageTask;
import com.js.hszpt.mapper.DataReceptionTaskMapper;
import com.js.hszpt.properties.ZptProperties;
import com.js.hszpt.vo.SyncDataReceiveVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.Date;
import java.util.List;

import static com.js.hszpt.enmus.DataStorageTaskStatus.RECEIVED;
import static com.js.hszpt.enmus.DataStorageTaskStatus.RECEPTION;

@Slf4j
@Service
public class DataReceptionTaskService extends ServiceImpl<DataReceptionTaskMapper, DataReceptionTask> {

    @Autowired
    private DataStorageTaskService dataStorageTaskService;

    @Autowired
    private DataReceptionConfig dataReceptionConfig;

    @Autowired
    private ZptProperties zptProperties;

    /**
     * 按任务名称接收数据
     * 
     * @param taskName 任务名称
     */
    public boolean receptionTask(String taskName) {
        long start = System.currentTimeMillis();
        log.info("【数据接收】任务:{},开始执行:{}", taskName, start);
        // 查询数据接收任务表 获取需要接收数据的任务名称
        DataReceptionTask dataReceptionTask = this.getById(taskName);
        if (dataReceptionTask == null) {
            log.warn("【数据接收】数据接收任务表，查询不到任务配置:{}", taskName);
            return false;
        }
        // 设置请求参数 -> 请求地址、分页每页1000条、请求时间
        long startTime = System.currentTimeMillis();
        SyncDataReceiveVo syncDataReceiveVo = this.buildSyncDataReceiveVo(dataReceptionTask, new Date(startTime));
        // 根据任务参数名称，查询数据存储任务表 0-数据接收中
        List<DataStorageTask> dataStorageTaskList = dataStorageTaskService.getListByTaskNameAndStorageStatus(taskName,
                RECEPTION.getCode());
        // 处理数据存储任务
        boolean reception;
        if (CollUtil.isNotEmpty(dataStorageTaskList)) {
            // 处理接收中数据
            log.info("【数据接收】任务:{},当前数据接收中任务数量:{}", taskName, dataStorageTaskList.size());
            reception = this.receptionData(dataReceptionTask, syncDataReceiveVo, dataStorageTaskList.get(0));
        } else {
            // 第一次接收
            log.info("【数据接收】任务:{},新接收处理任务", taskName);
            reception = this.receptionData(dataReceptionTask, syncDataReceiveVo, null);

        }
        log.info("【数据接收】任务:{},接收状态:{}", taskName, reception);

        long end = System.currentTimeMillis();
        log.info("【数据接收】任务:{},接收任务执行耗时:{}ms", taskName, end - start);
        return reception;
    }

    public static void main(String[] args) {
        long startTime = System.currentTimeMillis() - (5 * 60 * 1000);
        System.out.println("Current time minus 5 minutes: " + new Date(startTime));
        System.out.println("Current time: " + new Date(System.currentTimeMillis()));
    }

    /**
     * 构建请求对象
     * 
     * @param dataReceptionTask 任务对象
     * @param date              当前时间
     * @return 请求对象
     */
    public SyncDataReceiveVo buildSyncDataReceiveVo(DataReceptionTask dataReceptionTask, Date date) {
        SyncDataReceiveVo syncDataReceiveVo = new SyncDataReceiveVo();
        syncDataReceiveVo.setTaskName(dataReceptionTask.getTaskName());
        syncDataReceiveVo.setSyncUrl(dataReceptionTask.getApiEndpoint());
        // 设置时间
        syncDataReceiveVo
                .setStartDate(DateUtil.format(dataReceptionTask.getLastCompletedTime(), "yyyy-MM-dd HH:mm:ss.SSS"));
        syncDataReceiveVo.setEndDate(DateUtil.format(date, "yyyy-MM-dd HH:mm:ss.SSS"));
        log.info("【数据接收】任务:{},当前任务请求信息:{}", dataReceptionTask.getTaskName(), dataReceptionTask);
        return syncDataReceiveVo;
    }

    /**
     * 接收数据
     * 
     * @param syncDataReceiveVo 请求参数
     * @param dataStorageTask   数据存储任务对象
     */
    public boolean receptionData(DataReceptionTask dataReceptionTask, SyncDataReceiveVo syncDataReceiveVo,
            DataStorageTask dataStorageTask) {
        String taskName = syncDataReceiveVo.getTaskName();
        if (dataStorageTask != null) {
            // 处理接受中记录是否接收完数据
            String createFileCount = dataStorageTask.getCreateFileCount();
            if (StrUtil.equals(createFileCount, dataStorageTask.getTotalFileCount())) {
                // 处理完成 更新状态为 1-数据接收完成
                dataStorageTask.setStorageStatus(RECEIVED.getCode());
                // 更新时间
                dataStorageTask.setModityDate(new Date());
                return dataStorageTaskService.updateById(dataStorageTask);
            }
            // 设置重处理的分页参数 每页1000条
            int pageIndex = Integer.parseInt(createFileCount) + 1;
            syncDataReceiveVo.setPageIndex(pageIndex);
            // 设置重处理时间
            syncDataReceiveVo.setStartDate(DateUtil.format(dataStorageTask.getDataStartTime(), "yyyy-MM-dd HH:mm:ss.SSS"));
            syncDataReceiveVo.setEndDate(DateUtil.format(dataStorageTask.getDataEndTime(), "yyyy-MM-dd HH:mm:ss.SSS"));
        } else {
            // 文件根路径 按日期创建
            String baseFilePath = zptProperties.getFilePath() + taskName + "/"
                    + DateUtil.format(new Date(), "yyyyMMddHHmmss");
            // 创建数据存储任务对象
            dataStorageTask = dataStorageTaskService.buildDataStorageTask(syncDataReceiveVo, baseFilePath);
        }
        // 请求接口接收数据
        String data = dataReceptionConfig.getData(syncDataReceiveVo);
        if (StrUtil.isBlank(data)) {
            log.error("【数据接收】任务:{},接收数据为空", taskName);
            return false;
        }
        // 解析分页信息
        JSONObject pageInfo = this.parseData(data, taskName);
        if (pageInfo == null) {
            return false;
        }
        // 文件总数
        int pageCount = pageInfo.getInt("pageCount");
        // 当前页
        int pageIndex = pageInfo.getInt("pageIndex");
        // 当前文件数量
        int count = Integer.parseInt(dataStorageTask.getCreateFileCount());
        // 记录文件总数 有多少页就有多少个文件
        dataStorageTask.setTotalFileCount(String.valueOf(pageCount));
        // 分页抽取数据 并保存
        for (int i = pageIndex; i <= pageCount; i++) {
            // 当前页
            pageIndex = pageInfo.getInt("pageIndex");
            log.info("【数据接收】任务:{},当前抽取页数,总页数:{}", pageIndex, pageCount);
            // 当前文件数量 + 1
            dataStorageTask.setCreateFileCount(String.valueOf(++count));
            // 更新时间
            dataStorageTask.setModityDate(new Date());
            // 保存接收的数据
            boolean save = this.saveReceivedData(data, dataStorageTask, pageIndex == pageCount);
            if (!save) {
                log.info("【数据接收】任务:{},保存文件失败", taskName);
                return false;
            }
            // 获取同步数据最大时间
            String maxReceptionDate = this.getMaxReceptionDate(data, taskName);
            if (StrUtil.isNotBlank(maxReceptionDate)) {




                Date maxDate = DateUtil.parse(maxReceptionDate, "yyyy-MM-dd HH:mm:ss");
                // 判断是否有新数据，如果最大时间和上次完成时间相同（意味着没有新数据）
                if (maxDate.equals(dataReceptionTask.getLastCompletedTime())) {
                    // 添加微小偏移量（例如1毫秒）到上次完成时间，确保不再重复查询同一条数据
                    Date newLastCompletedTime = new Date(maxDate.getTime() + 1);
                    dataReceptionTask.setLastCompletedTime(newLastCompletedTime);
                    log.info("【数据接收】任务:{},当前时间段没有新数据，将完成时间微调为:{}",
                            taskName, DateUtil.format(newLastCompletedTime, "yyyy-MM-dd HH:mm:ss.SSS"));
                } else {
                    // 有新数据，正常更新为最新数据时间
                    dataReceptionTask.setLastCompletedTime(maxDate);
                    log.info("【数据接收】任务:{},更新接收完成时间:{}",
                            taskName, maxReceptionDate);
                }
                this.updateById(dataReceptionTask);
            }
            // 继续接收下一页数据
            syncDataReceiveVo.setPageIndex(++pageIndex);
            data = dataReceptionConfig.getData(syncDataReceiveVo);
            if (StrUtil.isBlank(data)) {
                log.error("【数据接收】任务:{},接收数据为空", taskName);
                return false;
            }
            // 重新解析当前页数据
            pageInfo = this.parseData(data, taskName);
        }
        return true;
    }

    /**
     * 保存接收的数据 写入txt文件 修改数据库当前任务接收记录
     * 
     * @param data            接收的数据
     * @param dataStorageTask 数据存储任务对象
     */
    public boolean saveReceivedData(String data, DataStorageTask dataStorageTask, boolean finished) {
        // 获取分页信息
        String taskName = dataStorageTask.getTaskName();
        boolean saveFile = this.saveFile(data, dataStorageTask.getTargetFolderPath(),
                dataStorageTask.getCreateFileCount());
        if (!saveFile) {
            log.error("【数据接收】任务:{},保存文件失败", taskName);
            return false;
        }
        return this.saveDataStorageTask(dataStorageTask, finished);
    }

    public JSONObject getResultObject(String data, String taskName) {
        // 解析响应体
        JSONObject jsonObject = JSONUtil.parseObj(data);
        // 获取请求结果
        JSONObject resultObject = jsonObject.getJSONObject("resultObject");
        if (resultObject == null) {
            log.error("【数据接收】任务:{},获取请求结果resultObject参数为空", taskName);
            return null;
        }
        return resultObject;
    }

    /**
     * 解析接收数据
     * 
     * @param data     响应数据
     * @param taskName 任务名称
     * @return 分页信息
     */
    public JSONObject parseData(String data, String taskName) {
        JSONObject resultObject = this.getResultObject(data, taskName);
        // 获取分页信息
        JSONObject pageInfo = resultObject.getJSONObject("pageInfo");
        if (pageInfo == null) {
            log.error("【数据接收】任务:{},获取分页信息pageInfo参数为空", taskName);
            return null;
        }
        // 没查到数据 不需要保存
        int total = pageInfo.getInt("total", 0);
        if (total == 0) {
            log.info("【数据接收】任务:{},没有查到数据", taskName);
            return null;
        }
        return pageInfo;
    }

    public String getMaxReceptionDate(String data, String taskName) {
        JSONObject resultObject = this.getResultObject(data, taskName);
        JSONArray jsonArray = resultObject.getJSONArray("list");
        JSONObject jsonObject = jsonArray.getJSONObject(jsonArray.size() - 1);
        String modifyDate = jsonObject.getStr("modifyDate");
        //业务签证官表modify字段名拼写错误，特殊处理
        if("apply_officer_info".equals(taskName)){
            modifyDate = jsonObject.getStr("modityDate");
        }
        //
        if (StrUtil.equals("ctf_certificate_data_regen",taskName)) {
            modifyDate = jsonObject.getStr("updateTime");
        }
        if (StrUtil.isBlank(modifyDate)) {
            String createDate = jsonObject.getStr("createDate");
            log.info("【数据接收】任务:{},获取最大接收时间:{}", taskName, createDate);
            return createDate;
        }
        log.info("【数据接收】任务:{},获取最大接收时间:{}", taskName, modifyDate);
        return modifyDate;
    }

    /**
     * 保存txt文件
     * 
     * @param data     接收的数据报文
     * @param basePath 文件保存根路径
     * @param count    当前文件数量
     * @return 保存成功-true 失败-false
     */
    public boolean saveFile(String data, String basePath, String count) {
        // 将数据保存到本地文件
        String path = basePath + "/" + count + ".temp";
        File file = FileUtil.writeBytes(data.getBytes(), path);
        if (file == null) {
            return false;
        }
        // 将文件名称改成txt后缀
        FileUtil.rename(file, count + ".txt", true);
        return true;
    }

    /**
     * 保存数据接收任务
     * 
     * @param dataStorageTask 数据接收任务
     * @param finished        当前任务是否接收完成
     * @return 保存成功-true 失败-false
     */
    public boolean saveDataStorageTask(DataStorageTask dataStorageTask, boolean finished) {
        if (finished) {
            // 最后一页 更新状态为 1-数据接收完成
            dataStorageTask.setStorageStatus(RECEIVED.getCode());
            log.info("【数据接收】当前接收任务:{},处理完成", dataStorageTask.getTaskName());
        } else {
            log.info("【数据接收】当前接收任务:{},已接收:{}", dataStorageTask.getTaskName(), dataStorageTask.getCreateFileCount());
        }
        return dataStorageTaskService.saveOrUpdate(dataStorageTask);
    }

}
