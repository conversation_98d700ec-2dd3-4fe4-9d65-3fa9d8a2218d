package com.js.hszpt.builder;

import com.js.hszpt.vo.CertificateVo;

import java.util.Collections;
import java.util.Map;

import static com.js.hszpt.constants.OilPollutionWarrantyConstant.OIL_DAMAGE;
import static com.js.hszpt.constants.OilPollutionWarrantyConstant.OIL_DAMAGE_REVERSE;

public class OilDamageCertBuilder implements CertificateInfoBuilder{
    @Override
    public Map<String, String> certInfo() {
        return OIL_DAMAGE;
    }

    @Override
    public Map<String, String> certReverseInfo() {
        return OIL_DAMAGE_REVERSE;
    }

    @Override
    public Map<String, String> buildCertificateAttribute(CertificateVo certificateVo) {
        return Collections.emptyMap();
    }
}
