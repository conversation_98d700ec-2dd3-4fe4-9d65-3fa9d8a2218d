package com.js.hszpt.utils;


import cn.hutool.core.map.MapUtil;
import com.js.hszpt.constants.Common;
import com.js.hszpt.entity.*;
import com.js.hszpt.vo.CertificateData;
import com.js.hszpt.vo.OilPollutionCertificateVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class OilPollutionCertificateUtil {
    public static Map<String, String> createOilDamageInsuranceCert(OilPollutionCertificateVo oilPollutionCertificateVo) {
        try {
            log.info("【燃油污染或非持久性油类污染】生成证照照面属性信息开始");
            // 根据国内航线和国际航线生成不同的证书信息
            log.info("船舶航线类型代码：{}", oilPollutionCertificateVo.getBizDgrOpic().getShipRouteCode());
            if (StringUtils.equals("1", oilPollutionCertificateVo.getBizDgrOpic().getShipRouteCode())) {
                return getNonPersistentOilContaminationMap(oilPollutionCertificateVo);//非持久性油类污染损害民事责任保险或其他财务保证证书
            } else if (StringUtils.equals("2", oilPollutionCertificateVo.getBizDgrOpic().getShipRouteCode())) {
                log.info("证书分类名称：{}", oilPollutionCertificateVo.getBizAffairInsurance().getInsurCertName());
                if (oilPollutionCertificateVo.getBizAffairInsurance().getInsurCertName().contains("非持久性油类污染损害民事责任保险或其他财务保证证书")) {
                    log.info("111111111");
                    return getNonPersistentOilContaminationMap(oilPollutionCertificateVo);//非持久性油类污染损害民事责任保险或其他财务保证证书
                } else {
                    log.info("222222222");
                    if ("燃油污染损害民事责任保险或其他财务保证证书".equals(oilPollutionCertificateVo.getBizAffairInsurance().getInsurCertName())) {
                        log.info("33333333");
                        return getFuelPollutionInsuranceCertMap(oilPollutionCertificateVo);//燃油污染损害民事责任保险或其他财务保证证书
                    } else {
                        log.info("444444444");
                        return getOilDamageInsuranceCertMapEn(oilPollutionCertificateVo);//油污损害民事责任保险或其他财务保证证书
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("【燃油污染或非持久性油类污染】生成证照照面属性信息异常:{}", e.getStackTrace());
        }
        log.info("【燃油污染或非持久性油类污染】生成证照照面属性信息为空");
        return new HashMap<>();
    }

    public static Map<String, String> getFuelPollutionInsuranceCertMap(OilPollutionCertificateVo oilPollutionCertificateVo) {
        //窗口申请表
//        BizAffairApply bizAffairApply = oilPollutionCertificateVo.getBizAffairApply();
        //保险信息表
        BizAffairInsurance bizAffairInsurance = oilPollutionCertificateVo.getBizAffairInsurance();
        //制证校核表
        BizEgCertCheck bizEgCertCheck = oilPollutionCertificateVo.getBizEgCertCheck();
        //业务船舶表
        BizDgrOpic bizDgrOpic = oilPollutionCertificateVo.getBizDgrOpic();
        //审批意见表
        BizEgApprove bizEgApprove = oilPollutionCertificateVo.getBizEgApprove();
        //机构表
//        DictEgOrg dictEgOrg = oilPollutionCertificateVo.getDictEgOrg();
        //港口信息表
        DictEgPort dictEgPort = oilPollutionCertificateVo.getDictEgPort();
        //海事机构部门名称中英文对照表
        SysDeptEn sysDeptEn = oilPollutionCertificateVo.getSysDeptEn();
        //用户表
//        DictEgUser dictEgUser = oilPollutionCertificateVo.getDictEgUser();
        //签证官
        ZwApplyOfficerInfo zwApplyOfficerInfo = oilPollutionCertificateVo.getZwApplyOfficerInfo();


        // 开始日期
        String startDateStr;
        // 截至日期
        String endDateStr;
        // GMT开始日期
        String startDateStrGMT;
        // GMT截至日期
        String endDateStrGMT;
        // 颁证日期
        String dateStart;
        // 开始日期（只有日期中文）
        String dateStartEn;

        String finalNumber = "";
        String certificateName = bizAffairInsurance.getInsurCertName();
        CertificateData certificateData = new CertificateData();

        if (certificateName.equals("油污损害民事责任保险或其他财务保证证书")) {
            certificateData.setCertificateType(certificateName + "（" + "国际" + "）");
            if (null != bizEgCertCheck.getCertNo()) {
                finalNumber = bizEgCertCheck.getCertNo();
            }

        } else if (certificateName.equals("燃油污染损害民事责任保险或其他财务保证证书")) {
            certificateData.setCertificateType(certificateName + "（" + "国际" + "）");
            if (null != bizEgCertCheck.getCertNo()) {
                finalNumber = bizEgCertCheck.getCertNo();
            }
        }

        //北京时间
        if (bizAffairInsurance.getDateType() != null && "1".equals(bizAffairInsurance.getDateType())) {
            startDateStr = bizAffairInsurance.getEffStartDate() != null ?
                    DateUtil.getDateCn(bizAffairInsurance.getEffStartDate()) + DateUtil.getHourCn(bizAffairInsurance.getEffStartHour()) : "--";
            endDateStr = bizAffairInsurance.getDeadLine() != null ?
                    DateUtil.getDateCn(bizAffairInsurance.getDeadLine()) + DateUtil.getHourCn(bizAffairInsurance.getDeadLineHour()) : "--";
        } else {
            // 开始日期
            startDateStr = bizAffairInsurance.getEffStartDate() != null ? "格林威治时间" +
                    DateUtil.getDateCn(bizAffairInsurance.getEffStartDate()) +
                    DateUtil.getHourCn(bizAffairInsurance.getEffStartHour()) : "--";
            // 截至日期
            endDateStr = bizAffairInsurance.getDeadLine() != null ? "格林威治时间" +
                    DateUtil.getDateCn(bizAffairInsurance.getDeadLine()) +
                    DateUtil.getHourCn(bizAffairInsurance.getDeadLineHour()) : "--";
        }
//      英文添加非空判断
        String startDateStrEn = bizAffairInsurance.getEffStartDate() != null?DateUtil.getDateEn(bizAffairInsurance.getEffStartDate()
               + " " + bizAffairInsurance.getEffStartHour(), bizAffairInsurance.getDateType()):"--";
        String endDateStrEn = bizAffairInsurance.getDeadLine()!=null?DateUtil.getDateEn(bizAffairInsurance.getDeadLine()
               + " " + bizAffairInsurance.getDeadLineHour(), bizAffairInsurance.getDateType()):"--";
        String format;
        if (bizEgApprove != null) {
            if (bizEgApprove.getApprDate() != null) {
                ZoneId zoneId = ZoneId.systemDefault();
                ZonedDateTime zonedDateTime = bizEgApprove.getApprDate().toInstant().atZone(zoneId);
                LocalDateTime localDateTime = zonedDateTime.toLocalDateTime();
                format = Common.DateFormat.df.format(bizEgApprove.getApprDate() != null ? localDateTime : LocalDateTime.now());
            } else {
                format = Common.DateFormat.SDF.format(new Date());
            }

        } else {
            format = Common.DateFormat.SDF.format(new Date());
        }
        dateStart = bizAffairInsurance.getDeadLine() != null ? DateUtil.getDateCn(format) : "--";
        dateStartEn = DateUtil.getDateEn(format, bizAffairInsurance.getDateType());
        //GMT开始日期
        startDateStrGMT = startDateStrEn != null ? startDateStrEn : "--";
        // GMT截至日期
        endDateStrGMT = endDateStrEn != null ? endDateStrEn : "--";
        String vRegistryPort = null;
        if (null != bizDgrOpic) {
            // 船籍港中文
            vRegistryPort = bizDgrOpic.getRegportName();
        }
        String portNameEn = null;
        if (null != dictEgPort) {
            portNameEn = dictEgPort.getPortNameEn();
        }
        String ownerCn = StringUtils.isNotBlank(bizDgrOpic.getShipOwner()) ? bizDgrOpic.getShipOwner().replaceAll("&", "＆") : "--";
        String addressCn = StringUtils.isNotBlank(bizDgrOpic.getShipOwnerAddr()) ? bizDgrOpic.getShipOwnerAddr().replaceAll("&", "＆") : "--";
        // 避免空指针
        if (StringUtils.isBlank(bizDgrOpic.getShipOwnerEn())) {
            bizDgrOpic.setShipOwnerEn(" ");
        }
        if (StringUtils.isBlank(bizDgrOpic.getShipOwnerAddrEn())) {
            bizDgrOpic.setShipOwnerAddrEn(" ");
        }
        String addRess = bizDgrOpic.getShipOwnerEn().replaceAll("&", "＆") + "\r\n" + bizDgrOpic.getShipOwnerAddrEn().replaceAll("&", "＆");
        String startAndEndDate = startDateStr + " 至" + endDateStr;
        // 保险机构名称
        String SafetyMechanismName = bizAffairInsurance.getInsurCompName();
        SafetyMechanismName = StringUtils.isNoneBlank(SafetyMechanismName) ? SafetyMechanismName : "--";
        String safetyMechanismEnglishName = bizAffairInsurance.getInsurCompNameEn();
        safetyMechanismEnglishName = StringUtils.isNotBlank(safetyMechanismEnglishName) ? safetyMechanismEnglishName : "";

        String address = StringUtils.isNotBlank(bizAffairInsurance.getInsurCompAddr()) ? bizAffairInsurance.getInsurCompAddr().replaceAll("&", "＆") : "--";
        String addressEn = StringUtils.isNotBlank(bizAffairInsurance.getInsurCompAddrEn()) ? bizAffairInsurance.getInsurCompAddrEn().replaceAll("&", "＆") : "";

        return MapUtil.builder(new HashMap<String, String>())
                .put("结束时间(中文)", endDateStr)
                .put("结束时间(英文)", endDateStrGMT)
                .put("X海事局(中文)", sysDeptEn.getOrgName() != null ? sysDeptEn.getOrgName() : "--")
                .put("X海事局(英文)", sysDeptEn.getOrgNameEn() != null ? sysDeptEn.getOrgNameEn().replaceAll("&", "＆") : "--")
                .put("名称(中文)", SafetyMechanismName)
                .put("地址(中文)", address)
                .put("地点(中文)", sysDeptEn.getOrgAddr() != null ? sysDeptEn.getOrgAddr() : "--")
                .put("地点(英文)", sysDeptEn.getOrgAddrEn() != null ? sysDeptEn.getOrgAddrEn().replaceAll("&", "＆") : "--")
                .put("颁证日期(中文)", dateStart)
                .put("颁证日期(英文)", dateStartEn)
                .put("签证官员(中文)", zwApplyOfficerInfo.getOfficerName() != null ? zwApplyOfficerInfo.getOfficerName() : "--")
                .put("签证官员职务(中文)", zwApplyOfficerInfo.getPosition() != null ? zwApplyOfficerInfo.getPosition() : "--")
                .put("签证官员(英文)", zwApplyOfficerInfo.getOfficialNameEn() != null ? zwApplyOfficerInfo.getOfficialNameEn().replaceAll("&", "＆") : "--")
                .put("签证官员职务(英文)", zwApplyOfficerInfo.getOfficialTitleEn() != null ? zwApplyOfficerInfo.getOfficialTitleEn().replaceAll("&", "＆") : "--")
                .put("中文船名", bizDgrOpic.getShipName() != null ? bizDgrOpic.getShipName() : "--")
                .put("英文船名", bizDgrOpic.getShipNameEn() != null ? bizDgrOpic.getShipNameEn().replaceAll("&", "＆") : "--")
                .put("船舶编号/呼号", bizDgrOpic.getShipCallsign() != null ? bizDgrOpic.getShipCallsign() : "--")
                .put("IMO编号", bizDgrOpic.getShipImo() != null ? bizDgrOpic.getShipImo() : "--")
                .put("船籍港(中文)", vRegistryPort)
                .put("船籍港(英文)", portNameEn != null ? portNameEn.replaceAll(" ","") : "--")
                .put("船舶所有人和地址(中文)", ownerCn + "\r\n" + addressCn)
                .put("船舶所有人和地址(英文)", addRess != null ? addRess : "--")
                .put("保险类别(中文)", "油污和其他保赔责任险")
                .put("保险类别(英文)", "OIL POLLUTION AND INDEMNITY RISKS INCLUDING OIL POLLUTION RISKS")
                .put("保险时间(中文)", startAndEndDate != null ? startAndEndDate : "--")
                .put("保险时间(英文)", "FROM " + startDateStrGMT + " TO " + endDateStrGMT)
                .put("保险机构名称(英文)", safetyMechanismEnglishName)
                .put("保险机构地址(英文)", (addressEn))
                .put("证照编号", finalNumber != null ? finalNumber : "")
                .build();
    }

    public static Map<String, String> getOilDamageInsuranceCertMapEn(OilPollutionCertificateVo oilPollutionCertificateVo) {

        //窗口申请表
//        BizAffairApply bizAffairApply = oilPollutionCertificateVo.getBizAffairApply();
        //保险信息表
        BizAffairInsurance bizAffairInsurance = oilPollutionCertificateVo.getBizAffairInsurance();
        //制证校核表
        BizEgCertCheck bizEgCertCheck = oilPollutionCertificateVo.getBizEgCertCheck();
        //业务船舶表
        BizDgrOpic bizDgrOpic = oilPollutionCertificateVo.getBizDgrOpic();
        //审批意见表
        BizEgApprove bizEgApprove = oilPollutionCertificateVo.getBizEgApprove();
        //机构表
        DictEgOrg dictEgOrg = oilPollutionCertificateVo.getDictEgOrg();
        //港口信息表
        DictEgPort dictEgPort = oilPollutionCertificateVo.getDictEgPort();
        //海事机构部门名称中英文对照表
        SysDeptEn sysDeptEn = oilPollutionCertificateVo.getSysDeptEn();
        //用户表
        DictEgUser dictEgUser = oilPollutionCertificateVo.getDictEgUser();
        //签证官
        ZwApplyOfficerInfo zwApplyOfficerInfo = oilPollutionCertificateVo.getZwApplyOfficerInfo();

        // 开始日期
        String startDateStr;
        // 截至日期
        String endDateStr;
        // GMT开始日期
        String startDateStrGMT;
        // GMT截至日期
        String endDateStrGMT;
        // 颁证日期
        String dateStart;
        // 开始日期（只有日期中文）
        String dateStartEn;

        String finalNumber = "";
        String certificateName = bizAffairInsurance.getInsurCertName();
        CertificateData certificateData = new CertificateData();

        if (certificateName.equals("油污损害民事责任保险或其他财务保证证书")) {
            certificateData.setCertificateType(certificateName + "（" + "国际" + "）");
            if (null != bizEgCertCheck.getCertNo()) {
                finalNumber = bizEgCertCheck.getCertNo();
            }

        } else if (certificateName.equals("燃油污染损害民事责任保险或其他财务保证证书")) {
            certificateData.setCertificateType(certificateName + "（" + "国际" + "）");
            if (null != bizEgCertCheck.getCertNo()) {
                finalNumber = bizEgCertCheck.getCertNo();
            }
        }

        //北京时间
        if (bizAffairInsurance.getDateType() != null && "1".equals(bizAffairInsurance.getDateType())) {
            startDateStr = bizAffairInsurance.getEffStartDate() != null ?
                    DateUtil.getDateCn(bizAffairInsurance.getEffStartDate()) + DateUtil.getHourCn(bizAffairInsurance.getEffStartHour()) : "--";
            endDateStr = bizAffairInsurance.getDeadLine() != null ?
                    DateUtil.getDateCn(bizAffairInsurance.getDeadLine()) + DateUtil.getHourCn(bizAffairInsurance.getDeadLineHour()) : "--";
        } else {
            // 开始日期
            startDateStr = bizAffairInsurance.getEffStartDate() != null ? "格林威治时间" +
                    DateUtil.getDateCn(bizAffairInsurance.getEffStartDate()) +
                    DateUtil.getHourCn(bizAffairInsurance.getEffStartHour()) : "--";
            // 截至日期
            endDateStr = bizAffairInsurance.getDeadLine() != null ? "格林威治时间" +
                    DateUtil.getDateCn(bizAffairInsurance.getDeadLine()) +
                    DateUtil.getHourCn(bizAffairInsurance.getDeadLineHour()) : "--";
        }
        //添加英文非空判断
        String startDateStrEn = bizAffairInsurance.getEffStartDate()!=null?DateUtil.getDateEn(bizAffairInsurance.getEffStartDate()
                + " " + bizAffairInsurance.getEffStartHour(), bizAffairInsurance.getDateType()): "--";
        String endDateStrEn = bizAffairInsurance.getDeadLine()!=null ?  DateUtil.getDateEn(bizAffairInsurance.getDeadLine()
                + " " + bizAffairInsurance.getDeadLineHour(), bizAffairInsurance.getDateType()) : "--";
        String format;
        if (bizEgApprove != null) {
            if (bizEgApprove.getApprDate() != null) {
                ZoneId zoneId = ZoneId.systemDefault();
                ZonedDateTime zonedDateTime = bizEgApprove.getApprDate().toInstant().atZone(zoneId);
                LocalDateTime localDateTime = zonedDateTime.toLocalDateTime();
                format = Common.DateFormat.df.format(bizEgApprove.getApprDate() != null ? localDateTime : LocalDateTime.now());
            } else {
                format = Common.DateFormat.SDF.format(new Date());
            }
        } else {
            format = Common.DateFormat.SDF.format(new Date());
        }
        dateStart = bizAffairInsurance.getDeadLine() != null ? DateUtil.getDateCn(format) : "--";
        dateStartEn = DateUtil.getDateEn(format, bizAffairInsurance.getDateType());
        //GMT开始日期
        startDateStrGMT = startDateStrEn != null ? startDateStrEn : "--";
        // GMT截至日期
        endDateStrGMT = endDateStrEn != null ? endDateStrEn : "--";
        String vRegistryPort = null;
        if (null != bizDgrOpic) {
            // 船籍港中文
            vRegistryPort = bizDgrOpic.getRegportName();
        }
        String portNameEn = null;
        if (null != dictEgPort) {
            portNameEn = dictEgPort.getPortNameEn();
        }
        String ownerCn = StringUtils.isNotBlank(bizDgrOpic.getShipOwner()) ? bizDgrOpic.getShipOwner().replaceAll("&", "＆") : "--";
        String addressCn = StringUtils.isNotBlank(bizDgrOpic.getShipOwnerAddr()) ? bizDgrOpic.getShipOwnerAddr().replaceAll("&", "＆") : "--";
        // 避免空指针
        if (StringUtils.isBlank(bizDgrOpic.getShipOwnerEn())) {
            bizDgrOpic.setShipOwnerEn(" ");
        }
        if (StringUtils.isBlank(bizDgrOpic.getShipOwnerAddrEn())) {
            bizDgrOpic.setShipOwnerAddrEn(" ");
        }
        String addRess = bizDgrOpic.getShipOwnerEn().replaceAll("&", "＆") + "\r\n" + bizDgrOpic.getShipOwnerAddrEn().replaceAll("&", "＆");
        String startAndEndDate = startDateStr + " 至" + endDateStr;
        // 保险机构名称
        String SafetyMechanismName = bizAffairInsurance.getInsurCompName();
        StringBuilder safetyMechanism = new StringBuilder();
        safetyMechanism.append(SafetyMechanismName != null ? SafetyMechanismName : "");
        String safetyMechanismEnglishName = bizAffairInsurance.getInsurCompNameEn();
        safetyMechanism.append("\r\n").append(StringUtils.isNotBlank(safetyMechanismEnglishName) ? safetyMechanismEnglishName : "");

        String address = StringUtils.isNotBlank(bizDgrOpic.getShipOwnerAddr()) ? bizDgrOpic.getShipOwnerAddr().replaceAll("&", "＆") : "";
        String addressEn = StringUtils.isNotBlank(bizDgrOpic.getShipOwnerAddrEn()) ? bizDgrOpic.getShipOwnerAddrEn().replaceAll("&", "＆") : "";

        String ownerEn = StringUtils.isNotBlank(bizDgrOpic.getShipOwnerEn()) ? bizDgrOpic.getShipOwnerEn().replaceAll("&", "＆") : "--";
//        String addressEn = StringUtils.isNotBlank(bizAffairInsurance.getInsurCompAddrEn()) ? bizAffairInsurance.getInsurCompAddrEn().replaceAll("&", "＆") : "--";

        return MapUtil.builder(new HashMap<String, String>())
                .put("结束时间(中文)", endDateStr)
                .put("结束时间(英文)", endDateStrGMT)
                .put("X海事局(中文)", sysDeptEn.getOrgName() != null ? sysDeptEn.getOrgName() : "--")
                .put("X海事局(英文)", sysDeptEn.getOrgNameEn() != null ? sysDeptEn.getOrgNameEn().replaceAll("&", "＆") : "--")
                .put("地点(中文)", sysDeptEn.getOrgAddr() != null ? sysDeptEn.getOrgAddr() : "--")
                .put("地点(英文)", sysDeptEn.getOrgAddrEn() != null ? sysDeptEn.getOrgAddrEn().replaceAll("&", "＆") : "--")
                .put("颁证日期(中文)", dateStart)
                .put("颁证日期(英文)", dateStartEn)
                .put("签证官员(中文)", zwApplyOfficerInfo.getOfficerName() != null ? zwApplyOfficerInfo.getOfficerName() : "--")
                .put("签证官员职务(中文)", zwApplyOfficerInfo.getPosition() != null ? zwApplyOfficerInfo.getPosition() : "--")
                .put("签证官员(英文)", zwApplyOfficerInfo.getOfficialNameEn() != null ? zwApplyOfficerInfo.getOfficialNameEn().replaceAll("&", "＆") : "--")
                .put("签证官员职务(英文)", zwApplyOfficerInfo.getOfficialTitleEn() != null ? zwApplyOfficerInfo.getOfficialTitleEn().replaceAll("&", "＆") : "--")
                .put("中文船名", bizDgrOpic.getShipName() != null ? bizDgrOpic.getShipName() : "--")
                .put("英文船名", bizDgrOpic.getShipNameEn() != null ? bizDgrOpic.getShipNameEn() : "--")
                .put("船舶编号", bizDgrOpic.getShipCallsign() != null ? bizDgrOpic.getShipCallsign() : "--")
                .put("IMO编号", bizDgrOpic.getShipImo() != null ? bizDgrOpic.getShipImo() : "--")
                .put("船籍港(中文)", (vRegistryPort != null ? vRegistryPort : "--"))
                .put("船籍港(英文)", portNameEn != null ? portNameEn.replaceAll(" ","").replaceAll("&", "＆") : "--")
                .put("船舶所有人和地址(中文)", ownerCn + "\r\n" + addressCn)
                .put("船舶所有人和地址(英文)", ownerEn + "\r\n" + addressEn)
                .put("保险类别(中文)", "油污和其他保赔责任险")
                .put("保险类别(英文)", "OIL POLLUTION AND INDEMNITY RISKS INCLUDING OIL POLLUTION RISKS")
                .put("保险时间(中文)", startAndEndDate != null ? startAndEndDate : "--")
                .put("保险时间(英文)", "FROM " + startDateStrGMT + " TO " + endDateStrGMT)
                .put("保险机构名称(中文)", SafetyMechanismName != null ? SafetyMechanismName : "--")
                .put("保险机构名称(英文)", bizAffairInsurance.getInsurCompNameEn() != null ? bizAffairInsurance.getInsurCompNameEn().replaceAll("&", "＆") : "--")
                .put("保险机构地址(中文)", bizAffairInsurance.getInsurCompAddr() != null ? bizAffairInsurance.getInsurCompAddr() : "--")
                .put("保险机构地址(英文)", bizAffairInsurance.getInsurCompAddrEn() != null ? bizAffairInsurance.getInsurCompAddrEn().replaceAll("&", "＆") : "--")
                .put("证照编号", finalNumber != null ? finalNumber : "")
                .build();
    }

    public static Map<String, String> getNonPersistentOilContaminationMap(OilPollutionCertificateVo oilPollutionCertificateVo) {

        //窗口申请表
//        BizAffairApply bizAffairApply = oilPollutionCertificateVo.getBizAffairApply();
        //保险信息表
        BizAffairInsurance bizAffairInsurance = oilPollutionCertificateVo.getBizAffairInsurance();
        //制证校核表
        BizEgCertCheck bizEgCertCheck = oilPollutionCertificateVo.getBizEgCertCheck();
        //业务船舶表
        BizDgrOpic bizDgrOpic = oilPollutionCertificateVo.getBizDgrOpic();
        //审批意见表
        BizEgApprove bizEgApprove = oilPollutionCertificateVo.getBizEgApprove();
        //机构表
//        DictEgOrg dictEgOrg = oilPollutionCertificateVo.getDictEgOrg();
        //港口信息表
//        DictEgPort dictEgPort = oilPollutionCertificateVo.getDictEgPort();
        //海事机构部门名称中英文对照表
        SysDeptEn sysDeptEn = oilPollutionCertificateVo.getSysDeptEn();
        //用户表
//        DictEgUser dictEgUser = oilPollutionCertificateVo.getDictEgUser();
        //签证官
//        OfficerInfo officerInfo = oilPollutionCertificateVo.getOfficerInfo();

        String finalNumber;
        finalNumber = bizEgCertCheck.getCertNo();
        String certificateName = bizAffairInsurance.getInsurCertName();
        CertificateData certificateData = new CertificateData();
        if (certificateName.equals("油污损害民事责任保险或其他财务保证证书")) {
            certificateData.setCertificateType(certificateName + "（" + "国内" + "）");
            if (null != bizEgCertCheck.getCertNo()) {
                finalNumber = bizEgCertCheck.getCertNo();
            }
        } else if (certificateName.equals("燃油污染损害民事责任保险或其他财务保证证书")) {
            certificateData.setCertificateType(certificateName + "（" + "国内" + "）");
            if (null != bizEgCertCheck.getCertNo()) {
                finalNumber = bizEgCertCheck.getCertNo();
            }
        } else if (certificateName.equals("非持久性油类污染损害民事责任保险或其他财务保证证书")) {
            // 证照类型
            certificateData.setCertificateType(certificateName);
            if (null != bizEgCertCheck.getCertNo()) {
                finalNumber = bizEgCertCheck.getCertNo();
            }
        }
        String startDateStr;
        String endDateStr;
        String dateStart;
        // 开始日期（只有日期中文）
        if (null != bizAffairInsurance.getDateType() && "1".equals(bizAffairInsurance.getDateType())) {
            // 北京时间
            // 开始日期
            startDateStr = bizAffairInsurance.getEffStartDate() != null ?
                    DateUtil.getDateCn(bizAffairInsurance.getEffStartDate()) +
                            DateUtil.getHourCn(bizAffairInsurance.getEffStartHour()) : "--";
            // 截至日期
            endDateStr = bizAffairInsurance.getDeadLine() != null ?
                    DateUtil.getDateCn(bizAffairInsurance.getDeadLine()) +
                            DateUtil.getHourCn(bizAffairInsurance.getDeadLineHour()) : "--";
        } else {
            // 开始日期
            startDateStr = bizAffairInsurance.getEffStartDate() != null ? "格林威治时间" +
                    DateUtil.getDateCn(bizAffairInsurance.getEffStartDate()) +
                    DateUtil.getHourCn(bizAffairInsurance.getEffStartHour()) : "--";
            // 截至日期
            endDateStr = bizAffairInsurance.getDeadLine() != null ? "格林威治时间" +
                    DateUtil.getDateCn(bizAffairInsurance.getDeadLine()) +
                    DateUtil.getHourCn(bizAffairInsurance.getDeadLineHour()) : "--";
        }
        String format;
        if (bizEgApprove != null) {
            ZoneId zoneId = ZoneId.systemDefault();
            ZonedDateTime zonedDateTime = bizEgApprove.getApprDate().toInstant().atZone(zoneId);
            LocalDateTime localDateTime = zonedDateTime.toLocalDateTime();
            format = Common.DateFormat.df.format(bizEgApprove.getApprDate() != null ? localDateTime : LocalDateTime.now());
        } else {
            format = Common.DateFormat.SDF.format(new Date());
        }
        dateStart = bizAffairInsurance.getDeadLine() != null ?
                DateUtil.getDateCn(format) : "--";
        // 日期
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        StringBuffer sb = new StringBuffer();
        //证照编号
        certificateData.setCertificateNumber(finalNumber);


        return MapUtil.builder(new HashMap<String, String>())
                .put("结束时间", endDateStr)
                .put("X海事局", sysDeptEn.getOrgName())
                .put("地点", sysDeptEn.getOrgAddr())
                .put("颁证日期", dateStart)
                .put("中文船名", bizDgrOpic.getShipName() != null ? bizDgrOpic.getShipName() : "--")
                .put("船舶编号", bizDgrOpic.getShipCallsign() != null ? bizDgrOpic.getShipCallsign() : "--")
                .put("IMO编号", bizDgrOpic.getShipImo() != null ? bizDgrOpic.getShipImo() : "--")
                .put("船籍港", bizDgrOpic.getRegportName() != null ? bizDgrOpic.getRegportName() : "--")
                .put("船舶所有人和地址", bizDgrOpic.getShipOwner() + "\r\n" + bizDgrOpic.getShipOwnerAddr() != null ? bizDgrOpic.getShipOwner() + "\r\n" + bizDgrOpic.getShipOwnerAddr() : "--")
                .put("保险类别", "油污和其他保赔责任险")
                .put("保险时间", startDateStr + " 至 " + endDateStr)
                .put("保险机构名称", bizAffairInsurance.getInsurCompName() != null ? bizAffairInsurance.getInsurCompName() : "")
                .put("保险机构地址", bizAffairInsurance.getInsurCompAddr() != null ? bizAffairInsurance.getInsurCompAddr().replaceAll("&", "＆") : "")
                .put("保险机构地址2", "")
                .put("证照编号", finalNumber != null ? finalNumber : "")
                .build();
    }
}
