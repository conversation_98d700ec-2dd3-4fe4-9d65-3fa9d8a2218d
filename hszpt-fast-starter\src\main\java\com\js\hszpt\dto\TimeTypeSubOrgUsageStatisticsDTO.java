package com.js.hszpt.dto;

import lombok.Data;

/**
 * 年度下级机构使用统计结果DTO
 */
@Data
public class TimeTypeSubOrgUsageStatisticsDTO {

    /**
     * 机构编码
     */
    private String orgCode;

    /**
     * 机构类型
     */
    private String orgType;
    
    /**
     * 机构名称
     */
    private String orgName;
    
    /**
     * 年份
     */
    private String year;

    /**
     * 时间维度
     */
    private String timePoint;
    
    /**
     * 使用数量总和
     */
    private Long usageCount;

    /**
     * 使用数量占比（百分比）
     */
    private Double usageRatio;
} 