package com.js.hszpt.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.sql.Timestamp;
import java.util.Date;

/**
 * 外网查询结果VO
 */
@Data
public class certificateExternalNetworkQueryVo {
    // ID字段
    private String id;

    private String certificateId;

    // 事项名称
    private String affairName;

    // 申请人
    private String applicantName;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") // 用于Spring MVC绑定
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")    // 用于JSON序列化 timestamp
    private Timestamp applyDate;

    // 船舶名称
    private String shipName;

    // IMO船舶识别号
    private String shipImo;

    // 船舶名称英文
    private String shipNameEn;

    // 证书名称
    private String certificateName;
//    private String certificateNameEn;

    // 颁发机构中文名称
    private String issuOrgNameCn;
    //签发机构code
    private String issuOrgNameEn;

    // 证书编号
    private String certificateNum;

    // 持有人姓名（新增字段）
    private String holderName;

    // 签发日期
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") // 用于Spring MVC绑定
    @JsonFormat(pattern = "yyyy-MM-dd")    // 用于JSON序列化
    private Date issueDate;

    // 生效日期(表：有效期日期-开始)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") // 用于Spring MVC绑定
    @JsonFormat(pattern = "yyyy-MM-dd")    // 用于JSON序列化
    private Date effectDate;

    // 截止日期(表：有效期日期-结束)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") // 用于Spring MVC绑定
    @JsonFormat(pattern = "yyyy-MM-dd")    // 用于JSON序列化
    private Date expireDate;

    // 证照状态
    private String statusFlag;

    // 新增字段
    private String certPrintNo;            // 证书印刷号
    private String holderIdentityNumber;   // 身份证号
    private String certificateTypeCode;    // 证照类型代码
    private String certificateType;        // 证照类型名称

    // 通知内容
    private String noticeContent;
}
