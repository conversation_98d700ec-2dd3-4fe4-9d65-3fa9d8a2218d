package com.js.hszpt.entity;

import com.js.core.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.Builder;
import java.util.Date;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import com.js.util.SnowFlakeUtil;


/**
 * 
 * @ClassName:  LawWorkflowNodeInfo   
 * @Description:TODO(工作流模板节点表)   
 * @author:   System Generation 
 */
@Data

@TableName("LAW_WORKFLOW_NODE_INFO")
@ApiModel(value = "工作流模板节点表")
public class LawWorkflowNodeInfo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "LAW_WORKFLOW_NODE_INFO_ID", type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value = "主键ID")
    private String id = String.valueOf(SnowFlakeUtil.nextId());

    /**
    * 关联ID
    */
    @ApiModelProperty(value = "关联ID")
    private String lawWorkflowNodeInfoId;


    /**
    * 工作流模板配置表ID
    */
    @ApiModelProperty(value = "工作流模板配置表ID")
    private String lawWorkflowNodeSetId;


    /**
    * 节点代码
    */
    @ApiModelProperty(value = "节点代码")
    private String nodeCode;


    /**
    * 节点名称
    */
    @ApiModelProperty(value = "节点名称")
    private String nodeName;


    /**
    * 是否必填节点 0-否 1-是
    */
    @ApiModelProperty(value = "是否必填节点 0-否 1-是")
    private String nodeIsSelect;


    /**
    * 节点顺序
    */
    @ApiModelProperty(value = "节点顺序")
    private Integer nodeSort;


    /**
    * 节点机构层级 1-部局 2-直属局 3-分支局 4-海事处
    */
    @ApiModelProperty(value = "节点机构层级 1-部局 2-直属局 3-分支局 4-海事处")
    private Integer nodeOrgLevel;


    /**
    * 节点类型（字段已废弃，从前端组件表取）
    */
    @ApiModelProperty(value = "节点类型（字段已废弃，从前端组件表取）")
    private Integer nodeType;


    /**
    * 创建人
    */
    @ApiModelProperty(value = "创建人")
    private String createOperId;


    /**
    * 创建部门
    */
    @ApiModelProperty(value = "创建部门")
    private String createOperDept;


    /**
    * 修改人
    */
    @ApiModelProperty(value = "修改人")
    private String modifyOperId;


    /**
    * 修改部门
    */
    @ApiModelProperty(value = "修改部门")
    private String modifyOperDept;


    /**
    * 修改时间
    */
    @ApiModelProperty(value = "修改时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;


    /**
    * 版本号
    */
    @ApiModelProperty(value = "版本号")
    private String recordVersion;


    /**
    * 数据来源标识：0-系统新增,1-数据迁移
    */
    @ApiModelProperty(value = "数据来源标识：0-系统新增,1-数据迁移")
    private String dataFlag;


}