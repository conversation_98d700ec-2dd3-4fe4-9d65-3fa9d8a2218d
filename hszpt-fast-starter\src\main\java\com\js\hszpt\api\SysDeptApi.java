package com.js.hszpt.api;


import com.js.hszpt.entity.SysDeptIamb;
import com.js.hszpt.service.SysDeptIambService;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.js.core.api.BaseApiPlus;
import com.js.core.common.utils.ResultUtil;
import com.js.core.common.vo.PageVo;
import com.js.core.common.vo.Result;
import com.js.core.common.vo.SearchVo;
import com.js.annotation.SystemLog;
import com.js.enums.LogType;
import com.js.hszpt.service.SysUserService;
import com.js.hszpt.vo.SysDeptInfoResponse;import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 *
 * @ClassName: SysDeptIambApi
 * @Description:TODO(组织信息表接口)
 * @author:  System Generation
 *
 */
@Slf4j
@RestController
@Api(description = "组织信息表接口")
@RequestMapping("/sysDept")
public class SysDeptApi extends BaseApiPlus<SysDeptIambService,SysDeptIamb,String> {

	@Autowired
	private SysDeptIambService sysDeptIambService;


    /**
     * 获取机构树形状接口
     *
     * @param orgCode 机构编码
     * @param flag 1 查本级 ， 2查下级
     * @return
     */
    @GetMapping("/deptSon")
    public Result<List<SysDeptInfoResponse>> getSysDept(@RequestParam("orgCode") String orgCode, @RequestParam("flag") String flag) {
        return Result.success(sysDeptIambService.deptSonApp(orgCode,flag));
    }

}
