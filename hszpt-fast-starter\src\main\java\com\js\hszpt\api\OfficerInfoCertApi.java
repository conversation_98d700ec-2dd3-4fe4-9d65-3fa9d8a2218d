package com.js.hszpt.api;


import com.js.hszpt.entity.OfficerInfoCert;
import com.js.hszpt.service.OfficerInfoCertService;
 
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.js.core.api.BaseApiPlus;
import com.js.core.common.utils.ResultUtil;
import com.js.core.common.vo.PageVo;
import com.js.core.common.vo.Result;
import com.js.core.common.vo.SearchVo;
import com.js.annotation.SystemLog;
import com.js.enums.LogType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import java.util.List;


 /**
 * 
 * @ClassName: OfficerInfoCertApi  
 * @Description:TODO(接口)   
 * @author:  System Generation  
 *
 */
@Slf4j
@RestController
@Api(description = "接口")
@RequestMapping("/officerInfoCert")
public class OfficerInfoCertApi extends BaseApiPlus<OfficerInfoCertService,OfficerInfoCert,String>{

	@SystemLog(description = "-分页查询", type = LogType.OPERATION)
	@RequestMapping(value = "/getPage", method = RequestMethod.GET)
	@ApiOperation(value = "分页查询")
	public Result<Page<OfficerInfoCert>> getPage(@ModelAttribute OfficerInfoCert param, @ModelAttribute SearchVo searchVo, @ModelAttribute PageVo pageVo) {
		Page<OfficerInfoCert> page = this.baseService.findByCondition(param, searchVo, pageVo);
		return ResultUtil.data(page);
	}
	
	@SystemLog(description = "-列表查询", type = LogType.OPERATION)
	@RequestMapping(value = "/getList", method = RequestMethod.GET)
	@ApiOperation(value = "列表查询")
	public Result<List<OfficerInfoCert>> getList(@ModelAttribute OfficerInfoCert param, @ModelAttribute SearchVo searchVo) {
		List<OfficerInfoCert> list = this.baseService.findByCondition(param, searchVo);
		return ResultUtil.data(list);
	}
	
}
