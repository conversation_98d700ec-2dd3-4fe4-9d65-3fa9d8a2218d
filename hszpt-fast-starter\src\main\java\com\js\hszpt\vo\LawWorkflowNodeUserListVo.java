package com.js.hszpt.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/8/12 16:04
 */
@ApiModel("工作流用户信息查询列表")
public class LawWorkflowNodeUserListVo{

    private static final long serialVersionUID = -9212073066769747249L;

    @ApiModelProperty("工作流用户信息ID")
    private String lawWorkflowNodeUserId;

    @ApiModelProperty("用户名称")
    private String userName;

    @ApiModelProperty("用户id")
    private String userCode;

    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    @ApiModelProperty("机构名称")
    private String orgName;

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @ApiModelProperty("所属部门")
    private String departmentName;


    @ApiModelProperty("操作类型")
    private String type;


    public String getCreateOperId() {
        return createOperId;
    }

    public void setCreateOperId(String createOperId) {
        this.createOperId = createOperId;
    }

    @ApiModelProperty("创建人")
    private String createOperId;

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @ApiModelProperty("创建时间")
    private Date createTime;

    public String getNodeName() {
        return nodeName;
    }

    public void setNodeName(String nodeName) {
        this.nodeName = nodeName;
    }

    public String getItemsName() {
        return itemsName;
    }

    public void setItemsName(String itemsName) {
        this.itemsName = itemsName;
    }

    @ApiModelProperty("节点名称")
    private String nodeName;

    @ApiModelProperty("模板名称")
    private String itemsName;
    public String getLawWorkflowNodeUserId() {
        return lawWorkflowNodeUserId;
    }

    public void setLawWorkflowNodeUserId(String lawWorkflowNodeUserId) {
        this.lawWorkflowNodeUserId = lawWorkflowNodeUserId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

}
