package com.js.hszpt.entity;

import com.js.core.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.js.util.SnowFlakeUtil;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.Builder;
import java.util.Date;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;


/**
 * 
 * @ClassName:  CertTypeApproval   
 * @Description:TODO(证照类型目录审批表)   
 * @author:   System Generation 
 */
@Data

@TableName("CERT_TYPE_APPROVAL")
@ApiModel(value = "证照类型目录审核审批表")
public class CertTypeApproval extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 审批记录主键ID
     */
    @TableId(value = "CERT_TYPE_APPR_ID", type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value = "审批记录主键ID")
    private String id = String.valueOf(SnowFlakeUtil.nextId());

    /**
    * 证照类型目录ID
    */
    @ApiModelProperty(value = "证照类型目录ID")
    private String certTypeDirId;

    /**
     * 证照类型目录审批申请ID
     */
    @ApiModelProperty(value = "证照类型目录审批申请ID")
    private String certTypeApprApplyId;

    /**
    * 申请人ID
    */
    @ApiModelProperty(value = "申请人ID")
    private String applicantId;


    /**
    * 申请人姓名
    */
    @ApiModelProperty(value = "申请人姓名")
    private String applicantName;


    /**
    * 申请提交时间
    */
    @ApiModelProperty(value = "申请提交时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date applyTime;


    /**
    * 当前审批环节：1-申请、2-审核、3-审批
    */
    @ApiModelProperty(value = "当前审批环节：1-申请、2-审核、3-审批")
    private String currentNode;

    /**
    * 审批结果：0-草稿 1-通过 2-不通过 3-退回
    */
    @ApiModelProperty(value = "审批结果：0-草稿 1-通过 2-不通过 3-退回")
    private String approvalResult;


    /**
    * 审批意见
    */
    @ApiModelProperty(value = "审批意见")
    private String approvalOpinion;


    /**
    * 审批完成时间
    */
    @ApiModelProperty(value = "审批完成时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date completeTime;

    /**
    * 删除标志：0-正常，1-已删除
    */
    @ApiModelProperty(value = "删除标志：0-正常，1-已删除")
    private String delFlag;

    /**
     * 处理状态：1-提交 2-保存
     */
    @ApiModelProperty(value = "处理状态：1-提交 2-保存")
    private String handleStatus;
}