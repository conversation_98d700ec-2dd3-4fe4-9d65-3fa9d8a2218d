package com.js.hszpt.vo;

import com.js.hszpt.entity.*;
import lombok.Data;

@Data
public class CertificateVo {

    // 办件信息
    private BizAffairApply bizAffairApply;
    // 用户信息
    private DictEgUser dictEgUser;
    // 机构信息
    private DictEgOrg dictEgOrg;
    // 保险信息
    private BizAffairInsurance bizAffairInsurance;
    // 机构中英文对照表
    private SysDeptEn sysDeptEn;
    // 审批信息
    private BizEgApprove bizEgApprove;
    // 港口信息
    private DictEgPort dictEgPort;
    // 签证官信息
    private ZwApplyOfficerInfo zwApplyOfficerInfo;
    // 证书信息
    private BizEgCertCheck bizEgCertCheck;
    // 证书类型
    private CertificateType certificateType;
    // 船舶信息
    private BizDgrOpic bizDgrOpic;
    // 申请受理审批信息
    private BizEgApplyInfo bizEgApplyInfo;
    // 船舶基本信息
    private ShipInfoAll shipInfoAll;
    // 船舶证书-所有权证书
    private CertShipOwnership certShipOwnership;
    // 受理信息
    private BizEgAccept bizEgAccept;
}
