package com.js.hszpt.interceptor;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web MVC配置类
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Autowired
    private ZhhsAuthInterceptor zhhsAuthInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册拦截器，拦截所有API请求
        registry.addInterceptor(zhhsAuthInterceptor)
                .addPathPatterns("/certAccessLog/**")
                .addPathPatterns("/certificate/**")
                .addPathPatterns("/certApprovalApply/**")
                .addPathPatterns("/certificate/statistics/**")
                .addPathPatterns("//certificate/usage/statistics/**")
                .addPathPatterns("/certTypeApproval/**")
                .addPathPatterns("/certTypeBatchApproval/**")
                .addPathPatterns("/certTypeBatchApprovalDetail/**")
                .addPathPatterns("/certTypeBatchOperation/**")
                .addPathPatterns("/certTypeBatchOperationDetail/**")
                .addPathPatterns("/certTypeDirectory/**")
                .addPathPatterns("/certTypeDirSeq/**")
                .addPathPatterns("/ctfAffair/**")
                .addPathPatterns("/ctfCertificate/**")
                .addPathPatterns("/ctfCertificateType/**")
                .addPathPatterns("/lawWorkflowBpm/**")
                .addPathPatterns("/lawWorkflowNodeComponent/**")
                .addPathPatterns("/lawWorkflowNodeInfo/**")
                .addPathPatterns("/lawWorkflowNodeOrg/**")
                .addPathPatterns("/lawWorkflowNodeSet/**")
                .addPathPatterns("/lawWorkflowNodeUser/**");
    }
}
