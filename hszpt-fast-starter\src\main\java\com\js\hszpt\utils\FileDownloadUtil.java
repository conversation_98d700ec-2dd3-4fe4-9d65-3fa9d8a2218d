package com.js.hszpt.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.FileOutputStream;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * 文件下载工具类
 * 解决文件句柄泄露问题，确保所有资源都被正确关闭
 *
 * 资源关闭保证：
 * 1. try-with-resources 语法确保所有实现了AutoCloseable的资源都会被自动关闭
 * 2. 即使发生异常，资源仍会在finally块中被关闭
 * 3. 关闭顺序：内层资源先关闭，外层资源后关闭
 */
@Component
@Slf4j
public class FileDownloadUtil {

    // 使用共享的HttpClient，避免每次创建新实例
    @Autowired
    private CloseableHttpClient httpClient;

    /**
     * 下载文件到指定路径
     * 使用try-with-resources确保所有资源都被正确关闭，防止句柄泄露
     * 
     * @param fileUrl 文件URL
     * @param destinationPath 目标文件路径
     * @return 下载是否成功
     */
    public boolean downloadFile(String fileUrl, String destinationPath) {
        log.info("开始下载文件，URL: {}", fileUrl);

        // 🔑 关键修复：使用共享的HttpClient，只需要关闭response和流
        // HttpClient由Spring容器管理，不需要在这里关闭
        try {
            HttpGet httpGet = new HttpGet(fileUrl);

            try (CloseableHttpResponse response = httpClient.execute(httpGet)) {
                int statusCode = response.getStatusLine().getStatusCode();
                
                if (statusCode == 200) {
                    log.info("文件下载成功，开始写入到: {}", destinationPath);
                    
                    // 🔑 关键修复：使用try-with-resources确保流被正确关闭
                    // 资源关闭保证：
                    // 1. outputStream.close() - 确保文件完全写入磁盘
                    // 2. inputStream.close() - 释放网络连接资源
                    // 3. 即使发生异常，这两个流都会被自动关闭
                    try (InputStream inputStream = response.getEntity().getContent();
                         FileOutputStream outputStream = new FileOutputStream(destinationPath)) {

                        // 优化：使用更大的缓冲区提高性能
                        byte[] buffer = new byte[8192];
                        int bytesRead;
                        long totalBytes = 0;

                        while ((bytesRead = inputStream.read(buffer)) != -1) {
                            outputStream.write(buffer, 0, bytesRead);
                            totalBytes += bytesRead;
                        }

                        log.info("文件写入完成: {}, 大小: {} bytes", destinationPath, totalBytes);
                        return true;
                        
                    } catch (Exception e) {
                        log.error("文件写入失败: {}", e.getMessage(), e);
                        // 清理可能创建的不完整文件
                        cleanupIncompleteFile(destinationPath);
                        return false;
                    }
                } else {
                    log.error("下载文件失败，HTTP状态码: {}", statusCode);
                    return false;
                }
            }
        } catch (Exception e) {
            log.error("文件下载失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 下载文件并返回下载信息
     * 
     * @param fileUrl 文件URL
     * @param destinationPath 目标文件路径
     * @return 下载结果信息
     */
    public DownloadResult downloadFileWithResult(String fileUrl, String destinationPath) {
        long startTime = System.currentTimeMillis();
        boolean success = downloadFile(fileUrl, destinationPath);
        long duration = System.currentTimeMillis() - startTime;
        
        DownloadResult result = new DownloadResult();
        result.setSuccess(success);
        result.setFileUrl(fileUrl);
        result.setDestinationPath(destinationPath);
        result.setDurationMs(duration);
        
        if (success) {
            try {
                result.setFileSize(Files.size(Paths.get(destinationPath)));
            } catch (Exception e) {
                log.warn("获取文件大小失败: {}", e.getMessage());
            }
        }
        
        return result;
    }

    /**
     * 清理不完整的文件
     */
    private void cleanupIncompleteFile(String filePath) {
        try {
            Files.deleteIfExists(Paths.get(filePath));
            log.info("已清理不完整文件: {}", filePath);
        } catch (Exception e) {
            log.warn("清理不完整文件失败: {}", e.getMessage());
        }
    }

    /**
     * 下载结果信息
     */
    public static class DownloadResult {
        private boolean success;
        private String fileUrl;
        private String destinationPath;
        private long fileSize;
        private long durationMs;

        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public String getFileUrl() { return fileUrl; }
        public void setFileUrl(String fileUrl) { this.fileUrl = fileUrl; }
        
        public String getDestinationPath() { return destinationPath; }
        public void setDestinationPath(String destinationPath) { this.destinationPath = destinationPath; }
        
        public long getFileSize() { return fileSize; }
        public void setFileSize(long fileSize) { this.fileSize = fileSize; }
        
        public long getDurationMs() { return durationMs; }
        public void setDurationMs(long durationMs) { this.durationMs = durationMs; }
        
        @Override
        public String toString() {
            return String.format("DownloadResult{success=%s, fileSize=%d bytes, duration=%d ms}",
                    success, fileSize, durationMs);
        }
    }

    /**
     * 验证资源关闭的测试方法
     * 用于确认try-with-resources确实会关闭所有资源
     */
    public void demonstrateResourceClosure() {
        log.info("=== try-with-resources 资源关闭演示 ===");
        log.info("1. HttpClient: 由Spring容器管理，应用关闭时自动关闭");
        log.info("2. CloseableHttpResponse: try-with-resources自动调用close()");
        log.info("3. InputStream: try-with-resources自动调用close()");
        log.info("4. FileOutputStream: try-with-resources自动调用close()");
        log.info("5. 关闭顺序: 内层资源先关闭，外层资源后关闭");
        log.info("6. 异常安全: 即使发生异常，所有资源仍会被关闭");
        log.info("=== 资源关闭保证完毕 ===");
    }
}
