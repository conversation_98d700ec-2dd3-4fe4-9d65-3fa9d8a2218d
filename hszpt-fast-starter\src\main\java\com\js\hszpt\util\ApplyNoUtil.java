package com.js.hszpt.util;

import com.js.redis.starter.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

import static jdk.nashorn.internal.runtime.regexp.joni.Config.log;

@Slf4j
@Component
public class ApplyNoUtil {

    private static RedisUtil redisUtil;

    private static final String APPLY_KEY_PREFIX = "CERT_";

    private static final long expireTime = 60 * 60 * 24 * 2;

    @Autowired
    public void setRedisUtil(RedisUtil redisUtil) {
        ApplyNoUtil.redisUtil = redisUtil;
    }

    public static String getToDayMaxApplyId() {
        String toDay = DateUtils.formatDate(new Date(), "yyyyMMdd");
        String key =  APPLY_KEY_PREFIX + toDay;
        Long toDayMax = null;
        try{
            toDayMax = redisUtil.incr(key,1);
            if (toDayMax == 1) {
                redisUtil.expire(key, expireTime);
                log.info("{}失效时间：{}", key, expireTime);
            }
        }catch (Exception e){
            log.error("申请编号生成异常{}", e.getMessage());
            log.error("context",e);
        }
        if (toDayMax == null) {
            log.error("申请编号生成异常");
            return null;
        }
        // 构建申请编号
        String number = String.valueOf(toDayMax);
        if (String.valueOf(toDayMax).length() != 4) {
            while (number.length() < 4) {
                number = "0" + number;
            }
        }
        number = toDay + number;

        return number;
    }
}
