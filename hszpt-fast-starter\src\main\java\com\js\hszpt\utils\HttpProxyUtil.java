package com.js.hszpt.utils;

import cn.hutool.core.util.StrUtil;
import com.js.hszpt.config.ProxyConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.StreamUtils;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.PrintWriter;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Enumeration;

/**
 * HTTP透传工具类
 * 提供HTTP请求转发的工具方法
 */
@Component
public class HttpProxyUtil {

    private static final Logger logger = LoggerFactory.getLogger(HttpProxyUtil.class);

    @Autowired
    private ProxyConfig proxyConfig;

    @Autowired
    @Qualifier("proxyRestTemplate")
    private RestTemplate restTemplate;

    @Value("${spring.profiles.active}")
    private String env;



    /**
     * 转发HTTP请求到目标服务器
     *
     * @param request   原始HTTP请求
     * @param response  HTTP响应对象
     * @param targetUrl 目标URL，如果为null则使用配置中的URL
     * @param useAuth   是否使用鉴权
     */
    public void forwardRequest(HttpServletRequest request, HttpServletResponse response,
            String targetUrl, boolean useAuth) {
        try {
            // 读取请求体
            byte[] body = readRequestBody(request);

            // 构建目标URI
            URI uri = buildTargetUri(request, targetUrl,"");

            // 构建HTTP请求头
            HttpHeaders headers = buildRequestHeaders(request);

            // 添加app_key和app_secret（非开发环境） 数据中台鉴权
            if (env != null && !env.equals("dev")) {
                headers.set("app_key",proxyConfig.getAppKey());
                headers.set("app_secret",proxyConfig.getAppSecret());
                headers.set("sso_type",proxyConfig.getSsoType());
            }

            // 添加鉴权信息（如果需要）
            if (useAuth) {
                headers.set("Authorization", "Bearer " + proxyConfig.getAuthToken());
                //logger.info("已添加鉴权信息");
            }

            // 创建HTTP请求实体
            HttpEntity<byte[]> httpEntity = new HttpEntity<>(body, headers);

            // 获取HTTP方法
            HttpMethod httpMethod = HttpMethod.valueOf(request.getMethod());

            //logger.info("转发请求到: {}, 方法: {}", uri, httpMethod);

            // 发送请求到目标服务器
            ResponseEntity<byte[]> responseEntity = restTemplate.exchange(
                    uri,
                    httpMethod,
                    httpEntity,
                    byte[].class);

            logger.info("收到目标服务器响应，状态码: {}", responseEntity.getStatusCode());

            // 重置响应以确保清除所有先前的设置
            response.reset();

            // 设置响应状态码
            response.setStatus(responseEntity.getStatusCodeValue());

            // 设置响应头 - 跳过可能导致问题的头信息
            responseEntity.getHeaders().forEach((name, values) -> {
                // 跳过Transfer-Encoding头，因为它会由容器自动添加，避免重复
                // 跳过Connection头，这应由代理/容器管理
                if (!"transfer-encoding".equalsIgnoreCase(name) &&
                        !"connection".equalsIgnoreCase(name)) {
                    values.forEach(value -> {
                        response.addHeader(name, value);
                        logger.debug("设置响应头: {} = {}", name, value);
                    });
                } else {
                    logger.debug("跳过响应头: {} = {}", name, values);
                }
            });

            // 写入响应体
            writeResponseBody(response, responseEntity.getBody(), responseEntity.getHeaders().getContentType());

        } catch (Exception e) {
            logger.error("转发请求时发生错误", e);
            writeErrorResponse(response, "转发请求时发生错误: " + e.getMessage());
        }
    }

    /**
     * 转发HTTP请求到目标服务器，确保参数对象中的属性被正确传递
     *
     * @param request     原始HTTP请求
     * @param response    HTTP响应对象
     * @param targetUrl   目标URL，如果为null则使用配置中的URL
     * @param useAuth     是否使用鉴权
     * @param paramObject 包含需要传递的参数的对象
     */
    public void forwardRequest(HttpServletRequest request, HttpServletResponse response,
            String targetUrl, boolean useAuth, Object paramObject) {
        try {
            logger.info("请求参数:{}", paramObject);
            // 构建目标URI
            URI uri = buildTargetUri(request, targetUrl,"");

            // 构建HTTP请求头
            HttpHeaders headers = buildRequestHeaders(request);

            // 添加鉴权信息（如果需要）
            if (useAuth) {
                headers.set("Authorization", "Bearer " + proxyConfig.getAuthToken());
                logger.info("已添加鉴权信息");
            }

            // 创建HTTP请求实体，直接使用传入的paramObject作为请求体
            HttpEntity<?> httpEntity = new HttpEntity<>(paramObject, headers);

            // 获取HTTP方法
            HttpMethod httpMethod = HttpMethod.valueOf(request.getMethod());

            logger.info("转发请求到: {}, 方法: {}, 带有参数对象", uri, httpMethod);

            // 发送请求到目标服务器
            ResponseEntity<byte[]> responseEntity = restTemplate.exchange(
                    uri,
                    httpMethod,
                    httpEntity,
                    byte[].class);

            logger.info("收到目标服务器响应，状态码: {}", responseEntity.getStatusCode());

            // 重置响应以确保清除所有先前的设置
            response.reset();

            // 设置响应状态码
            response.setStatus(responseEntity.getStatusCodeValue());

            // 设置响应头 - 跳过可能导致问题的头信息
            responseEntity.getHeaders().forEach((name, values) -> {
                // 跳过Transfer-Encoding头，因为它会由容器自动添加，避免重复
                // 跳过Connection头，这应由代理/容器管理
                if (!"transfer-encoding".equalsIgnoreCase(name) &&
                        !"connection".equalsIgnoreCase(name)) {
                    values.forEach(value -> {
                        response.addHeader(name, value);
                        logger.debug("设置响应头: {} = {}", name, value);
                    });
                } else {
                    logger.debug("跳过响应头: {} = {}", name, values);
                }
            });

            // 写入响应体
            writeResponseBody(response, responseEntity.getBody(), responseEntity.getHeaders().getContentType());

        } catch (Exception e) {
            logger.error("转发请求时发生错误", e);
            writeErrorResponse(response, "转发请求时发生错误: " + e.getMessage());
        }
    }

    /**
     * 使用配置中的目标URL和鉴权设置转发请求
     *
     * @param request  原始HTTP请求
     * @param response HTTP响应对象
     */
    public void forwardRequest(HttpServletRequest request, HttpServletResponse response) {
        forwardRequest(request, response, null, proxyConfig.isAuthEnabled());
    }

    /**
     * 读取请求体 - 修复内存泄露问题
     */
    private byte[] readRequestBody(HttpServletRequest request) throws IOException {
        InputStream inputStream = null;
        try {
            inputStream = request.getInputStream();
            if (inputStream == null) {
                return new byte[0];
            }
            return StreamUtils.copyToByteArray(inputStream);
        } finally {
            // 确保输入流被正确关闭
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    logger.warn("关闭输入流时发生异常", e);
                }
            }
        }
    }

    /**
     * 构建目标URI
     */
    private URI buildTargetUri(HttpServletRequest request, String targetUrl,String flag) throws URISyntaxException {
        String requestUri = request.getRequestURI();
        String queryString = request.getQueryString();

        // 如果没有指定目标URL，则使用配置中的URL
        String baseUrl = (targetUrl != null) ? targetUrl : proxyConfig.getTargetUrl();

        StringBuilder fullUrl = new StringBuilder(baseUrl);

        // 如果请求URI以/proxy开头，则移除这部分
        if (requestUri.startsWith("/proxy")) {
            requestUri = requestUri.substring(6); // 移除"/proxy"
        }

        // 确保URI以/开头
        if (!requestUri.startsWith("/") && !baseUrl.endsWith("/")) {
            fullUrl.append("/");
        }

        fullUrl.append(requestUri);

        if (queryString != null && !queryString.isEmpty()) {
            fullUrl.append("?").append(queryString);
        }

        if(StrUtil.isNotBlank(flag) && flag.equals("1")){
            fullUrl.append("&notCert=1");
        }

        return new URI(fullUrl.toString());
    }

    /**
     * 构建请求头
     */
    private HttpHeaders buildRequestHeaders(HttpServletRequest request) {
        HttpHeaders headers = new HttpHeaders();

        // 复制原始请求的所有请求头
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();

            // 跳过一些特定的请求头，这些请求头可能会导致问题
            if ("host".equalsIgnoreCase(headerName) ||
                    "connection".equalsIgnoreCase(headerName) ||
                    "content-length".equalsIgnoreCase(headerName)) {
                continue;
            }

            Enumeration<String> headerValues = request.getHeaders(headerName);
            while (headerValues.hasMoreElements()) {
                String headerValue = headerValues.nextElement();
                headers.add(headerName, headerValue);
            }
        }

        // 设置内容类型（如果原始请求中有）
        String contentType = request.getContentType();
        if (contentType != null) {
            headers.setContentType(MediaType.parseMediaType(contentType));
        }

        return headers;
    }

    /**
     * 安全地写入响应体，确保资源正确释放 - 增强版本
     */
    private void writeResponseBody(HttpServletResponse response, byte[] responseBody, MediaType contentType) throws IOException {
        if (responseBody == null) {
            logger.warn("收到空响应体");
            return;
        }

        // 检查响应是否已提交
        if (response.isCommitted()) {
            logger.warn("响应已提交，无法写入响应体");
            return;
        }

        OutputStream outputStream = null;
        try {
            // 设置Content-Type
            if (contentType != null) {
                response.setContentType(contentType.toString());
            } else {
                response.setContentType("application/json;charset=UTF-8");
            }

            // 设置Content-Length
            response.setContentLength(responseBody.length);

            // 获取输出流并写入数据
            outputStream = response.getOutputStream();
            outputStream.write(responseBody);
            outputStream.flush();
            logger.debug("响应体已写入，长度: {}", responseBody.length);
        } catch (IOException e) {
            logger.error("写入响应体时发生异常", e);
            throw e;
        } finally {
            // 确保输出流被正确关闭
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    logger.warn("关闭输出流时发生异常", e);
                }
            }
        }
    }

    /**
     * 安全地写入错误响应，使用OutputStream避免与getWriter()冲突
     */
    private void writeErrorResponse(HttpServletResponse response, String errorMessage) {
        OutputStream outputStream = null;
        try {
            if (!response.isCommitted()) {
                response.setStatus(500);
                response.setContentType("application/json;charset=UTF-8");

                // 构建错误JSON - 修复XSS漏洞：对错误消息进行完整的JSON转义
                String sanitizedMessage = sanitizeForJson(errorMessage != null ? errorMessage : "未知错误");
                String jsonError = "{\"success\":false,\"message\":\"" + sanitizedMessage + "\"}";

                // 使用OutputStream而不是Writer，避免与getOutputStream()冲突
                byte[] errorBytes = jsonError.getBytes("UTF-8");
                response.setContentLength(errorBytes.length);

                outputStream = response.getOutputStream();
                outputStream.write(errorBytes);
                outputStream.flush();
                logger.debug("错误响应已写入: {}", jsonError);
            } else {
                logger.warn("响应已提交，无法写入错误信息");
            }
        } catch (IOException ex) {
            logger.error("写入错误响应时发生异常", ex);
        } finally {
            // 确保OutputStream被正确关闭
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (Exception e) {
                    logger.warn("关闭OutputStream时发生异常", e);
                }
            }
        }
    }

    /**
     * 处理GET请求的转发，将对象参数转换为URL查询参数
     *
     * @param request     原始HTTP请求
     * @param response    HTTP响应对象
     * @param targetUrl   目标URL，如果为null则使用配置中的URL
     * @param useAuth     是否使用鉴权
     * @param paramObject 包含需要转换为查询参数的对象
     */
    public void forwardGetRequest(HttpServletRequest request, HttpServletResponse response,
            String targetUrl, boolean useAuth, Object paramObject) {
        try {
            logger.info("GET请求参数转发:{}", paramObject);

            // 获取基本URL
            String baseUrl = (targetUrl != null) ? targetUrl : proxyConfig.getTargetUrl();
            String requestUri = request.getRequestURI();

            // 如果请求URI以/proxy开头，则移除这部分
            if (requestUri.startsWith("/proxy")) {
                requestUri = requestUri.substring(6); // 移除"/proxy"
            }

            // 拼接请求URI
            StringBuilder fullUrl = new StringBuilder(baseUrl);
            if (!requestUri.startsWith("/") && !baseUrl.endsWith("/")) {
                fullUrl.append("/");
            }
            if(requestUri.equals("/certificateExternal/crewCertificateQuery")){
                //转到新开的船员查询
                requestUri = "/certificate/crewCertificateQuery";
            }
            fullUrl.append(requestUri);

            // 将对象转换为查询参数
            StringBuilder queryParams = new StringBuilder();

            // 使用反射获取对象属性并转换为查询参数 - 修复内存泄露
            Field[] fields = paramObject.getClass().getDeclaredFields();
            boolean firstParam = true;

            for (Field field : fields) {
                boolean wasAccessible = field.isAccessible();
                try {
                    field.setAccessible(true);
                    Object value = field.get(paramObject);
                    if (value != null && !value.toString().isEmpty()) {
                        if (firstParam) {
                            queryParams.append("?");
                            firstParam = false;
                        } else {
                            queryParams.append("&");
                        }
                        // 修复XSS漏洞：对参数名和参数值进行URL编码
                        String encodedFieldName = URLEncoder.encode(field.getName(), StandardCharsets.UTF_8.toString());
                        String encodedValue = URLEncoder.encode(value.toString(), StandardCharsets.UTF_8.toString());
                        queryParams.append(encodedFieldName)
                                .append("=")
                                .append(encodedValue);
                    }
                } catch (IllegalAccessException e) {
                    logger.warn("无法访问字段: {}", field.getName(), e);
                } catch (UnsupportedEncodingException e) {
                    logger.warn("URL编码失败，字段: {}", field.getName(), e);
                    // 如果编码失败，跳过这个参数以避免安全风险
                } finally {
                    // 恢复原始的可访问性状态，避免内存泄露
                    field.setAccessible(wasAccessible);
                }
            }

            // 拼接最终URL
            fullUrl.append(queryParams);

            logger.info("构建的GET请求URL: {}", fullUrl);

            // 构建HTTP请求头
            HttpHeaders headers = buildRequestHeaders(request);

            // 添加app_key和app_secret（非开发环境） 数据中台鉴权
            if (env != null && !env.equals("dev")) {
                headers.set("app_key",proxyConfig.getAppKey());
                headers.set("app_secret",proxyConfig.getAppSecret());
                headers.set("sso_type",proxyConfig.getSsoType());
            }

            // 添加鉴权信息（如果需要）
            if (useAuth) {
                headers.set("Authorization", "Bearer " + proxyConfig.getAuthToken());
                logger.info("已添加鉴权信息");
            }

            // 创建HTTP请求实体（对于GET请求，请求体为空）
            HttpEntity<Void> httpEntity = new HttpEntity<>(headers);

            // 发送GET请求到目标服务器
            ResponseEntity<byte[]> responseEntity = restTemplate.exchange(
                    fullUrl.toString(),
                    HttpMethod.GET,
                    httpEntity,
                    byte[].class);

            logger.info("收到目标服务器响应，状态码: {}", responseEntity.getStatusCode());

            // 处理响应（复用现有代码）
            // 重置响应以确保清除所有先前的设置
            response.reset();

            // 设置响应状态码
            response.setStatus(responseEntity.getStatusCodeValue());

            // 设置响应头 - 跳过可能导致问题的头信息
            responseEntity.getHeaders().forEach((name, values) -> {
                // 跳过Transfer-Encoding头，因为它会由容器自动添加，避免重复
                // 跳过Connection头，这应由代理/容器管理
                if (!"transfer-encoding".equalsIgnoreCase(name) &&
                        !"connection".equalsIgnoreCase(name)) {
                    values.forEach(value -> {
                        response.addHeader(name, value);
                        logger.debug("设置响应头: {} = {}", name, value);
                    });
                } else {
                    logger.debug("跳过响应头: {} = {}", name, values);
                }
            });

            // 写入响应体
            writeResponseBody(response, responseEntity.getBody(), responseEntity.getHeaders().getContentType());

        } catch (Exception e) {
            logger.error("转发GET请求时发生错误", e);
            writeErrorResponse(response, "转发请求时发生错误: " + e.getMessage());
        }
    }

    /**
     * 转发HTTP请求到目标服务器并返回响应数据
     *
     * @param request   原始HTTP请求
     * @param response  HTTP响应对象
     * @param targetUrl 目标URL，如果为null则使用配置中的URL
     * @param useAuth   是否使用鉴权
     * @return 目标服务器的响应数据
     */
    public byte[] forwardRequestAndGetResponse(HttpServletRequest request, HttpServletResponse response,
            String targetUrl, boolean useAuth,Object paramObject) {
        try {
            // 读取请求体
            byte[] body = readRequestBody(request);

            // 获取基本URL
            String baseUrl = (targetUrl != null) ? targetUrl : proxyConfig.getTargetUrl();
            String requestUri = request.getRequestURI();
            String targetPath = "certificateExternal/crewCertificateQuery";
            String replacementPath = "certificate/crewCertificateQuery";
            // 拼接请求URI
            StringBuilder fullUrl = new StringBuilder(baseUrl);
            if (!requestUri.startsWith("/") && !baseUrl.endsWith("/")) {
                fullUrl.append("/");
            }
            // 检查URI是否包含目标路径
            if (requestUri.contains(targetPath)) {
                // 替换路径部分
                requestUri =  requestUri.replace(targetPath, replacementPath);
            }
            fullUrl.append(requestUri);
            // 将对象转换为查询参数
            StringBuilder queryParams = new StringBuilder();
            // 使用反射获取对象属性并转换为查询参数 - 修复内存泄露
            Field[] fields = paramObject.getClass().getDeclaredFields();
            boolean firstParam = true;

            for (Field field : fields) {
                boolean wasAccessible = field.isAccessible();
                try {
                    field.setAccessible(true);
                    Object value = field.get(paramObject);
                    if (value != null && !value.toString().isEmpty()) {
                        if (firstParam) {
                            queryParams.append("?");
                            firstParam = false;
                        } else {
                            queryParams.append("&");
                        }
                        // 修复XSS漏洞：对参数名和参数值进行URL编码
                        String encodedFieldName = URLEncoder.encode(field.getName(), StandardCharsets.UTF_8.toString());
                        String encodedValue = URLEncoder.encode(value.toString(), StandardCharsets.UTF_8.toString());
                        queryParams.append(encodedFieldName)
                                .append("=")
                                .append(encodedValue);
                    }
                } catch (IllegalAccessException e) {
                    logger.warn("无法访问字段: {}", field.getName(), e);
                } catch (UnsupportedEncodingException e) {
                    logger.warn("URL编码失败，字段: {}", field.getName(), e);
                    // 如果编码失败，跳过这个参数以避免安全风险
                } finally {
                    // 恢复原始的可访问性状态，避免内存泄露
                    field.setAccessible(wasAccessible);
                }
            }
            // 拼接最终URL
            fullUrl.append(queryParams);
            logger.info("构建的GET请求URL: {}", fullUrl);

            // 构建HTTP请求头
            HttpHeaders headers = buildRequestHeaders(request);

            // 添加app_key和app_secret（非开发环境） 数据中台鉴权
            if (env != null && !env.equals("dev")) {
                headers.set("app_key",proxyConfig.getAppKey());
                headers.set("app_secret",proxyConfig.getAppSecret());
                headers.set("sso_type",proxyConfig.getSsoType());
            }

            // 添加鉴权信息（如果需要）
            if (useAuth) {
                headers.set("Authorization", "Bearer " + proxyConfig.getAuthToken());
                logger.info("已添加鉴权信息");
            }

            // 创建HTTP请求实体
            HttpEntity<byte[]> httpEntity = new HttpEntity<>(body, headers);

            // 获取HTTP方法
            HttpMethod httpMethod = HttpMethod.valueOf(request.getMethod());

            logger.info("转发请求到: {}, 方法: {}", fullUrl, httpMethod);

            // 发送请求到目标服务器
            ResponseEntity<byte[]> responseEntity = restTemplate.exchange(
                    fullUrl.toString(),
                    httpMethod,
                    httpEntity,
                    byte[].class);

            logger.info("收到目标服务器响应，状态码: {}", responseEntity.getStatusCode());

            // 处理响应...（与原方法相同）

            // 返回响应数据
            return responseEntity.getBody();
        } catch (Exception e) {
            logger.error("转发请求时发生错误", e);
            // 注意：这个方法是用来获取响应数据的，不应该直接写入response
            // 只记录错误日志，让调用方处理异常
            return null;
        }
    }

    /**
     * 安全地关闭可关闭的资源
     * @param closeable 需要关闭的资源
     * @param resourceName 资源名称（用于日志）
     */
    private void safeClose(AutoCloseable closeable, String resourceName) {
        if (closeable != null) {
            try {
                closeable.close();
                logger.debug("成功关闭资源: {}", resourceName);
            } catch (Exception e) {
                logger.warn("关闭资源 {} 时发生异常", resourceName, e);
            }
        }
    }

    /**
     * 安全地转义字符串用于JSON输出，防止XSS攻击
     * @param input 需要转义的字符串
     * @return 转义后的安全字符串
     */
    private String sanitizeForJson(String input) {
        if (input == null) {
            return "";
        }

        // 对JSON中的特殊字符进行转义，防止XSS攻击
        return input.replace("\\", "\\\\")    // 反斜杠必须首先转义
                   .replace("\"", "\\\"")     // 双引号
                   .replace("\b", "\\b")      // 退格符
                   .replace("\f", "\\f")      // 换页符
                   .replace("\n", "\\n")      // 换行符
                   .replace("\r", "\\r")      // 回车符
                   .replace("\t", "\\t")      // 制表符
                   .replace("/", "\\/")       // 斜杠（可选，但更安全）
                   .replace("<", "\\u003c")   // 小于号，防止HTML注入
                   .replace(">", "\\u003e")   // 大于号，防止HTML注入
                   .replace("&", "\\u0026")   // 和号，防止HTML实体注入
                   .replace("'", "\\u0027")   // 单引号，防止JavaScript注入
                   .replace("=", "\\u003d");  // 等号，防止某些注入攻击
    }
}
