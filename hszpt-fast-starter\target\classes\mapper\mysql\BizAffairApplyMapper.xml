<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.js.hszpt.mapper.BizAffairApplyMapper">

    <select id="getApply" resultType="com.js.hszpt.entity.BizAffairApply">
        select distinct biz_affair_apply.* 
        from biz_affair_apply 
        left join biz_eg_send_cert on biz_affair_apply.apply_id = biz_eg_send_cert.apply_id
        left join biz_eg_cert_check on biz_affair_apply.apply_id = biz_eg_cert_check.apply_id and biz_eg_cert_check.check_flag_code = '1'
        <where>
            <if test="modelType != null and modelType != ''">
                and biz_affair_apply.model_type = #{modelType}
            </if>
            <if test="cleanStatus != null and cleanStatus != ''">
                and biz_affair_apply.clean_status = #{cleanStatus}
            </if>
            and (
                biz_eg_send_cert.apply_id is not null
                or
                biz_eg_cert_check.apply_id is not null
            )
            limit 100;
        </where>
    </select>
</mapper>
