package com.js.hszpt.api;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.js.common.service.SecurityService;
import com.js.core.common.utils.ResultUtil;
import com.js.core.common.vo.Result;
import com.js.hszpt.config.ProxyConfig;
import com.js.hszpt.dto.CertificateExternalQueryDto;
import com.js.hszpt.dto.CrewCertificateQueryDto;
import com.js.hszpt.dto.IntranetQueryQueryDto;
import com.js.hszpt.enmus.CertificateTypeCode;
import com.js.hszpt.entity.Certificate;
import com.js.hszpt.properties.ZptProperties;
import com.js.hszpt.service.CertificateService;
import com.js.hszpt.service.CertAccessLogService;
import com.js.hszpt.service.CrewCertificateService;
import com.js.hszpt.service.SysDeptIambService;
import com.js.hszpt.utils.FileDownloadUtil;
import com.js.hszpt.utils.HttpProxyUtil;
import com.js.hszpt.utils.VerifyCodeUtil;
import com.js.hszpt.utils.encrypt.RSAEncryptionUtil;
import com.js.hszpt.utils.encrypt.Sm4Tool;
import com.js.hszpt.vo.*;
import com.js.sys.entity.Dict;
import com.js.sys.entity.DictData;
import com.js.sys.service.DictDataService;
import com.js.sys.service.DictService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.web.bind.annotation.*;
import com.js.core.api.BaseApiPlus;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.security.PrivateKey;
import java.text.SimpleDateFormat;
import java.time.DateTimeException;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 外部证照查询接口
 */
@Slf4j
@RestController
@RequestMapping("/certificateExternal")
public class CertificateExternalApi extends BaseApiPlus<CertificateService, Certificate, String> {

    @Autowired
    private CertificateService certificateService;

    @Autowired
    private SecurityService securityService;

    @Autowired
    private SysDeptIambService sysDeptIambService;

    @Autowired
    private ZptProperties zptProperties;

    @Autowired
    private DictService dictService;

    @Autowired
    private DictDataService dictDataService;

    @Autowired
    private CertAccessLogService accessLogService;

    @Autowired
    private CrewCertificateService crewCertificateService;

    @Autowired
    private FileDownloadUtil fileDownloadUtil;

    @Autowired
    private VerifyCodeUtil verifyCodeUtil;

    @Value("${qrcode.security.privateKey}")
    private String privateKeyString;

    private ObjectMapper objectMapper = new ObjectMapper();

    @Value("${certificate.certOfd}")
    private String certOfd;

    @Autowired
    private HttpProxyUtil httpProxyUtil;

    @Value("${certificate.targetUrl}")
    private String targetUrl;

    @Autowired
    private ProxyConfig proxyConfig;

    /**
     * 外部证照查询接口
     */
    @GetMapping("/certificateIntranetQueryQuery")
    public Result<IPage<CertificateExternalQueryVo>> query(CertificateExternalQueryDto queryDto, Page page) {
        try {
            // 获取当前用户的 client_id
            // String clientId = securityService.getCurrUser().getClientId();
            String clientId = securityService.getCurrUser().getUsername();
            if (StrUtil.isBlank(clientId)) {
                return Result.failed("无法获取用户信息");
            }

            // 参数校验
            if (queryDto == null) {
                return Result.failed("查询参数不能为空");
            }

            // 必填参数校验
            if (StrUtil.isBlank(queryDto.getHolderName())) {
                return Result.failed("持证人姓名不能为空");
            }
            if (StrUtil.isBlank(queryDto.getHolderIdentityNumber())) {
                return Result.failed("持有人身份标识号码不能为空");
            }

            // 转换查询参数
            IntranetQueryQueryDto intranetDto = new IntranetQueryQueryDto();
            // 设置必要的查询参数
            intranetDto.setHolderName(queryDto.getHolderName());
            intranetDto.setHolderIdentityNumber(queryDto.getHolderIdentityNumber());

            // 设置其他查询条件
            if (StrUtil.isNotBlank(queryDto.getShipName())) {
                intranetDto.setShipName(queryDto.getShipName());
            }
            if (StrUtil.isNotBlank(queryDto.getCertificateNum())) {
                intranetDto.setCertificateNum(queryDto.getCertificateNum());
            }
            if (StrUtil.isNotBlank(queryDto.getCertificateType())) {
                intranetDto.setCertificateType(queryDto.getCertificateType());
            }
            if (StrUtil.isNotBlank(queryDto.getCertificateName())) {
                intranetDto.setCertificateName(queryDto.getCertificateName());
            }
            if (StrUtil.isNotBlank(queryDto.getExpireDateStart())) {
                intranetDto.setExpireDateStart(queryDto.getExpireDateStart());
            }
            if (StrUtil.isNotBlank(queryDto.getExpireDateEnd())) {
                intranetDto.setExpireDateEnd(queryDto.getExpireDateEnd());
            }
            if (StrUtil.isNotBlank(queryDto.getStatusFlag())) {
                intranetDto.setStatusFlag(queryDto.getStatusFlag());
            }
            if (StrUtil.isNotBlank(queryDto.getShipImo())) {
                intranetDto.setShipImo(queryDto.getShipImo());
            }
            if (StrUtil.isNotBlank(queryDto.getShipCallSign())) {
                intranetDto.setShipCallSign(queryDto.getShipCallSign());
            }

            // 设置分页参数
            if (page == null) {
                page = new Page<>(1, 10);
            }

            // 设置 clientId 到查询参数中
            intranetDto.setClientId(clientId);

            // 14RYG 0018460 中间加空格
            if (StrUtil.isNotBlank(queryDto.getCertificateNum())) {
                String certificateNum = queryDto.getCertificateNum().replaceAll(" ", "");
                char[] chars = certificateNum.toCharArray();
                StringBuilder stringBuilder = new StringBuilder();
                for (int i = 0; i < chars.length; i++) {// 判断是否是英文
                    if (i > 1 && (chars[i - 1] + "").matches("^[a-zA-Z\\s,.!?;:'\"-]+$")
                            && (chars[i] + "").matches("^\\d+$")) {// 判断是否是数字
                        stringBuilder.append(" ").append(chars[i]);
                    } else {
                        stringBuilder.append(chars[i]);
                    }
                }
                intranetDto.setCertificateNum(stringBuilder.toString());
            }
            log.info("内网查询接口certificateIntranetQueryQuery，请求参数intranetQueryQueryDto:{}", intranetDto);

            // 1. 查询证照列表
            IPage<CertificateIntranetQueryQueryVo> queryResult = certificateService
                    .certificateIntranetQueryQuery(intranetDto, page);

            // 2. 转换结果并获取详细信息
            IPage<CertificateExternalQueryVo> result = queryResult.convert(item -> {
                CertificateExternalQueryVo vo = new CertificateExternalQueryVo();
                // 复制基础信息
                BeanUtils.copyProperties(item, vo);

                // 显式设置 shipImo 字段
                vo.setShipImo(item.getShipImo());

                try {
                    // 获取证照详细属性信息
                    if (item.getId() != null) {
                        List<Map<String, Object>> attributes = certificateService.CertificateVerify(item.getId());
                        vo.setAttributes(attributes);
                    }
                } catch (IllegalAccessException e) {
                    log.error("Failed to get certificate attributes for id: " + item.getId(), e);
                }

                return vo;
            });

            return Result.success(result);
        } catch (Exception e) {
            log.error("Certificate external query failed", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 下载电子证照ofd
     *
     * @param certificateId 电子证照生成的id
     * @return 电子证照接口生成信息
     */
    @GetMapping("/getPermitDetail/{certificateId}")
    public Result<String> getPermitDetail(@PathVariable("certificateId") String certificateId) {
        log.info("获取证照详情请求的证照ID: {}", certificateId);
        // 获取OFD文件URL
        String url = baseService.getPermitDetail(certificateId);
        if (StrUtil.isBlank(url)) {
            // 统计次数
            return ResultUtil.error("电子证照查看失败");
        }
        // 截取电子证照服务IP替换为外网域名
        String target = "group1";
        String newPrefix = targetUrl;
        int index = url.indexOf(target);
        String ofdUrl = "";
        if (index != -1) {
            String suffix = url.substring(index);
            ofdUrl = newPrefix + suffix;
            log.info("生成的OFD文件URL: {}", ofdUrl);
        } else {
            log.error("目标字符串未找到。");
            return ResultUtil.error("电子证照查看失败");
        }

        // 使用buildFileName方法生成符合规范的文件名
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String subPrefix = certOfd + sdf.format(date) + "/";

        // 创建目录
        String dirPath = zptProperties.getFilePath() + subPrefix;
        File dir = new File(dirPath);
        if (!dir.exists()) {
            boolean created = dir.mkdirs();
            if (!created) {
                log.error("目录创建失败: {}", dirPath);
                return ResultUtil.error("文件存储目录创建失败");
            }
            log.info("已创建存储目录: {}", dirPath);
        }

        String fileName = baseService.buildFileName(certificateId, "ofd"); // 调用buildFileName方法
        log.info("生成的文件名: {}", fileName);
        String cachedFilePath = dirPath + fileName; // 使用已创建的目录路径
        log.info("文件存放路径: {}", cachedFilePath);
        // 🔑 修复：使用优化的文件下载工具，防止句柄泄露
        boolean downloadSuccess = fileDownloadUtil.downloadFile(url, cachedFilePath);
        if (!downloadSuccess) {
            return ResultUtil.error("电子证照查看失败");
        }
        String finalUrl = newPrefix + subPrefix + fileName;
        log.info("返回的文件URL: {}", finalUrl);
        accessLogService.certOperationLog("1", "1", "1", "", certificateId);
        return ResultUtil.data(finalUrl);
    }

    // 已移除原downloadFile方法，使用FileDownloadUtil替代以防止资源泄露

    /**
     * 下载电子证照jpg
     * 
     * @param certificateId
     * @return
     */
    @GetMapping("/download/{certificateId}")
    public Result<String> download(@PathVariable("certificateId") String certificateId) {
        String url = this.certificateService.download(certificateId);
        if (StrUtil.isBlank(url)) {
            // 统计次数
            return ResultUtil.error("电子证照查看失败");
        }
        // 截取电子证照服务IP替换为外网域名
        String target = "DCS";
        String newPrefix = targetUrl;
        int index = url.indexOf(target);
        String jpgUrl = "";
        if (index != -1) {
            String suffix = url.substring(index);
            jpgUrl = newPrefix + suffix;
            log.info(jpgUrl);
        } else {
            log.error("目标字符串未找到。");
            // 统计次数
            return ResultUtil.error("电子证照查看失败");
        }
        if (StrUtil.isNotBlank(jpgUrl)) {
            // 统计次数
            return ResultUtil.data(jpgUrl);
        } else {
            return ResultUtil.error("电子证照查看失败");
        }
    }

    /**
     * 海事通app电子证照查询接口
     *
     * @param idNumber         身份证号
     * @param certificateNames 证书名称列表
     * @param shipName         船舶名称
     * @param shipImo          IMO船舶识别号
     * @param shipCallSign     船舶编号或呼号
     * @param issuOrgCode3     签发机关
     * @param validCode        证照状态
     * @param pageSize         每页大小
     * @param pageNumber       当前页码
     * @return 电子证照信息
     */
    @GetMapping("/getCertificateByIdNumber")
    public Result<Map<String, Object>> getCertificateByIdNumber(
            @RequestParam(required = false) String idNumber,
            @RequestParam(required = false) List<String> certificateNames,
            @RequestParam(required = false) String shipName,
            @RequestParam(required = false) String shipImo,
            @RequestParam(required = false) String shipCallSign,
            @RequestParam(required = false) List<String> issuOrgCode3,
            @RequestParam(required = false) String validCode,
            @RequestParam(required = false) String certStartDate,
            @RequestParam(required = false) String certEndDate,
            @RequestParam(required = false) String shipNames,
            @RequestParam(required = false) String displaySource,
            @RequestParam Integer pageSize,
            @RequestParam Integer pageNumber) {

        long startTime = System.currentTimeMillis(); // 开始时间记录
        log.info("开始执行 getCertificateByIdNumber 方法，时间戳: {}", startTime);

        // 参数校验
        if (StrUtil.isBlank(idNumber) && StrUtil.isBlank(shipNames)) {
            return Result.failed("身份证号或船名不能为空");
        }
        if (pageSize <= 0) {
            return Result.failed("每页大小必须大于0");
        }
        if (pageNumber <= 0) {
            return Result.failed("页码必须大于0");
        }

        // 记录请求参数
        log.info("请求参数 - 身份证号: {}, 证书名称: {}, 船舶名称: {}, 船舶IMO: {}, 船舶编号: {}, 签发机关: {}, 证照状态: {}, 页码: {}, 每页大小: {}",
                idNumber, certificateNames, shipName, shipImo, shipCallSign, issuOrgCode3, validCode, pageNumber,
                pageSize);
        // 去掉逗号
        String cleanedShipName = shipName != null ? shipName.replace(",", "") : null;

        // 创建 DTO 对象并设置参数
        CertificateExternalQueryDto queryDto = new CertificateExternalQueryDto();
        queryDto.setHolderIdentityNumber(idNumber); // 身份证号
        queryDto.setCertificateNames(certificateNames);
        queryDto.setShipName(cleanedShipName); // 船舶名称
        queryDto.setShipImo(shipImo); // IMO船舶识别号
        queryDto.setShipCallSign(shipCallSign); // 船舶编号或呼号
        queryDto.setIssuOrgCode3(issuOrgCode3); // 签发机关
        queryDto.setStatusFlag(validCode); // 证照状态
        queryDto.setCertStartDate(certStartDate); // 有效期开始时间
        queryDto.setCertEndDate(certEndDate);// 有效期结束时间
        queryDto.setShipNames(shipNames);// 船舶名称集合
        queryDto.setDisplaySource(displaySource);

        // 尝试调用服务方法
        try {
            // 获取证照信息
            Map<String, Object> response = certificateService.getCertificateByIdNumber(queryDto, pageSize, pageNumber);
            long endTime = System.currentTimeMillis(); // 结束时间记录
            log.info("结束执行 getCertificateByIdNumber 方法，时间戳: {}, 耗时: {} 毫秒", endTime, (endTime - startTime));
            List<CertificateAppSearchVo> records = (List<CertificateAppSearchVo>) response.get("records");
            if (queryDto.getDisplaySource() != null && "3".equals(queryDto.getDisplaySource())) {
                log.info("displaySource = 3，开始对相同证照名称的记录进行去重处理");

                // 按证照名称分组
                Map<String, List<CertificateAppSearchVo>> groupedRecords = records.stream()
                        .collect(Collectors.groupingBy(CertificateAppSearchVo::getCertificateName));

                List<CertificateAppSearchVo> deduplicatedRecords = new ArrayList<>();

                for (Map.Entry<String, List<CertificateAppSearchVo>> entry : groupedRecords.entrySet()) {
                    String groupCertificateName = entry.getKey();
                    List<CertificateAppSearchVo> value = entry.getValue();

                    if (value.size() == 1) {
                        // 只有一个记录，直接添加
                        deduplicatedRecords.add(value.get(0));
                    } else {
                        // 多个记录，按 effectDate 和 expireDate 排序，取最新的
                        CertificateAppSearchVo latestRecord = value.stream()
                                .max((r1, r2) -> {
                                    // 比较 effectDate (生效日期)
                                    int effectiveDateCompare = compareDate(r1.getEffectDate(), r2.getEffectDate());
                                    if (effectiveDateCompare != 0) {
                                        return effectiveDateCompare;
                                    }
                                    // effectDate 相同时，比较 expireDate (到期日期)
                                    return compareDate(r1.getExpireDate(), r2.getExpireDate());
                                })
                                .orElse(value.get(0));

                        deduplicatedRecords.add(latestRecord);
                        log.info("证照名称：{}，原始记录数：{}，去重后保留1条", groupCertificateName, value.size());
                    }
                }

                // 更新结果集
                records = deduplicatedRecords;
                response.put("records", records);
                log.info("去重处理完成，原始记录数：{}，去重后记录数：{}", groupedRecords.values().stream().mapToInt(List::size).sum(),
                        deduplicatedRecords.size());
            }
            if (!records.isEmpty()) { // 新增空校验
                accessLogService.certOperationLog("1", "2", "1", "", records.get(0).getCertificateId());
            } else {
                accessLogService.certOperationLog("1", "2", "1", "", "");
                log.warn("未查询到证照记录，身份证号: {}", idNumber); // 可选：添加空结果日志记录
            }
            return ResultUtil.data(response); // 返回结果
        } catch (Exception e) {
            log.error("获取证照信息失败: {}", e.getMessage());
            e.printStackTrace(); // 打印堆栈信息以便调试
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 比较两个Date对象
     * 
     * @param date1 日期对象1
     * @param date2 日期对象2
     * @return 比较结果：-1表示date1<date2，0表示相等，1表示date1>date2
     */
    private int compareDate(Date date1, Date date2) {
        // 处理空值情况
        if (date1 == null && date2 == null) {
            return 0;
        }
        if (date1 == null) {
            return -1;
        }
        if (date2 == null) {
            return 1;
        }

        return date1.compareTo(date2);
    }

    /**
     * 比较两个日期字符串，支持多种日期格式
     * 
     * @param date1 日期字符串1
     * @param date2 日期字符串2
     * @return 比较结果：-1表示date1<date2，0表示相等，1表示date1>date2
     */
    private int compareDateString(String date1, String date2) {
        // 处理空值情况
        if (StrUtil.isBlank(date1) && StrUtil.isBlank(date2)) {
            return 0;
        }
        if (StrUtil.isBlank(date1)) {
            return -1;
        }
        if (StrUtil.isBlank(date2)) {
            return 1;
        }

        try {
            // 尝试解析日期，支持多种格式
            Date parsedDate1 = parseDateString(date1);
            Date parsedDate2 = parseDateString(date2);

            if (parsedDate1 == null && parsedDate2 == null) {
                return 0;
            }
            if (parsedDate1 == null) {
                return -1;
            }
            if (parsedDate2 == null) {
                return 1;
            }

            return parsedDate1.compareTo(parsedDate2);
        } catch (Exception e) {
            log.warn("日期比较异常，date1: {}, date2: {}, 错误: {}", date1, date2, e.getMessage());
            // 如果解析失败，按字符串比较
            return date1.compareTo(date2);
        }
    }

    /**
     * 解析日期字符串，支持多种格式
     * 
     * @param dateStr 日期字符串
     * @return 解析后的Date对象，解析失败返回null
     */
    private Date parseDateString(String dateStr) {
        if (StrUtil.isBlank(dateStr)) {
            return null;
        }

        // 支持的日期格式
        String[] patterns = {
                "yyyy-MM-dd",
                "yyyy/MM/dd",
                "yyyy-MM-dd HH:mm:ss",
                "yyyy/MM/dd HH:mm:ss",
                "yyyyMMdd",
                "yyyy-MM-dd'T'HH:mm:ss",
                "yyyy-MM-dd'T'HH:mm:ss.SSS"
        };

        for (String pattern : patterns) {
            try {
                return DateUtil.parse(dateStr, pattern);
            } catch (Exception e) {
                // 继续尝试下一个格式
            }
        }

        return null;
    }

    /**
     * 外网船员证书查询接口 - 透传调用第三方服务
     *
     * 支持根据多种条件查询船员电子证照信息，包括：
     * - 证书编号（certificateNumber）
     * - 持证人姓名（certificateHolderName）
     * - 身份证号（certificateHolderCode）
     * - 证照类型（certificateTypeCode）- 必填
     * - 证书印刷号（certPrintNo）
     *
     * @param crewCertificateQueryDto 查询参数DTO
     * @param page                    分页参数
     * @return 查询结果，包含船员电子证照信息列表
     */
    @GetMapping("/crewCertificateQuery")
    public Result<Map<String, Object>> crewCertificateQuery(
            CrewCertificateQueryDto crewCertificateQueryDto,
            Page page,
            HttpServletRequest request,
            HttpServletResponse response) {
        log.info("开始透传调用外网船员证书查询服务，参数：{}", crewCertificateQueryDto);
        long startTime = System.currentTimeMillis(); // 新增开始时间记录
        try {
            // 检查 crewCertificateQueryDto 是否为 null
            if (crewCertificateQueryDto == null) {
                log.error("查询参数 crewCertificateQueryDto 为空");
                return Result.failed("参数不能为空");
            }
            if (StrUtil.isBlank(crewCertificateQueryDto.getCertificateHolderCode())) {
                return Result.failed("身份证号不能为空");
            }
            if (crewCertificateQueryDto.getPageSize() == null || crewCertificateQueryDto.getPageSize() <= 0) {
                return Result.failed("每页大小必须大于0");
            }
            if (crewCertificateQueryDto.getPageNumber() == null || crewCertificateQueryDto.getPageNumber() <= 0) {
                return Result.failed("页码必须大于0");
            }
            Map<String, Object> resultData = new HashMap<>();

            // 外网查询校验验证码
            if (!verifyCodeUtil.check(crewCertificateQueryDto.getCaptchaId(), crewCertificateQueryDto.getCode())
                    && StrUtil.isNotBlank(crewCertificateQueryDto.getCode())) {
                return Result.failed(205, "验证码错误");
            }
            // 验证crewCertificateQueryDto.getCertStartDate()和crewCertificateQueryDto.getCertEndDate()是否是日期格式yyyy-MM-dd
            // 新增的验证逻辑
            String startDate = crewCertificateQueryDto.getCertStartDate();
            if (startDate != null) {
                try {
                    LocalDate.parse(startDate);
                } catch (DateTimeException e) {
                    return Result.failed("开始日期格式错误");
                }
            }
            String endDate = crewCertificateQueryDto.getCertEndDate();
            if (endDate != null) {
                try {
                    LocalDate.parse(endDate);
                } catch (DateTimeException e) {
                    return Result.failed("结束日期格式错误");
                }
            }

            // 解密二维码信息，获取电子证照id
            String id = null;
            String certTypeCode = null;
            String qrcode = crewCertificateQueryDto.getCertificateId();
            log.info("app扫码核验请求参数qrcode:{}", qrcode);
            if (StrUtil.isNotEmpty(qrcode)) {
                PrivateKey privateKey = RSAEncryptionUtil.getPrivateKey(privateKeyString);
                String certId = qrcode.substring(qrcode.lastIndexOf("id=") + 3);
                certId = certId.replace(" ", "+");
                String param = Sm4Tool.decrypt(certId, "5d3b282609644b4f8992b31a2e92f0f3");
                if (StrUtil.isNotEmpty(param)) {
                    certTypeCode = param.split("_")[0];
                    id = param.split("_")[1];
                    log.info("app扫码核验接口hstQrcodeVerification 请求参数qrcode解密后:{}", id);
                    crewCertificateQueryDto.setCertificateId(id);
                }
            }
            // 获取所有配置的证书类型编码
            String[] typeCodeArray = Arrays.stream(CertificateTypeCode.values())
                    .map(CertificateTypeCode::getTypeCode)
                    .toArray(String[]::new);
            // 枚举配置的类型不进行透传 直接查询业务
            if (StrUtil.equalsAny(certTypeCode, typeCodeArray)) {
                List<Map<String, Object>> listData = baseService.CertificateVerify(id);
                response.setContentType("application/json;charset=UTF-8");
                String result = objectMapper.writer().writeValueAsString(listData);
                response.getWriter().write(JSONUtil.toJsonStr(result));
                return null;
            }
            // 使用HttpProxyUtil转发请求到第三方服务
            // 这里使用配置的外网船员证书查询服务URL，并启用鉴权
            // 由于是GET请求，使用新增的forwardGetRequest方法，确保修改的参数能被正确传递
            // httpProxyUtil.forwardGetRequest(request, response,
            // proxyConfig.getTargetUrl(), true,
            // crewCertificateQueryDto);

            Page<CrewCertificateQueryVo> pageResult = new Page<>();
            byte[] responseData = httpProxyUtil.forwardRequestAndGetResponse(request, response,
                    proxyConfig.getTargetUrl(), true, crewCertificateQueryDto);
            // 处理响应数据
            if (responseData != null) {
                String responseStr = new String(responseData, "UTF-8");
                log.info("获取到的响应数据: {}", responseStr);

                // 将响应数据转换为对象
                ObjectMapper objectMapper = new ObjectMapper()
                        .setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"))
                        .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                ;
                // 创建TypeReference来指定复杂的泛型类型
                Map<String, Object> resultMap = objectMapper.readValue(responseStr, Map.class);
                Map<String, Object> records = (Map<String, Object>) resultMap.get("result");

                // 提取分页信息
                int total = (int) records.get("total");
                int current = (int) records.get("current");
                int size = (int) records.get("size");

                resultData.put("validCount", records.get("validCount"));
                resultData.put("invalidCount", records.get("invalidCount"));

                List<Map<String, Object>> recordsList = (List<Map<String, Object>>) records.get("records");
                // 创建分页对象
                pageResult.setCurrent(current);
                pageResult.setSize(size);
                pageResult.setTotal(total);

                List<CrewCertificateQueryVo> list = new ArrayList<>();
                if (recordsList != null && !recordsList.isEmpty()) {
                    for (Map<String, Object> record : recordsList) {
                        CrewCertificateQueryVo vo = objectMapper.convertValue(record, CrewCertificateQueryVo.class);
                        list.add(vo);
                    }
                    log.info("获取到的集合数据: {}", list.get(0).getCertificateId());
                    pageResult.setRecords(list);
                    accessLogService.certOperationLog("2", "2", "1", "", list.get(0).getCertificateId());
                } else {
                    log.info("未获取到记录数据");
                }
            }
            resultData.put("records", pageResult.getRecords());
            resultData.put("total", pageResult.getTotal()); // 使用总记录数
            resultData.put("size", pageResult.getSize());
            resultData.put("current", pageResult.getCurrent());
            resultData.put("orders", "");
            resultData.put("searchCount", true);
            resultData.put("pages", pageResult.getPages()); // 使用总记录数计算总页数
            long endTime = System.currentTimeMillis(); // 新增结束时间记录
            log.info("APP船员接口执行总耗时：{} 毫秒", (endTime - startTime)); // 新增耗时日志
            return Result.success(resultData);
        } catch (Exception e) {
            log.error("透传调用外网船员证书查询服务失败", e);
            return ResultUtil.error("系统异常：" + e.getMessage());
        }
    }

    /**
     * 获取机构树形状接口
     *
     * @param orgCode 机构编码
     * @param flag    1 查本级 ， 2查下级
     * @return
     */
    @GetMapping("/deptSon")
    public Result<List<SysDeptInfoResponse>> getSysDept(@RequestParam("orgCode") String orgCode,
            @RequestParam("flag") String flag) {
        return Result.success(sysDeptIambService.deptSonApp(orgCode, flag));
    }
}
