package com.js.hszpt.service;

import com.js.hszpt.mapper.LawWorkflowNodeSetDao;
import com.js.hszpt.entity.LawWorkflowNodeSet;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.core.common.utils.PageUtil;
import com.js.core.common.vo.PageVo;
import com.js.core.common.vo.SearchVo;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.js.hszpt.vo.LawWorkflowNodeInfoTreeVo;
import com.js.hszpt.vo.LawWorkflowNodeSetVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
 
/**
 * 
 * @ClassName:  LawWorkflowNodeSetService    
 * @Description:TODO(工作流模板配置表接口实现)   
 * @author:  System Generation
 *
 */
@Slf4j
@Service
public class LawWorkflowNodeSetService extends ServiceImpl<LawWorkflowNodeSetDao,LawWorkflowNodeSet> {
	@Autowired
	private  LawWorkflowNodeSetDao lawWorkflowNodeSetDao;
	@Autowired
	private  LawWorkflowNodeInfoService lawWorkflowNodeInfoService;

	/**
	 * 多条件分页查询
	 * @param param
	 * @param searchVo
	 * @param pageVo
	 * @return
	 */
	public Page<LawWorkflowNodeSet> findByCondition(LawWorkflowNodeSet param, SearchVo searchVo, PageVo pageVo) {
		Page<LawWorkflowNodeSet> page = PageUtil.initMpPage(pageVo);
		QueryWrapper<LawWorkflowNodeSet> queryWrapper = this.getCondition(param,searchVo);
		return this.page(page, queryWrapper);
	}
	
	/**
	 * 
	 * @Title: findByCondition   
	 * @Description: TODO(多条件列表查询)   
	 * @param param
	 * @param searchVo
	 * @return  List<LawWorkflowNodeSet>      
	 * @throws
	 */
	public List<LawWorkflowNodeSet> findByCondition(LawWorkflowNodeSet param, SearchVo searchVo){
		QueryWrapper<LawWorkflowNodeSet> queryWrapper = this.getCondition(param, searchVo);
		//添加排序字段
		queryWrapper.orderByAsc("create_time");
		
		return this.list(queryWrapper);
	}
	
	
	/**
	 * 
	 * @Title: getCondition   
	 * @Description: TODO(构建自定义查询条件)   
	 * @param param
	 * @param searchVo
	 * @return  QueryWrapper<LawWorkflowNodeSet>      
	 * @throws
	 */
	private QueryWrapper<LawWorkflowNodeSet> getCondition(LawWorkflowNodeSet param, SearchVo searchVo){
		QueryWrapper<LawWorkflowNodeSet> queryWrapper = new QueryWrapper<LawWorkflowNodeSet>();
		//请根据实际字段自行扩展
		 
		// 创建时间
		if (StrUtil.isNotBlank(searchVo.getStartDate()) && StrUtil.isNotBlank(searchVo.getEndDate())) {
			Date start = DateUtil.parse(searchVo.getStartDate());
			Date end = DateUtil.parse(searchVo.getEndDate());
			queryWrapper.between("create_time",start, DateUtil.endOfDay(end));
		}
		return queryWrapper;
		
	}

	/**
	 * 查询工作流模板
	 * @return
	 */
	public List<LawWorkflowNodeSetVo> selectJgWorkflowNodeSet() {
		try {
			// 1. 查询工作流节点集基本信息
			List<LawWorkflowNodeSetVo> lawWorkflowNodeSetVos = lawWorkflowNodeSetDao.selectWorkflowNodeSets();
			if (lawWorkflowNodeSetVos == null) {
				return new ArrayList<>();
			}

			// 2. 查询所有关联的工作流节点信息
			List<LawWorkflowNodeInfoTreeVo> lawWorkflowNodeInfoVos = lawWorkflowNodeSetDao.selectWorkflowNodeInfos();
			if (lawWorkflowNodeInfoVos == null) {
				return lawWorkflowNodeSetVos;
			}

			// 3. 按setId分组节点信息
			Map<String, List<LawWorkflowNodeInfoTreeVo>> setNodeMap = lawWorkflowNodeInfoVos.stream()
					.collect(Collectors.groupingBy(LawWorkflowNodeInfoTreeVo::getLawWorkflowNodeSetId));

			// 4. 设置关联关系
			lawWorkflowNodeSetVos.forEach(e -> {
				List<LawWorkflowNodeInfoTreeVo> nodeInfos = setNodeMap.get(e.getLawWorkflowNodeSetId());
				e.setNodeInfoTreeVos(nodeInfos != null ? nodeInfos : new ArrayList<>());
			});

			return lawWorkflowNodeSetVos;

		} catch (Exception e) {
			log.error("查询工作流模板失败", e);
			return new ArrayList<>();
		}
	}
}