package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 电子证照信息汇聚表
 */
@Data
@TableName("ctf_certificate_data_regen")
public class CtfCertificateDataRegen implements Serializable {

    /**
     * 证照数据主键
     */
    @TableId
    private String dataId;

    /**
     * 电子证照标识码
     */
    private String certificateId;

    /**
     * 目录ID
     */
    private String catalogId;

    /**
     * 目录名称
     */
    private String catalogName;

    /**
     * 模板ID
     */
    private String templateId;

    /**
     * 证照类型名称
     */
    private String certificateType;

    /**
     * 证照类型代码
     */
    private String certificateTypeCode;

    /**
     * 证照颁发机构
     */
    private String issueDept;

    /**
     * 证照颁发机构代码
     */
    private String issueDeptCode;

    /**
     * 证照所属地区编码
     */
    private String certificateAreaCode;

    /**
     * 持证者名称
     */
    private String certificateHolder;

    /**
     * 持证者代码
     */
    private String certificateHolderCode;

    /**
     * 持证者类型
     */
    private String certificateHolderType;

    /**
     * 证照编号
     */
    private String certificateNumber;

    /**
     * 颁证日期
     */
    private String issueDate;

    /**
     * 有效期起始日
     */
    private String validBeginDate;

    /**
     * 有效期截止日
     */
    private String validEndDate;

    /**
     * 照面拓展信息
     */
    private String surfaceData;

    /**
     * 证照状态（-5 异常,-4 撤回,-3 撤销,-2 注销,-1 作废,0 首次生成,1 生成已同步,2 修改未同步,3 过期,4 修改已同步,5 预览）
     */
    private String status;

    /**
     * 登记人员ID
     */
    private String creator;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 最后操作人
     */
    private String operator;

    /**
     * 修改时间
     */
    private String updateTime;

    /**
     * 文件保存位置
     */
    private String filePath;

    /**
     * 同步状态（0-新增未上传,1-已上传，2-更新未上传，3-更新已上传,4-删除未上传,5-删除未上传）
     */
    private String syncStatus;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 登记部门
     */
    private String deptId;

    /**
     * 申请编号
     */
    private String applyNum;

    /**
     * 事项名称
     */
    private String affairName;

    /**
     * 事项类型
     */
    private String affairType;

    /**
     * 服务对象
     */
    private String serveBusiness;

    /**
     * 事项ID
     */
    private String affairId;

    /**
     * 事项编号
     */
    private String affairNum;

    /**
     * 签章类型
     */
    private String qzType;

    /**
     * 证书类别
     */
    private String zzType;

    /**
     * 加签文件备用地址
     */
    private String draftUrl;

    /**
     * 是否预览
     */
    private String isView;

    /**
     * 归档编号
     */
    private String sortName;

    /**
     * 备用字段
     */
    private String col1;

    /**
     * 验证日期
     */
    private String verifyDate;

    /**
     * 验证
     */
    private String verification;

    /**
     * 统一信用代码
     */
    private String creditCode;

    /**
     * 印章名称
     */
    private String sealName;

    /**
     * 数据创建同步时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:sss")
    @TableField(value = "rec_create_time")
    private Date recCreateTime;

    /**
     * 数据更新同步时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date recModifyTime;

    private String createStatus;
}