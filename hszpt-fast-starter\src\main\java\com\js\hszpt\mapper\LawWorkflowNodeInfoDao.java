package com.js.hszpt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.js.hszpt.entity.LawWorkflowNodeInfo;
import com.js.hszpt.vo.LawWorkflowNodeInfoVo;

import java.util.List;

/**
 * 
 * @ClassName:  LawWorkflowNodeInfo   
 * @Description:TODO(工作流模板节点表数据处理层)   
 * @author:   System Generation 
 */
public interface LawWorkflowNodeInfoDao extends BaseMapper<LawWorkflowNodeInfo> {

    List<LawWorkflowNodeInfoVo> getDaoServiceClient(String nodeInfoId);
}