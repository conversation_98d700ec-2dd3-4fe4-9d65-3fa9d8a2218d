package com.js.hszpt.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName: CtfCmsHandbookQueryDTO
 * @Description: 帮助手册查询参数DTO
 * @author: System Generation
 */
@Data
@ApiModel(value = "帮助手册查询参数")
public class CtfCmsHandbookQueryDTO {

    /**
     * 当前页
     */
    @ApiModelProperty(value = "当前页")
    private Integer current = 1;

    /**
     * 每页条数
     */
    @ApiModelProperty(value = "每页条数")
    private Integer size = 10;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 栏目分类 1-船员证照 2-船舶证照
     */
    @ApiModelProperty(value = "栏目分类 1-船员证照 2-船舶证照")
    private String columnType;

    /**
     * 是否发布，0-未发布 1-已发布 2-已下线
     */
    @ApiModelProperty(value = "是否发布，0-未发布 1-已发布 2-已下线")
    private String isRelease;

    /**
     * 语言类型，1-中文 2-英文
     */
    @ApiModelProperty(value = "语言类型，1-中文 2-英文")
    private String langType;
}