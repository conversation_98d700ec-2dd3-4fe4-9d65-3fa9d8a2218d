package com.js.hszpt.dto;

import com.js.hszpt.entity.Certificate;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class NonPersistentDto extends Certificate {

    private String validDate;
    private String IssueDeptDate;
    private String ensureType;
    private String ensureDate;
    private String guarantor;
    private String guarantorArea1;
    private String guarantorArea2;
    private String shipsName;
    private String grossTonnage;
    private String shipsCode;
    private String IMOshipsCode;
    private String shipsArea;
    private String shipsMaster;
    private String issueDept;
    private String IssueDeptArea;
    private String widthCode;
}
