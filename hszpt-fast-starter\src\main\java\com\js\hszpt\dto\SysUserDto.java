package com.js.hszpt.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/8/12 19:19
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class SysUserDto {

    private static final long serialVersionUID = -8017638256179784269L;

    @ApiModelProperty("用户姓名")
    @JsonProperty("name")
    private String name;

    public String getNodeInfoId() {
        return nodeInfoId;
    }

    public void setNodeInfoId(String nodeInfoId) {
        this.nodeInfoId = nodeInfoId;
    }

    private String nodeInfoId;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    @ApiModelProperty("海事机构id")
    private String deptId;

    @ApiModelProperty("海事机构名")
    private String deptName;

    @ApiModelProperty("部门名称")
    private String departmentName;

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    @ApiModelProperty("当前页码")
    @Min(value = 1, message = "页码最小为1")
    private Integer currentPage = 1;

    public Integer getCurrentPage() {
        return currentPage;
    }

    public void setCurrentPage(Integer currentPage) {
        this.currentPage = currentPage;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    @ApiModelProperty("每页条数")
    @Min(value = 1, message = "每页条数最小为1")
    @Max(value = 10, message = "每页条数最大为100")
    private Integer pageSize = 10;

}
