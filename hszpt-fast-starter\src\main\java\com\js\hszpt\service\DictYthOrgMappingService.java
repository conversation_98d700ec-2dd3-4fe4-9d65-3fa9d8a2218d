package com.js.hszpt.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.hszpt.entity.DictYthOrgMapping;
import com.js.hszpt.mapper.DictYthOrgMappingMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class DictYthOrgMappingService extends ServiceImpl<DictYthOrgMappingMapper, DictYthOrgMapping> {


    public String getOrgCode(String srcOrgCode) {
        QueryWrapper<DictYthOrgMapping> queryWrapper = Wrappers.<DictYthOrgMapping>query();
        queryWrapper.lambda()
                .eq(DictYthOrgMapping::getSrcOrgCode, srcOrgCode)
                .last("LIMIT 1");
        DictYthOrgMapping dictYthOrgMapping = this.getOne(queryWrapper, false);
        if (dictYthOrgMapping == null) {
            log.warn("【机构编码转换3.0】未找到海事机构的映射:{}", srcOrgCode);
            return null;
        }
        return dictYthOrgMapping.getOrgCode();
    }

    public List<DictYthOrgMapping> getAll() {
        QueryWrapper<DictYthOrgMapping> queryWrapper = Wrappers.<DictYthOrgMapping>query();
        return this.list(queryWrapper);
    }


    public String getSrcOrgCode(String orgCode) {
        QueryWrapper<DictYthOrgMapping> queryWrapper = Wrappers.<DictYthOrgMapping>query();
        queryWrapper.lambda()
                .eq(DictYthOrgMapping::getSourceTypeCode, "1") //过滤类型为“手工添加”的数据
                .eq(DictYthOrgMapping::getOrgCode, orgCode)
                .last("LIMIT 1");
        DictYthOrgMapping dictYthOrgMapping = this.getOne(queryWrapper, false);
        if (dictYthOrgMapping == null) {
            log.warn("【机构编码转换2.0】未找到海事机构的映射:{}", orgCode);
            return null;
        }
        return dictYthOrgMapping.getSrcOrgCode();
    }
}
