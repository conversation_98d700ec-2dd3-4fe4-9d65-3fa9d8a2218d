package com.js.hszpt.utils;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.js.hszpt.constants.Common;
import com.js.hszpt.entity.*;
import com.js.hszpt.vo.CertificateVo;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class AffairInsuranceUtil {

    public static Map<String,String> getInsuranceDateInfoMap(BizAffairInsurance bizAffairInsurance, BizEgApprove bizEgApprove, boolean flag) {
        // 证书审批时间
        String format;
        if (bizEgApprove != null) {
            format = Common.DateFormat.SDF.format(bizEgApprove.getApprDate() != null ? bizEgApprove.getApprDate() : LocalDateTime.now());
        } else {
            format = Common.DateFormat.SDF.format(new Date());
        }
        // 保险时间
        String effStartDate = bizAffairInsurance.getEffStartDate();
        String effStartHour = bizAffairInsurance.getEffStartHour();
        String deadLine = bizAffairInsurance.getDeadLine();
        String deadLineHour = bizAffairInsurance.getDeadLineHour();
        // 时间类型（北京时间为空） + 开始日期 + 小时
        String startDateStr = StrUtil.isNotBlank(effStartDate) ?
                "{}" + DateUtil.getDateCn(effStartDate) + DateUtil.getHourCn(effStartHour) : "--";
        // 时间类型（北京时间为空） + 截至日期 + 小时
        String endDateStr = StrUtil.isNotBlank(deadLine) ?
                "{}" + DateUtil.getDateCn(deadLine) + DateUtil.getHourCn(deadLineHour) : "--";
        // 北京时间
        if (StrUtil.equals(bizAffairInsurance.getDateType(),"1")) {
            startDateStr = StrUtil.format(startDateStr,"");
            endDateStr = StrUtil.format(endDateStr,"");
        } else { // 格林威治时间
            startDateStr = StrUtil.format(startDateStr,"格林威治时间");
            endDateStr = StrUtil.format(endDateStr,"格林威治时间");
        }
        // 开始日期（英文）
        String startDateStrEn = StrUtil.isNotBlank(effStartDate) ? DateUtil.getDateEn(effStartDate + " " + effStartHour, bizAffairInsurance.getDateType()):" ";
        // 截至日期（英文）
        String endDateStrEn = flag ? DateUtil.getDateEn(deadLine + " " + deadLineHour, bizAffairInsurance.getDateType()) : " ";
        // GMT开始日期
        String startDateStrGMT = StrUtil.isNotBlank(startDateStrEn) ? startDateStrEn : " ";
        // GMT截至日期
        String endDateStrGMT = StrUtil.isNotBlank(endDateStrEn) ? endDateStrEn : " ";
        // 颁证日期(中文)
        String dateStart = StrUtil.isNotBlank(deadLine) ? DateUtil.getDateCn(format) : "--";
        // 颁证日期(英文)
        String dateStartEn = flag ? DateUtil.getDateEn(format, bizAffairInsurance.getDateType()) : " ";
        // 保险类别(中文)
        String insuranceType = "残骸清除责任保险";
        // 保险类别(英文)
        String insuranceTypeEn = flag ? "LIABILITY FOR THE REMOVAL OF WRECKS" : " ";
        // 保险时间(中文)
        String startAndEndDate = startDateStr + " 至" + endDateStr;
        // 保险时间(英文)
        String startAndEndDateEn = flag ? "FROM " + startDateStrGMT + " TO " + endDateStrGMT : " ";
        // 保险机构名称(中文)
        String safetyMechanismName = StrUtil.isNotBlank(bizAffairInsurance.getInsurCompName()) ? bizAffairInsurance.getInsurCompName() : "--";
        // 保险机构名称(英文)
        String safetyMechanismNameEn = flag ? bizAffairInsurance.getInsurCompNameEn() : " ";
        // 保险机构地址(中文)
        String address = StrUtil.isNotBlank(bizAffairInsurance.getInsurCompAddr()) ? bizAffairInsurance.getInsurCompAddr() : "--";
        // 保险机构地址(英文)
        String addressEn = flag ? bizAffairInsurance.getInsurCompAddrEn() : " ";
        return MapUtil.builder(new HashMap<String,String>())
                .put("结束时间(中文)", endDateStr)
                .put("结束时间(英文)", endDateStrEn)
                .put("颁证日期(中文)",dateStart)
                .put("颁证日期(英文)", dateStartEn)
                .put("保险类别(中文)",insuranceType)
                .put("保险类别(英文)", insuranceTypeEn)
                .put("保险时间(中文)",startAndEndDate)
                .put("保险时间(英文)", startAndEndDateEn)
                .put("保险机构名称(中文)",safetyMechanismName)
                .put("保险机构名称(英文)", safetyMechanismNameEn)
                .put("保险机构地址(中文)",address)
                .put("保险机构地址(英文)", addressEn)
                .build();
    }

}
