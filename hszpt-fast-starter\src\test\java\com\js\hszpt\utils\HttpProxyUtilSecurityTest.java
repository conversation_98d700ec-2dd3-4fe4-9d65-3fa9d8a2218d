package com.js.hszpt.utils;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.web.client.RestTemplate;
import com.js.hszpt.config.ProxyConfig;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * HttpProxyUtil安全性测试
 * 验证XSS漏洞修复是否有效
 */
public class HttpProxyUtilSecurityTest {

    @Mock
    private RestTemplate restTemplate;
    
    @Mock
    private ProxyConfig proxyConfig;
    
    @Mock
    private HttpServletRequest request;
    
    @Mock
    private HttpServletResponse response;
    
    private HttpProxyUtil httpProxyUtil;
    
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        httpProxyUtil = new HttpProxyUtil();
        // 使用反射设置私有字段
        try {
            setPrivateField(httpProxyUtil, "restTemplate", restTemplate);
            setPrivateField(httpProxyUtil, "proxyConfig", proxyConfig);
            setPrivateField(httpProxyUtil, "env", "test");
        } catch (Exception e) {
            fail("设置测试环境失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试参数对象，包含可能的XSS攻击载荷
     */
    public static class TestParamObject {
        private String normalParam = "normalValue";
        private String xssParam = "<script>alert('XSS')</script>";
        private String urlParam = "http://example.com?param=value&other=<script>";
        private String specialChars = "测试中文&特殊字符=value";
        
        // Getters
        public String getNormalParam() { return normalParam; }
        public String getXssParam() { return xssParam; }
        public String getUrlParam() { return urlParam; }
        public String getSpecialChars() { return specialChars; }
    }
    
    @Test
    void testUrlEncodingInQueryParams() throws Exception {
        // 准备测试数据
        TestParamObject paramObject = new TestParamObject();
        
        // 模拟请求配置
        when(request.getRequestURI()).thenReturn("/test");
        when(request.getMethod()).thenReturn("GET");
        when(proxyConfig.getTargetUrl()).thenReturn("http://localhost:8080");
        
        // 使用反射调用私有方法来构建查询参数
        Method buildQueryParamsMethod = getPrivateMethod("buildQueryParams", Object.class);
        String queryParams = (String) buildQueryParamsMethod.invoke(httpProxyUtil, paramObject);
        
        // 验证XSS攻击载荷被正确编码
        String expectedXssEncoded = URLEncoder.encode("<script>alert('XSS')</script>", StandardCharsets.UTF_8.toString());
        assertTrue(queryParams.contains(expectedXssEncoded), 
                "XSS攻击载荷应该被URL编码");
        
        // 验证不包含未编码的危险字符
        assertFalse(queryParams.contains("<script>"), 
                "查询参数不应包含未编码的script标签");
        assertFalse(queryParams.contains("alert('XSS')"), 
                "查询参数不应包含未编码的JavaScript代码");
        
        // 验证特殊字符被正确编码
        String expectedSpecialCharsEncoded = URLEncoder.encode("测试中文&特殊字符=value", StandardCharsets.UTF_8.toString());
        assertTrue(queryParams.contains(expectedSpecialCharsEncoded), 
                "特殊字符应该被URL编码");
        
        System.out.println("构建的查询参数: " + queryParams);
    }
    
    @Test
    void testFieldNameEncoding() throws Exception {
        // 创建一个包含特殊字段名的测试对象
        Object paramObject = new Object() {
            @SuppressWarnings("unused")
            public String getField() { return "value"; }
        };
        
        // 验证字段名也会被编码（虽然在正常情况下字段名不会包含特殊字符）
        Method buildQueryParamsMethod = getPrivateMethod("buildQueryParams", Object.class);
        String queryParams = (String) buildQueryParamsMethod.invoke(httpProxyUtil, paramObject);
        
        // 验证基本功能正常
        assertNotNull(queryParams);
        assertTrue(queryParams.contains("field=value") || queryParams.contains(URLEncoder.encode("field", StandardCharsets.UTF_8.toString())));
    }
    
    /**
     * 辅助方法：设置私有字段
     */
    private void setPrivateField(Object target, String fieldName, Object value) throws Exception {
        java.lang.reflect.Field field = target.getClass().getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(target, value);
    }
    
    /**
     * 辅助方法：获取私有方法
     */
    private Method getPrivateMethod(String methodName, Class<?>... paramTypes) throws Exception {
        Method method = httpProxyUtil.getClass().getDeclaredMethod(methodName, paramTypes);
        method.setAccessible(true);
        return method;
    }
    
    @Test
    void testErrorMessageSanitization() throws Exception {
        // 测试错误消息中的XSS攻击载荷是否被正确转义
        String maliciousMessage = "Error: <script>alert('XSS')</script> & \"injection\" test";

        // 使用反射调用私有的sanitizeForJson方法
        Method sanitizeMethod = getPrivateMethod("sanitizeForJson", String.class);
        String sanitizedMessage = (String) sanitizeMethod.invoke(httpProxyUtil, maliciousMessage);

        // 验证危险字符被正确转义
        assertFalse(sanitizedMessage.contains("<script>"),
                "错误消息不应包含未转义的script标签");
        assertFalse(sanitizedMessage.contains("alert('XSS')"),
                "错误消息不应包含未转义的JavaScript代码");

        // 验证特定字符被正确转义
        assertTrue(sanitizedMessage.contains("\\u003c"),
                "小于号应该被转义为Unicode");
        assertTrue(sanitizedMessage.contains("\\u003e"),
                "大于号应该被转义为Unicode");
        assertTrue(sanitizedMessage.contains("\\u0026"),
                "和号应该被转义为Unicode");
        assertTrue(sanitizedMessage.contains("\\\""),
                "双引号应该被转义");

        System.out.println("原始消息: " + maliciousMessage);
        System.out.println("转义后消息: " + sanitizedMessage);
    }

    @Test
    void testNullErrorMessageHandling() throws Exception {
        // 测试null错误消息的处理
        Method sanitizeMethod = getPrivateMethod("sanitizeForJson", String.class);
        String result = (String) sanitizeMethod.invoke(httpProxyUtil, (String) null);

        assertEquals("", result, "null消息应该返回空字符串");
    }

    /**
     * 模拟的buildQueryParams方法，用于测试
     * 这个方法模拟了HttpProxyUtil中构建查询参数的逻辑
     */
    private String buildQueryParams(Object paramObject) throws Exception {
        StringBuilder queryParams = new StringBuilder();
        java.lang.reflect.Field[] fields = paramObject.getClass().getDeclaredFields();
        boolean firstParam = true;

        for (java.lang.reflect.Field field : fields) {
            boolean wasAccessible = field.isAccessible();
            try {
                field.setAccessible(true);
                Object value = field.get(paramObject);
                if (value != null && !value.toString().isEmpty()) {
                    if (firstParam) {
                        queryParams.append("?");
                        firstParam = false;
                    } else {
                        queryParams.append("&");
                    }
                    // 应用修复：对参数名和参数值进行URL编码
                    String encodedFieldName = URLEncoder.encode(field.getName(), StandardCharsets.UTF_8.toString());
                    String encodedValue = URLEncoder.encode(value.toString(), StandardCharsets.UTF_8.toString());
                    queryParams.append(encodedFieldName)
                            .append("=")
                            .append(encodedValue);
                }
            } finally {
                field.setAccessible(wasAccessible);
            }
        }

        return queryParams.toString();
    }
}
