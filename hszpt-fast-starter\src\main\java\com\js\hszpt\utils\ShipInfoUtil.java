package com.js.hszpt.utils;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.js.hszpt.entity.CertShipOwnership;
import com.js.hszpt.entity.DictEgPort;
import com.js.hszpt.entity.ShipInfoAll;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

public class ShipInfoUtil {

    public static Map<String,String> getShipInfoMap(ShipInfoAll shipInfoAll, DictEgPort dictEgPort, CertShipOwnership certShipOwnership) {
        // 国际航线
        boolean flag = StrUtil.equals("2", shipInfoAll.getShipRouteCode());
        // 中文船名
        String finalShipName = StrUtil.isNotBlank(shipInfoAll.getShipName()) ? shipInfoAll.getShipName() : "--";
        // 获取英文船名
        String shipNameEn = flag && shipInfoAll.getShipNameEn() != null ? shipInfoAll.getShipNameEn().replaceAll("&", "＆") : " ";
        finalShipName = finalShipName + "\r\n" + shipNameEn;
        // 总吨
        String shipGrosston = StrUtil.isNotBlank(shipInfoAll.getShipGrosston()) ? shipInfoAll.getShipGrosston() : "--";
        // 船舶编号
        String shipCallSign = StrUtil.isNotBlank(shipInfoAll.getShipCallsign()) ? shipInfoAll.getShipCallsign() : "--";
        // IMO编号
        String shipImo = StrUtil.isNotBlank(shipInfoAll.getShipImo()) ? shipInfoAll.getShipImo() : "--";
        // 船籍港(英文)
        String finalVRegistryPort = StrUtil.isNotBlank(shipInfoAll.getRegportName()) ? shipInfoAll.getRegportName() : "--";
        // 调整：国际航线 需要拼接 英文船籍港
        // 获取英文船籍港
        String portNameEn = flag && StrUtil.isNotBlank(dictEgPort.getPortNameEn()) ? dictEgPort.getPortNameEn().replaceAll(" ","") : " ";
        String vRegistryPortEn = StrUtil.isNotBlank(portNameEn) ? portNameEn.replaceAll("&", "＆") : " ";
        finalVRegistryPort = finalVRegistryPort + "\r\n" + vRegistryPortEn;
        // 船舶所有人和地址(英文)
        String ownerCn = certShipOwnership != null && StringUtils.isNotBlank(certShipOwnership.getShipOwner()) ?
                certShipOwnership.getShipOwner().replaceAll("&", "＆") : "--";
        String addressCn = certShipOwnership != null && StringUtils.isNotBlank(certShipOwnership.getShipOwnerAddr()) ?
                certShipOwnership.getShipOwnerAddr().replaceAll("&", "＆") : "--";
        String finalOwnerAndAddress = ownerCn + "\r\n" + addressCn;
        // 调整：国际航线 需要拼接 英文所有人和地址(英文)
        // 20250318处理值为null 拼接起来变成了 null null字符串
        String shipOwnerEn = flag && certShipOwnership != null && StrUtil.isNotBlank(certShipOwnership.getShipOwnerEn()) ? certShipOwnership.getShipOwnerEn() : " ";
        String shipOwnerAddrEn = flag && certShipOwnership != null && StrUtil.isNotBlank(certShipOwnership.getShipOwnerAddrEn()) ? certShipOwnership.getShipOwnerAddrEn() : " ";
        String finalOwnerAndAddressEn = shipOwnerEn + "\r\n" + shipOwnerAddrEn;
        finalOwnerAndAddress = finalOwnerAndAddress + "\r\n" + finalOwnerAndAddressEn;
        return MapUtil.builder(new HashMap<String,String>())
                .put("中文船名",finalShipName)
                .put("总吨",shipGrosston)
                .put("船舶编号",shipCallSign)
                .put("IMO编号",shipImo)
                .put("船籍港(英文)",finalVRegistryPort)
                .put("船舶所有人和地址(英文)",finalOwnerAndAddress)
                .build();
    }
}
