<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- 修改这里的namespace为正确的Mapper接口路径 -->
<mapper namespace="com.js.hszpt.mapper.LawWorkflowNodeSetDao">

    <!-- 修改resultType为正确的包路径 -->
    <select id="selectWorkflowNodeSets" resultType="com.js.hszpt.vo.LawWorkflowNodeSetVo">
        SELECT
            law_workflow_node_set_id as lawWorkflowNodeSetId,
            ITEMS_NAME as itemsName
        FROM
            LAW_WORKFLOW_NODE_SET
        ORDER BY
            law_workflow_node_set_id ASC
    </select>

    <!-- 修改resultType为正确的包路径 -->
    <select id="selectWorkflowNodeInfos" resultType="com.js.hszpt.vo.LawWorkflowNodeInfoTreeVo">
        SELECT
            law_workflow_node_info_id as lawWorkflowNodeInfoId,
            law_workflow_node_set_id as lawWorkflowNodeSetId,
            node_name as nodeName,
            node_sort as nodeSort,
            node_org_level as nodeOrgLevel
        FROM
            LAW_WORKFLOW_NODE_INFO
    </select>

</mapper>