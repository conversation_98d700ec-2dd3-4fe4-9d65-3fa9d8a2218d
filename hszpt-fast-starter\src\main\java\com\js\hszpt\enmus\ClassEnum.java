package com.js.hszpt.enmus;

import lombok.AllArgsConstructor;
import lombok.Getter;

//游艇证书 证书等级
@Getter
@AllArgsConstructor
public enum ClassEnum {
    SEA("1.所有长度的海上游艇", "1. Yachts of all lengths"),
    SEA2("2.仅限于20米及以下长度的海上游艇", "2. Yachts length of 20 neters or less"),
    INNER_RIVER("1.内河任何适航水域游艇", "I. Yachts in any navigable inland water area"),
    INNER_RIVER2("2.仅限于内河封闭水域的游艇", "2. Yachts in enclosed inland water area only");



    private String name;
    private String nameEn;
}
