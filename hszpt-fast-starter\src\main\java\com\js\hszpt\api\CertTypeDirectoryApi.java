package com.js.hszpt.api;


import com.js.hszpt.entity.CertTypeDirectory;
import com.js.hszpt.service.CertTypeDirectoryService;
import com.js.hszpt.vo.CertTypeDirectoryQueryVO;
import com.js.hszpt.vo.CertTypeDirectoryListVO;
import com.js.hszpt.vo.CertTypeDirectorySaveVO;
import com.js.hszpt.vo.CertTypeDirectoryDetailVO;
import com.js.hszpt.vo.CertTypeDirectoryTreeVO;
import com.js.hszpt.vo.BatchAbolishRequest;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.js.core.api.BaseApiPlus;
import com.js.core.common.utils.ResultUtil;
import com.js.core.common.vo.PageVo;
import com.js.core.common.vo.Result;
import com.js.core.common.vo.SearchVo;
import com.js.annotation.SystemLog;
import com.js.enums.LogType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import org.apache.commons.lang3.StringUtils;


 /**
 *
 * @ClassName: CertTypeDirectoryApi
 * @Description:TODO(证照类型目录表接口)
 * @author:  System Generation
 *
 */
@Slf4j
@RestController
@Api(description = "证照类型目录表接口")
@RequestMapping("/certTypeDirectory")
public class CertTypeDirectoryApi extends BaseApiPlus<CertTypeDirectoryService,CertTypeDirectory,String>{

	@GetMapping("/queryCertTypeDirectoryList")
	@ApiOperation("查询证照类型目录分页列表")
	@SystemLog(description = "查询证照类型目录分页列表", type = LogType.OPERATION)
	public Result<Page<CertTypeDirectoryListVO>> queryCertTypeDirectoryList(CertTypeDirectoryQueryVO queryVO) {
		log.info("开始查询证照类型目录列表，查询条件：{}", queryVO);
		try {
			// 1. 参数校验
			if (queryVO.getPageNumber() <= 0) {
				return ResultUtil.error("页码必须大于0");
			}
			if (queryVO.getPageSize() <= 0) {
				return ResultUtil.error("每页条数必须大于0");
			}

			// 2. 日期格式校验
			if (!validateDateRange(queryVO)) {
				return ResultUtil.error("日期格式不正确或日期范围无效");
			}

			// 3. 执行查询
			Page<CertTypeDirectoryListVO> result = baseService.queryCertTypeDirectoryList(queryVO);

			log.info("查询证照类型目录列表成功，总记录数：{}，当前页数据量：{}",
				result.getTotal(), result.getRecords().size());

			return ResultUtil.data(result);
		} catch (Exception e) {
			log.error("查询证照类型目录列表失败", e);
			return ResultUtil.error("查询证照类型目录列表失败：" + e.getMessage());
		}
	}

	private boolean validateDateRange(CertTypeDirectoryQueryVO queryVO) {
		// 下发日期范围校验
		if (queryVO.getIssueDateStart() != null && queryVO.getIssueDateEnd() != null) {
			if (queryVO.getIssueDateStart().after(queryVO.getIssueDateEnd())) {
				return false;
			}
		}

		// 创建日期范围校验
		if (queryVO.getCreateTimeStart() != null && queryVO.getCreateTimeEnd() != null) {
			if (queryVO.getCreateTimeStart().after(queryVO.getCreateTimeEnd())) {
				return false;
			}
		}

		return true;
	}

	@PostMapping("/saveOrSubmit")
	@ApiOperation("保存或提交证照类型目录")
	@SystemLog(description = "保存或提交证照类型目录", type = LogType.OPERATION)
	public Result<String> saveOrSubmit(@Valid @ModelAttribute CertTypeDirectorySaveVO saveVO) {
		log.info("开始保存或提交证照类型目录，参数：{}", saveVO);
		try {
			return baseService.saveOrSubmit(saveVO);
		} catch (Exception e) {
			log.error("保存或提交证照类型目录失败", e);
			return ResultUtil.error("操作失败：" + e.getMessage());
		}
	}

	@PostMapping("/revoke/{certTypeDirId}")
	@ApiOperation("撤回证照类型目录")
	@SystemLog(description = "撤回证照类型目录", type = LogType.OPERATION)
	public Result<Void> revoke(@PathVariable String certTypeDirId) {
		log.info("开始撤回证照类型目录，ID：{}", certTypeDirId);
		try {
			baseService.revoke(certTypeDirId);
			return ResultUtil.success("撤回成功");
		} catch (IllegalArgumentException e) {
			log.warn("撤回证照类型目录参数错误：{}", e.getMessage());
			return ResultUtil.error(e.getMessage());
		} catch (Exception e) {
			log.error("撤回证照类型目录失败", e);
			return ResultUtil.error("撤回失败：" + e.getMessage());
		}
	}

	@PostMapping("/abolish/{certTypeApprApplyId}")
	@ApiOperation("废止证照类型目录")
	@SystemLog(description = "废止证照类型目录", type = LogType.OPERATION)
	public Result<Void> abolish(@PathVariable String certTypeApprApplyId) {
		log.info("开始废止证照类型目录，ID：{}", certTypeApprApplyId);
		try {
			baseService.abolish(certTypeApprApplyId);
			return ResultUtil.success("废止成功");
		} catch (IllegalArgumentException e) {
			log.warn("废止证照类型目录参数错误：{}", e.getMessage());
			return ResultUtil.error(e.getMessage());
		} catch (Exception e) {
			log.error("废止证照类型目录失败", e);
			return ResultUtil.error("废止失败：" + e.getMessage());
		}
	}

	@PostMapping("/enable/{certTypeApprApplyId}")
	@ApiOperation("启用证照类型目录")
	@SystemLog(description = "启用证照类型目录", type = LogType.OPERATION)
	public Result<Void> enable(@PathVariable String certTypeApprApplyId) {
		log.info("开始启用证照类型目录，ID：{}", certTypeApprApplyId);
		try {
			baseService.enable(certTypeApprApplyId);
			return ResultUtil.success("启用申请提交成功");
		} catch (IllegalArgumentException e) {
			log.warn("启用证照类型目录参数错误：{}", e.getMessage());
			return ResultUtil.error(e.getMessage());
		} catch (Exception e) {
			log.error("启用证照类型目录失败", e);
			return ResultUtil.error("启用申请失败：" + e.getMessage());
		}
	}

	@PostMapping("/delete/{certTypeDirId}")
	@ApiOperation("删除证照类型目录")
	@SystemLog(description = "删除证照类型目录", type = LogType.OPERATION)
	public Result<Void> delete(@PathVariable String certTypeDirId) {
		log.info("开始删除证照类型目录，ID：{}", certTypeDirId);
		try {
			// 1. 参数校验
			if (StringUtils.isBlank(certTypeDirId)) {
				return ResultUtil.error("证照类型目录ID不能为空");
			}

			// 2. 执行删除
			baseService.deleteCertTypeDirectory(certTypeDirId);

			return ResultUtil.success("删除成功");
		} catch (IllegalArgumentException e) {
			log.warn("删除证照类型目录参数错误：{}", e.getMessage());
			return ResultUtil.error(e.getMessage());
		} catch (Exception e) {
			log.error("删除证照类型目录失败", e);
			return ResultUtil.error("删除失败：" + e.getMessage());
		}
	}

	@GetMapping("/getCertTypeDirInfo/{certTypeApprApplyId}")
	@ApiOperation("查询证照类型目录详情")
	@SystemLog(description = "查询证照类型目录详情", type = LogType.OPERATION)
	public Result<CertTypeDirectoryDetailVO> getCertTypeDirInfo(@PathVariable String certTypeApprApplyId) {
		log.info("开始查询证照类型目录详情，ID：{}", certTypeApprApplyId);
		try {
			// 1. 参数校验
			if (StringUtils.isBlank(certTypeApprApplyId)) {
				return ResultUtil.error("证照申请ID不能为空");
			}

			// 2. 执行查询
			CertTypeDirectoryDetailVO result = baseService.getCertTypeDirInfo(certTypeApprApplyId);

			if (result == null || result.getCertTypeDirectory() == null) {
				return ResultUtil.error("未找到对应的证照类型目录信息");
			}

			log.info("查询证照类型目录详情成功");
			return ResultUtil.data(result);
		} catch (Exception e) {
			log.error("查询证照类型目录详情失败", e);
			return ResultUtil.error("查询证照类型目录详情失败：" + e.getMessage());
		}
	}

	@GetMapping("/tree")
	@ApiOperation("获取证照类型目录树形结构")
	@SystemLog(description = "获取证照类型目录树形结构", type = LogType.OPERATION)
	public Result<List<CertTypeDirectoryTreeVO>> getTree() {
		log.info("开始查询证照类型目录树形结构");
		try {
            // 新增缓存机制（可选优化）
            // List<CertTypeDirectoryTreeVO> treeList = cacheService.getTreeCache();
            // if (treeList == null) {
            List<CertTypeDirectoryTreeVO> treeList = baseService.getCertTypeDirectoryTree();
            // cacheService.setTreeCache(treeList);
            return ResultUtil.data(treeList);
		} catch (Exception e) {
			log.error("查询证照类型目录树形结构失败", e);
			return ResultUtil.error("查询证照类型目录树形结构失败：" + e.getMessage());
		}
	}

	@PostMapping(value="/batchAbolish",consumes = "application/json")
	@ApiOperation("批量废止证照类型目录")
	@SystemLog(description = "批量废止证照类型目录", type = LogType.OPERATION)
	public Result<Void> batchAbolish(@RequestBody @Validated BatchAbolishRequest batchAbolishRequest) {
		log.info("开始批量废止证照类型目录，参数：{}", batchAbolishRequest);
		try {
			baseService.batchAbolish(batchAbolishRequest.getCertTypeApprApplyIds());
			return ResultUtil.success("批量废止处理成功");
		} catch (IllegalArgumentException e) {
			log.warn("批量废止证照类型目录参数错误：{}", e.getMessage());
			return ResultUtil.error(e.getMessage());
		} catch (Exception e) {
			log.error("批量废止证照类型目录失败", e);
			return ResultUtil.error("批量废止失败：" + e.getMessage());
		}
	}
}
