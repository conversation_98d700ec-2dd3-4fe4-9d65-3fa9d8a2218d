package com.js.hszpt.vo;

import com.js.hszpt.entity.*;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class OilPollutionCertificateVo {
    //窗口申请表
    private BizAffairApply bizAffairApply;
    //用户表
    private DictEgUser dictEgUser;
    //保险信息表
    private BizAffairInsurance bizAffairInsurance;
    //机构表
    private DictEgOrg dictEgOrg;
    //海事机构部门名称中英文对照表
    private SysDeptEn sysDeptEn;
    //审批意见表
    private BizEgApprove bizEgApprove;
    //业务船舶表
    private BizDgrOpic bizDgrOpic;
    //港口信息
    private DictEgPort dictEgPort;
    //制证校核表
    private BizEgCertCheck bizEgCertCheck;
    //证照分类表
    private CertificateType certificateType;
    //签证官
    private ZwApplyOfficerInfo zwApplyOfficerInfo;
    //机构映射表
//    private DictYthOrgMapping dictYthOrgMapping;
    //数据是否齐全，true为正常，false为异常直接返回
    private Boolean flag = true;
    // 受理信息
    private BizEgAccept bizEgAccept;
}
