package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@TableName("dict_eg_org")
public class DictEgOrg {

    /**
     * 机构部门代码
     */
    @TableId
    private Long deptCode;

    /**
     * 机构部门名称
     */
    @TableField("dept_name")
    private String deptName;

    /**
     * 序号
     */
    @TableField("seq_no")
    private Long seqNo;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    /**
     * 创建日期
     */
    @TableField("create_date")
    private Date createDate;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    /**
     * 修改日期
     */
    @TableField("modify_date")
    private Date modifyDate;

    /**
     * 删除标志代码 0：正常；1：删除
     */
    @TableField("delete_flag_code")
    private String deleteFlagCode;

    /**
     * 父级标识
     */
    @TableField("parent_id")
    private String parentId;

    /**
     * 层级
     */
    @TableField("gov_level")
    private String govLevel;

    /**
     * 编码
     */
    @TableField("code")
    private String code;

    /**
     * 是否展示 0：否；1：是
     */
    @TableField("if_show")
    private String ifShow;

    /**
     * 是否大厅展示 0：否；1：是
     */
    @TableField("if_hall_show")
    private String ifHallShow;

    /**
     * 统一社会信用代码 统一社会信用代码是一组长度为18位的用于法人和其他组织身份识别的代码。
     * 标准规定统一社会信用代码用18位阿拉伯数字或大写英文字母表示，分别是1位登记管理部门代码、1位机构类别代码、
     * 6位登记管理机关行政区划码、9位主体标识码、1位校验码。
     */
    @TableField("uscc")
    private String uscc;

    /**
     * 办理时间
     */
    @TableField("handle_date")
    private String handleDate;

    /**
     * 办理地点
     */
    @TableField("approve_address")
    private String approveAddress;

    /**
     * 咨询方式
     */
    @TableField("consult_type")
    private String consultType;

    /**
     * 监督投诉方式
     */
    @TableField("supervise_type")
    private String superviseType;

    /**
     * 是否中心 0：否；1：是
     */
    @TableField("if_center")
    private String ifCenter;

    /**
     * 部门电话
     */
    @TableField("dept_phone")
    private String deptPhone;

    /**
     * 父级代码
     */
    @TableField("parent_code")
    private String parentCode;

    /**
     * 实施编码
     */
    @TableField("task_code")
    private String taskCode;

    /**
     * 部门类型
     */
    @TableField("dept_type")
    private String deptType;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    /**
     * 记录创建日期
     */
    @TableField("rec_create_date")
    private Date recCreateDate;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    /**
     * 记录修改日期
     */
    @TableField("rec_modify_date")
    private Date recModifyDate;

}
