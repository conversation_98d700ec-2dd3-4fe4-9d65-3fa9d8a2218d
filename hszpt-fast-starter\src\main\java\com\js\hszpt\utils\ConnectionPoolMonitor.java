package com.js.hszpt.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.pool.PoolStats;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * HTTP连接池监控工具类
 * 用于监控连接池使用情况，及时发现连接泄露问题
 * 
 * <AUTHOR>
 * @date 2025-01-31
 */
@Component
@Slf4j
public class ConnectionPoolMonitor {

    @Autowired
    private PoolingHttpClientConnectionManager poolingConnectionManager;

    /**
     * 定时监控连接池状态
     * 每5分钟执行一次
     */
    @Scheduled(fixedRate = 300000) // 5分钟 = 300000毫秒
    public void monitorConnectionPool() {
        try {
            PoolStats totalStats = poolingConnectionManager.getTotalStats();
            
            log.info("=== HTTP连接池状态监控 ===");
            log.info("总连接数: {}", totalStats.getAvailable() + totalStats.getLeased());
            log.info("可用连接数: {}", totalStats.getAvailable());
            log.info("已租用连接数: {}", totalStats.getLeased());
            log.info("等待连接数: {}", totalStats.getPending());
            log.info("最大连接数: {}", totalStats.getMax());
            
            // 计算连接使用率
            int totalConnections = totalStats.getAvailable() + totalStats.getLeased();
            double usageRate = totalConnections > 0 ? (double) totalStats.getLeased() / totalConnections * 100 : 0;
            log.info("连接使用率: {:.2f}%", usageRate);
            
            // 连接泄露告警
            if (totalStats.getLeased() > totalStats.getMax() * 0.8) {
                log.warn("⚠️ 连接池使用率过高！已租用连接数: {}, 最大连接数: {}", 
                    totalStats.getLeased(), totalStats.getMax());
            }
            
            if (totalStats.getPending() > 0) {
                log.warn("⚠️ 有连接请求在等待！等待数量: {}", totalStats.getPending());
            }
            
            log.info("=== 连接池监控结束 ===");
            
        } catch (Exception e) {
            log.error("连接池监控异常", e);
        }
    }

    /**
     * 手动清理空闲和过期连接
     * 每30分钟执行一次
     */
    @Scheduled(fixedRate = 1800000) // 30分钟 = 1800000毫秒
    public void cleanupConnections() {
        try {
            log.info("开始清理HTTP连接池中的空闲和过期连接");
            
            // 关闭过期连接
            poolingConnectionManager.closeExpiredConnections();
            
            // 关闭空闲超过60秒的连接
            poolingConnectionManager.closeIdleConnections(60, java.util.concurrent.TimeUnit.SECONDS);
            
            log.info("HTTP连接池清理完成");
            
        } catch (Exception e) {
            log.error("清理HTTP连接池时发生异常", e);
        }
    }

    /**
     * 获取连接池详细状态信息
     * @return 连接池状态信息
     */
    public String getConnectionPoolStatus() {
        try {
            PoolStats totalStats = poolingConnectionManager.getTotalStats();
            
            StringBuilder status = new StringBuilder();
            status.append("HTTP连接池状态:\n");
            status.append("- 总连接数: ").append(totalStats.getAvailable() + totalStats.getLeased()).append("\n");
            status.append("- 可用连接数: ").append(totalStats.getAvailable()).append("\n");
            status.append("- 已租用连接数: ").append(totalStats.getLeased()).append("\n");
            status.append("- 等待连接数: ").append(totalStats.getPending()).append("\n");
            status.append("- 最大连接数: ").append(totalStats.getMax()).append("\n");
            
            int totalConnections = totalStats.getAvailable() + totalStats.getLeased();
            double usageRate = totalConnections > 0 ? (double) totalStats.getLeased() / totalConnections * 100 : 0;
            status.append("- 连接使用率: ").append(String.format("%.2f%%", usageRate));
            
            return status.toString();
            
        } catch (Exception e) {
            log.error("获取连接池状态时发生异常", e);
            return "获取连接池状态失败: " + e.getMessage();
        }
    }

    /**
     * 检查是否存在连接泄露风险
     * @return true表示存在风险，false表示正常
     */
    public boolean hasConnectionLeakRisk() {
        try {
            PoolStats totalStats = poolingConnectionManager.getTotalStats();
            
            // 如果已租用连接数超过最大连接数的80%，认为存在泄露风险
            return totalStats.getLeased() > totalStats.getMax() * 0.8;
            
        } catch (Exception e) {
            log.error("检查连接泄露风险时发生异常", e);
            return false;
        }
    }

    /**
     * 强制清理所有空闲连接（紧急情况使用）
     */
    public void forceCleanupAllIdleConnections() {
        try {
            log.warn("执行强制清理所有空闲连接");
            
            // 关闭所有空闲连接（0秒超时）
            poolingConnectionManager.closeIdleConnections(0, java.util.concurrent.TimeUnit.SECONDS);
            poolingConnectionManager.closeExpiredConnections();
            
            log.warn("强制清理完成");
            
        } catch (Exception e) {
            log.error("强制清理连接时发生异常", e);
        }
    }
}
