package com.js.hszpt.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.hszpt.constants.Common;
import com.js.hszpt.entity.*;
import com.js.hszpt.mapper.DataStorageTaskMapper;

import com.js.hszpt.vo.SyncDataReceiveVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.function.Function;

import static com.js.hszpt.enmus.DataStorageTaskStatus.RECEPTION;

@Slf4j
@Service
public class DataStorageTaskService extends ServiceImpl<DataStorageTaskMapper, DataStorageTask> {

    @Autowired
    private BizAffairApplyService bizAffairApplyService;
    @Autowired
    private BizAffairInsuranceService bizAffairInsuranceService;
    @Autowired
    private BizEgCertCheckService bizEgCertCheckService;
    @Autowired
    private BizDgrOpicService bizDgrOpicService;
    @Autowired
    private BizEgApproveService bizEgApproveService;
    @Autowired
    private BizEgSendCertService bizEgSendCertService;
    @Autowired
    private DictEgPortService dictEgPortService;
    @Autowired
    private DictEgOrgService dictEgOrgService;
    @Autowired
    private DictEgPublicService dictEgPublicService;
    @Autowired
    private DictEgUserService dictEgUserService;
    @Autowired
    private OfficerInfoService officerInfoService;
    @Autowired
    private DictYthOrgMappingService dictYthOrgMappingService;
    @Autowired
    private BizEgApplyInfoService bizEgApplyInfoService;
    @Autowired
    private CertShipOwnershipService certShipOwnershipService;
    @Autowired
    private ShipInfoAllService shipInfoAllService;
    @Autowired
    private BizEgAcceptService bizEgAcceptService; // 新增注入的 bizEgAcceptService

    @Autowired
    private ZwOfficerInfoService zwOfficerInfoService;

    @Autowired
    private ZwApplyOfficerInfoService zwApplyOfficerInfoService;

    @Autowired
    private CtfCertificateDataRegenService ctfCertificateDataRegenService;

    public List<DataStorageTask> getListByTaskNameAndStorageStatus(String taskName, String storageStatus) {
        QueryWrapper<DataStorageTask> queryWrapper = Wrappers.query();
        queryWrapper.lambda()
                .eq(DataStorageTask::getTaskName, taskName)
                .eq(DataStorageTask::getStorageStatus, storageStatus)
                .orderByAsc(DataStorageTask::getCreateDate);
        return this.list(queryWrapper);
    }

    public boolean updateStorageStatus(String storageTasksId, String storageStatus) {
        DataStorageTask dataStorageTask = new DataStorageTask();
        dataStorageTask.setStorageStatus(storageStatus);
        dataStorageTask.setStorageTasksId(storageTasksId);
        return this.updateById(dataStorageTask);
    }

    //根据taskName 调用对应的service服务
    public Boolean taskDataStorage(String taskName, String path) {
        if ("CTF_OFFICER_INFO".equals(taskName)){//CTF_OFFICER_INFO
            return this.readAndSaveData(path, OfficerInfo.class, officerInfoService::saveOrUpdateBatch);
        }else if ("biz_dgr_opic".equals(taskName)) {//biz_dgr_opic
            return this.readAndSaveData(path, BizDgrOpic.class, bizDgrOpicService::saveOrUpdateBatch);
        } else if ("biz_eg_cert_check".equals(taskName)) {//biz_eg_cert_check---
            return this.readAndSaveData(path, BizEgCertCheck.class, bizEgCertCheckService::saveOrUpdateBatch);
        } else if ("biz_eg_send_cert".equals(taskName)) {//biz_eg_send_cert
            return this.readAndSaveData(path, BizEgSendCert.class, bizEgSendCertService::saveOrUpdateBatch);
        } else if ("biz_affair_insurance".equals(taskName)) {//biz_affair_insurance---
            return this.readAndSaveData(path, BizAffairInsurance.class, bizAffairInsuranceService::saveOrUpdateBatch);
        } else if ("biz_affair_apply".equals(taskName)) {//biz_affair_apply---
            return this.readAndSaveData(path, BizAffairApply.class, bizAffairApplyService::saveOrUpdateBatch);
        } else if ("biz_eg_approve".equals(taskName)) {//biz_eg_approve
            return this.readAndSaveData(path, BizEgApprove.class, bizEgApproveService::saveOrUpdateBatch);
        } else if ("dict_eg_org".equals(taskName)) {//dict_eg_org---
            return this.readAndSaveData(path, DictEgOrg.class, dictEgOrgService::saveOrUpdateBatch);
        } else if ("dict_eg_port".equals(taskName)) {//dict_eg_port---
            return this.readAndSaveData(path, DictEgPort.class, dictEgPortService::saveOrUpdateBatch);
        } else if ("dict_eg_public".equals(taskName)) {//dict_eg_public---
            return this.readAndSaveData(path, DictEgPublic.class, dictEgPublicService::saveOrUpdateBatch);
        } else if ("dict_eg_user".equals(taskName)) {//dict_eg_user---
            return this.readAndSaveData(path, DictEgUser.class, dictEgUserService::saveOrUpdateBatch);
        }else if ("dict_yth_org_mapping".equals(taskName)) {//dict_eg_user---
            return this.readAndSaveData(path, DictYthOrgMapping.class, dictYthOrgMappingService::saveOrUpdateBatch);
        }else if ("biz_eg_apply_info".equals(taskName)) {//biz_eg_apply_info---
            return this.readAndSaveData(path, BizEgApplyInfo.class, bizEgApplyInfoService::saveOrUpdateBatch);
        }else if ("cert_ship_ownership".equals(taskName)) {//cert_ship_ownership---
            return this.readAndSaveData(path, CertShipOwnership.class, certShipOwnershipService::saveOrUpdateBatch);
        }else if ("ship_info_all".equals(taskName)) {//ship_info_all---
            return this.readAndSaveData(path, ShipInfoAll.class, shipInfoAllService::saveOrUpdateBatch);
        } else if ("biz_eg_accept".equals(taskName)) { // 新增 biz_eg_accept 处理逻辑
            return this.readAndSaveData(path, BizEgAccept.class, bizEgAcceptService::saveOrUpdateBatch);
        } else if ("officer_info".equals(taskName)) {
            return this.readAndSaveData(path, ZwOfficerInfo.class, zwOfficerInfoService::saveOrUpdateBatch);
        } else if ("apply_officer_info".equals(taskName)) {
            return this.readAndSaveData(path, ZwApplyOfficerInfo.class, zwApplyOfficerInfoService::saveOrUpdateBatch);
        } else if (StrUtil.equals("ctf_certificate_data_regen",taskName)) {
            return this.readAndSaveData(path, CtfCertificateDataRegen.class, ctfCertificateDataRegenService::saveOrUpdateBatch);
        }
        return false;
    }

    /**
     * 每种类型单独调用，读取txt文件中的JSON数据 插入数据库中
     *
     * @param path       路径
     * @param tClass     保存类型
     * @param saveMethod 保存方法
     * @param <T>        实体类 (保存类型)
     * @return 是否插入成功
     */
    public <T> Boolean readAndSaveData(String path, Class<T> tClass, Function<List<T>, Boolean> saveMethod) {
        // 使用 Java 标准库替代 Hutool 的 FileUtil
        File folder = new File(path);
        // 检查文件夹是否存在
        if (!folder.exists() || !folder.isDirectory()) {
            log.error("{}文件夹不存在", path);
            return true;
        }
        
        // 获取文件夹中的所有文件
        File[] files = folder.listFiles();
        List<File> fileList = files != null ? Arrays.asList(files) : new ArrayList<>();
        log.info("[{}]文件夹内含有{}个待入库文件", path, fileList.size());
        
        try {
            for (File file : fileList) {
                log.info("开始处理文件：{}", file.getName());
                List<T> list = new ArrayList<>();
                String string = readFileContent(file);
                JSONArray jsonList = JSON.parseObject(string).getJSONObject("resultObject").getJSONArray("list");
                for (Object item : jsonList) {
                    try {
                        T t = JSON.parseObject(item.toString(), tClass);
                        // 处理特定字段
                        handleOrgCodeFields(t);
                        if (t != null) {
                            list.add(t);
                        } else {
                            log.warn("从文件中解析出的实体对象为null，已跳过");
                        }
                    } catch (Exception e) {
                        log.error("----------[{}]数据解析异常，数据内容：{}~~~", file.getName(), item);
                        e.printStackTrace();
                    }
                }
                //调用保存接口
                boolean saveFlag = false;
                try {
                    saveFlag = saveMethod.apply(list);
                } catch (Exception e) {
                    log.error("----------[{}]数据保存异常~~~", file.getName());
                    e.printStackTrace();
                }

                if (!saveFlag) {//保存失败
                    log.error("----------[{}]数据保存失败~~~", file.getName());
                    // 如果有错误，只保存一条数据
                    if (!list.isEmpty()) {
                        for(int i = 0; i < list.size(); i++) {
                            T singleItem = list.get(i);
                            try {
                                saveSingleItemByEntityType(singleItem, tClass);
                            } catch (Exception e) {
                                log.error("----------[{}]单条数据保存异常，异常原因：{}", file.getName(), e.getMessage());
                                e.printStackTrace();
                                // 打印单条数据保存异常的数据内容
                                log.error("----------[{}]单条数据保存异常的内容：{}", file.getName(), JSON.toJSONString(singleItem));
                            }
                        }
                    }
                } else {
                    log.info("----------[{}]数据保存成功~~~，插入{}条数据", file.getName(), list.size());
                    FileUtil.del(file);
                    log.info("----------删除文件:{}", file.getName());
                }
                log.info("完成文件处理：{}", file.getName());
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("读取[{}]内容，插入数据库失败。失败原因:{}", path, e.getMessage());
            return false;
        }
    }

    // 添加辅助方法读取文件内容
    private String readFileContent(File file) throws IOException {
        StringBuilder content = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(new FileInputStream(file), StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                content.append(line);
            }
        }
        return content.toString();
    }

    // 新增方法：处理特定字段
    private <T> void handleOrgCodeFields(T t) {
        if (t instanceof BizAffairApply) {
            BizAffairApply apply = (BizAffairApply) t;
            apply.setAcceptOrgCode(handleCode(apply.getAcceptOrgCode()));
            apply.setApprOrgCode(handleCode(apply.getApprOrgCode()));
            apply.setAuditOrgCode(handleCode(apply.getAuditOrgCode()));
            apply.setCreatorOrgCode(handleCode(apply.getCreatorOrgCode()));
            apply.setMsaOrgCode(handleCode(apply.getMsaOrgCode()));
        } else if (t instanceof BizAffairInsurance) {
            BizAffairInsurance insurance = (BizAffairInsurance) t;
            insurance.setCreatorOrgCode(handleCode(insurance.getCreatorOrgCode()));
            insurance.setMsaOrgCode(handleCode(insurance.getMsaOrgCode()));
        } else if (t instanceof BizDgrOpic) {
            BizDgrOpic opic = (BizDgrOpic) t;
            opic.setOrgCode(handleCode(opic.getOrgCode()));
            opic.setCreatorOrgCode(handleCode(opic.getCreatorOrgCode()));
            opic.setMsaOrgCode(handleCode(opic.getMsaOrgCode()));
        } else if (t instanceof BizEgAccept) {
            BizEgAccept accept = (BizEgAccept) t;
            accept.setCreatorOrgCode(handleCode(accept.getCreatorOrgCode()));
            accept.setMsaOrgCode(handleCode(accept.getMsaOrgCode()));
        } else if (t instanceof BizEgApplyInfo) {
            BizEgApplyInfo applyInfo = (BizEgApplyInfo) t;
            applyInfo.setAcceptOrgCode(handleCode(applyInfo.getAcceptOrgCode()));
            applyInfo.setAuditOrgCode(handleCode(applyInfo.getAuditOrgCode()));
            applyInfo.setApprOrgCode(handleCode(applyInfo.getApprOrgCode()));
            applyInfo.setMsaOrgCode(handleCode(applyInfo.getMsaOrgCode()));
        } else if (t instanceof BizEgApprove) {
            BizEgApprove approve = (BizEgApprove) t;
            approve.setCreatorOrgCode(handleCode(approve.getCreatorOrgCode()));
            approve.setMsaOrgCode(handleCode(approve.getMsaOrgCode()));
        } else if (t instanceof BizEgCertCheck) {
            BizEgCertCheck certCheck = (BizEgCertCheck) t;
            certCheck.setCreatorOrgCode(handleCode(certCheck.getCreatorOrgCode()));
            certCheck.setMsaOrgCode(handleCode(certCheck.getMsaOrgCode()));
        } else if (t instanceof BizEgSendCert) {
            BizEgSendCert sendCert = (BizEgSendCert) t;
            sendCert.setCreatorOrgCode(handleCode(sendCert.getCreatorOrgCode()));
            sendCert.setMsaOrgCode(handleCode(sendCert.getMsaOrgCode()));
        }
    }

    // 新增方法：处理字段值
    private String handleCode(String code) {
        if (code != null && code.length() < 8 && Common.codeMap.containsKey(code)) {
            return Common.codeMap.get(code);
        }
        return code;
    }

    /**
     * 构建数据存储任务对象
     *
     * @param syncDataReceiveVo 请求接口参数 取开始任务名称 开始时间、结束时间
     * @param baseFilePath 文件根路径
     * @return 数据存储对象
     */
    public DataStorageTask buildDataStorageTask(SyncDataReceiveVo syncDataReceiveVo, String baseFilePath) {
        Date date = new Date();
        DataStorageTask dataStorageTask = new DataStorageTask();
        dataStorageTask.setStorageTasksId(IdUtil.randomUUID());
        dataStorageTask.setTaskName(syncDataReceiveVo.getTaskName());
        dataStorageTask.setTargetFolderPath(baseFilePath);
        dataStorageTask.setStorageStatus(RECEPTION.getCode());
        dataStorageTask.setCreateFileCount("0");
        dataStorageTask.setDataStartTime(DateUtil.parse(syncDataReceiveVo.getStartDate(),"yyyy-MM-dd HH:mm:ss"));
        dataStorageTask.setDataEndTime(DateUtil.parse(syncDataReceiveVo.getEndDate(),"yyyy-MM-dd HH:mm:ss"));
        dataStorageTask.setCreateDate(date);
        dataStorageTask.setModityDate(date);
        return dataStorageTask;
    }

    /**
     * 根据实体类型直接保存单条数据
     * @param item 单条数据
     * @param clazz 实体类型
     * @param <T> 泛型类型
     */
    private <T> void saveSingleItemByEntityType(T item, Class<T> clazz) {
        try {
            if (item == null) {
                log.error("保存的单条数据为空");
                return;
            }
            
            List<T> typedList = new ArrayList<>();
            typedList.add(item);
            
            // 增加检查，确保没有null值
            if (typedList.isEmpty()) {
                log.error("待保存的列表为空");
                return;
            }
            
            log.info("尝试保存单条数据，类型: {}, 数据: {}", clazz.getSimpleName(), JSON.toJSONString(item));
            
            if (clazz == OfficerInfo.class && officerInfoService != null) {
                officerInfoService.saveOrUpdateBatch((List<OfficerInfo>)(List<?>)typedList);
            } else if (clazz == BizDgrOpic.class && bizDgrOpicService != null) {
                bizDgrOpicService.saveOrUpdateBatch((List<BizDgrOpic>)(List<?>)typedList);
            } else if (clazz == BizEgCertCheck.class && bizEgCertCheckService != null) {
                bizEgCertCheckService.saveOrUpdateBatch((List<BizEgCertCheck>)(List<?>)typedList);
            } else if (clazz == BizEgSendCert.class && bizEgSendCertService != null) {
                bizEgSendCertService.saveOrUpdateBatch((List<BizEgSendCert>)(List<?>)typedList);
            } else if (clazz == BizAffairInsurance.class && bizAffairInsuranceService != null) {
                bizAffairInsuranceService.saveOrUpdateBatch((List<BizAffairInsurance>)(List<?>)typedList);
            } else if (clazz == BizAffairApply.class && bizAffairApplyService != null) {
                // 处理BizAffairApply数据，保留历史数据的clean_status字段值
                if (!typedList.isEmpty()) {
                    BizAffairApply newItem = (BizAffairApply)typedList.get(0);
                    // 查询数据库中是否已存在该记录
                    BizAffairApply existingItem = bizAffairApplyService.getById(newItem.getApplyId());
                    if (existingItem != null && existingItem.getCleanStatus() != null) {
                        // 如果存在且已有clean_status值，则保留原值
                        newItem.setCleanStatus(existingItem.getCleanStatus());
                        log.info("保留BizAffairApply历史数据的clean_status值: {}", existingItem.getCleanStatus());
                    }
                }
                bizAffairApplyService.saveOrUpdateBatch((List<BizAffairApply>)(List<?>)typedList);
            } else if (clazz == BizEgApprove.class && bizEgApproveService != null) {
                bizEgApproveService.saveOrUpdateBatch((List<BizEgApprove>)(List<?>)typedList);
            } else if (clazz == DictEgOrg.class && dictEgOrgService != null) {
                dictEgOrgService.saveOrUpdateBatch((List<DictEgOrg>)(List<?>)typedList);
            } else if (clazz == DictEgPort.class && dictEgPortService != null) {
                dictEgPortService.saveOrUpdateBatch((List<DictEgPort>)(List<?>)typedList);
            } else if (clazz == DictEgPublic.class && dictEgPublicService != null) {
                dictEgPublicService.saveOrUpdateBatch((List<DictEgPublic>)(List<?>)typedList);
            } else if (clazz == DictEgUser.class && dictEgUserService != null) {
                dictEgUserService.saveOrUpdateBatch((List<DictEgUser>)(List<?>)typedList);
            } else if (clazz == DictYthOrgMapping.class && dictYthOrgMappingService != null) {
                dictYthOrgMappingService.saveOrUpdateBatch((List<DictYthOrgMapping>)(List<?>)typedList);
            } else if (clazz == BizEgApplyInfo.class && bizEgApplyInfoService != null) {
                bizEgApplyInfoService.saveOrUpdateBatch((List<BizEgApplyInfo>)(List<?>)typedList);
            } else if (clazz == CertShipOwnership.class && certShipOwnershipService != null) {
                certShipOwnershipService.saveOrUpdateBatch((List<CertShipOwnership>)(List<?>)typedList);
            } else if (clazz == ShipInfoAll.class && shipInfoAllService != null) {
                shipInfoAllService.saveOrUpdateBatch((List<ShipInfoAll>)(List<?>)typedList);
            } else if (clazz == BizEgAccept.class && bizEgAcceptService != null) {
                bizEgAcceptService.saveOrUpdateBatch((List<BizEgAccept>)(List<?>)typedList);
            }
            log.info("单条数据保存成功");
        } catch (Exception e) {
            log.error("通过实体类型直接保存单条数据失败: {}", e.getMessage());
            e.printStackTrace();
        }
    }

}
