package com.js.hszpt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.js.hszpt.entity.LawWorkflowNodeSet;
import com.js.hszpt.vo.LawWorkflowNodeInfoTreeVo;
import com.js.hszpt.vo.LawWorkflowNodeSetVo;

import java.util.List;

/**
 * 
 * @ClassName:  LawWorkflowNodeSet   
 * @Description:TODO(工作流模板配置表数据处理层)   
 * @author:   System Generation 
 */
public interface LawWorkflowNodeSetDao extends BaseMapper<LawWorkflowNodeSet> {

    List<LawWorkflowNodeSetVo> selectWorkflowNodeSets();

    List<LawWorkflowNodeInfoTreeVo> selectWorkflowNodeInfos();
}