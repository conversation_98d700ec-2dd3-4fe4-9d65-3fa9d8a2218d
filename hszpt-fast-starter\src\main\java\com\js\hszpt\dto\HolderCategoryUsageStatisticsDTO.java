package com.js.hszpt.dto;

import lombok.Data;

/**
 * 持证主体类别使用统计结果带占比DTO
 */
@Data
public class HolderCategoryUsageStatisticsDTO {

    /**
     * 持证主体类别代码
     */
    private String holderCategoryCode;
    
    /**
     * 持证主体类别名称
     */
    private String holderCategoryName;
    
    /**
     * 使用数量总和
     */
    private Long usageCount;
    
    /**
     * 使用数量占比（百分比）
     */
    private Double usageRatio;
    
    /**
     * 是否为汇总数据
     */
    private Boolean isSummary = false;
} 