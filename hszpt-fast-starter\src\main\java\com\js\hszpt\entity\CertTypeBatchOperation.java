package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.js.core.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.Builder;
import java.util.Date;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import com.js.util.SnowFlakeUtil;


/**
 * 
 * @ClassName:  CertTypeBatchOperation   
 * @Description:TODO(批量操作记录表)   
 * @author:   System Generation 
 */
@Data

@TableName("CERT_TYPE_BATCH_OPERATION")
@ApiModel(value = "批量操作记录表")
public class CertTypeBatchOperation extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 批量操作记录主键ID
     */
    @TableId(value = "CERT_TYPE_BATCH_OPER_ID", type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value = "批量操作记录主键ID")
    private String id = String.valueOf(SnowFlakeUtil.nextId());

    /**
     * 批量操作的类型 1-通过 2-不通过 3-退回
     */
    @TableField("OPERATION_TYPE")
    @ApiModelProperty(value = "批量操作的类型 1-批量废止 2-批量删除")
    private String operationType;

    /**
    * 本次批量操作涉及的记录数量
    */
    @ApiModelProperty(value = "本次批量操作涉及的记录数量")
    private Integer recordCount;


    /**
    * 批量操作成功的记录数量
    */
    @ApiModelProperty(value = "批量操作成功的记录数量")
    private Integer successCount;


    /**
    * 批量操作失败的记录数量
    */
    @ApiModelProperty(value = "批量操作失败的记录数量")
    private Integer failCount;


    /**
    * 批量操作的整体结果
    */
    @ApiModelProperty(value = "批量操作的整体结果")
    private String operationResult;


    /**
    * 批量操作的备注信息
    */
    @ApiModelProperty(value = "批量操作的备注信息")
    private String remarks;


    /**
    * 删除标志：0-正常，1-已删除
    */
    @ApiModelProperty(value = "删除标志：0-正常，1-已删除")
    private String delFlag;


}