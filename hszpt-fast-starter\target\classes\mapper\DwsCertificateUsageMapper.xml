<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.js.hszpt.mapper.DwsCertificateUsageMapper">

    <!-- 按下级机构统计使用情况 -->
    <select id="statisticsBySubOrg" resultType="com.js.hszpt.dto.SubOrgUsageStatisticsDTO">
        SELECT
        d.DEPT_ID as orgCode,
        d.NAME as orgName,
        SUM(u.usage_count) as usageCount
        FROM
        dws_certificate_usage u
        JOIN
        sys_dept d ON u.issuer_code LIKE CONCAT(d.DEPT_ID, '%')
        WHERE
        d.PARENT_ID = #{param.loginUserOrgCode}
        AND u.issuer_code LIKE CONCAT(#{param.loginUserOrgCode}, '%')
        <if test="param.issuerCodes != null and param.issuerCodes.size() > 0">
            AND (
            <foreach collection="param.issuerCodes" item="issuerCode" separator=" OR ">
                u.issuer_code LIKE CONCAT(#{issuerCode}, '%')
            </foreach>
            )
        </if>
        <if test="param.certificateTypeCodes != null and param.certificateTypeCodes.size() > 0">
            AND EXISTS (
            SELECT 1 FROM ert_type_directory etd_filter
            WHERE etd_filter.CERTIFICATE_TYPE_CODE = u.certificate_type_code
            AND (
            <foreach collection="param.certificateTypeCodes" item="typeCode" separator=" OR ">
                etd_filter.CERTIFICATE_TYPE_CODE = #{typeCode}
                OR EXISTS (
                SELECT 1 FROM ert_type_directory etd_child
                WHERE etd_child.CERTIFICATE_TYPE_CODE = etd_filter.CERTIFICATE_TYPE_CODE
                START WITH etd_child.PARENT_ID = (
                SELECT etd_parent.CERT_TYPE_DIR_ID
                FROM ert_type_directory etd_parent
                WHERE etd_parent.CERTIFICATE_TYPE_CODE = #{typeCode}
                )
                CONNECT BY PRIOR etd_child.CERT_TYPE_DIR_ID = etd_child.PARENT_ID
                )
            </foreach>
            )
            )
        </if>
        <if test="param.holderCategoryCodes != null and param.holderCategoryCodes.size() > 0">
            AND u.holder_category_code IN
            <foreach collection="param.holderCategoryCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.matterNatureCodes != null and param.matterNatureCodes.size() > 0">
            AND u.matter_nature_code IN
            <foreach collection="param.matterNatureCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.applicationSourceCode != null and param.applicationSourceCode != ''">
            AND u.application_source_code = #{param.applicationSourceCode}
        </if>
        <if test="param.statusCode != null and param.statusCode != ''">
            AND u.status_code = #{param.statusCode}
        </if>
        <if test="param.usageType != null and param.usageType != ''">
            AND u.usage_type = #{param.usageType}
        </if>
        <if test="param.timeType == 'year' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND SUBSTR(u.ds_certificate_date, 1, 4) BETWEEN #{param.startTime} AND #{param.endTime}
        </if>
        <if test="param.timeType == 'quarter' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND CONCAT(SUBSTR(u.ds_certificate_date, 1, 4),
            CASE
            WHEN SUBSTR(u.ds_certificate_date, 5, 2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(u.ds_certificate_date, 5, 2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(u.ds_certificate_date, 5, 2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(u.ds_certificate_date, 5, 2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
            BETWEEN
            CONCAT(SUBSTR(#{param.startTime}, 1, 4),
            CASE
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
            AND
            CONCAT(SUBSTR(#{param.endTime}, 1, 4),
            CASE
            WHEN SUBSTR(#{param.endTime}, 5, 2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(#{param.endTime}, 5, 2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(#{param.endTime}, 5, 2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(#{param.endTime}, 5, 2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
        </if>
        <if test="param.timeType == 'month' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND SUBSTR(u.ds_certificate_date, 1, 6) BETWEEN REPLACE(#{param.startTime}, '-', '') AND REPLACE(#{param.endTime}, '-', '')
        </if>
        <if test="param.timeType == 'day' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND u.ds_certificate_date BETWEEN REPLACE(#{param.startTime}, '-', '') AND REPLACE(#{param.endTime}, '-', '')
        </if>
        <if test="param.relatedMatterName != null and param.relatedMatterName != ''">
            <if test="param.relatedMatterName.startsWith('#') and param.relatedMatterName.endsWith('%')">
                AND EXISTS (
                SELECT 1 FROM ctf_certificate_type cct
                WHERE cct.CERTIFICATE_TYPE_ID = u.certificate_id
                AND cct.DESCRIPTION LIKE #{param.relatedMatterName}
                )
            </if>
            <if test="!param.relatedMatterName.startsWith('#') or !param.relatedMatterName.endsWith('%')">
                AND EXISTS (
                SELECT 1 FROM ctf_certificate_type cct
                WHERE cct.CERTIFICATE_TYPE_ID = u.certificate_id
                AND cct.DESCRIPTION IN
                <foreach collection="param.relatedMatterName.split(',')" item="name" open="(" separator="," close=")">
                    #{name}
                </foreach>
                )
            </if>
        </if>
        <if test="param.certificateIds != null and param.certificateIds.size() > 0">
            AND u.certificate_id IN
            <foreach collection="param.certificateIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        GROUP BY d.DEPT_ID, d.NAME
        ORDER BY usageCount DESC
    </select>

    <!-- 根据机构编码获取机构名称 -->
    <select id="getOrgNameByOrgCode" resultType="java.lang.String">
        SELECT NAME FROM sys_dept WHERE DEPT_ID = #{orgCode}
    </select>

    <!-- 获取机构的直接下级机构列表 -->
    <select id="getDirectSubOrgCodes" resultType="java.lang.String">
        SELECT DEPT_ID FROM sys_dept WHERE PARENT_ID = #{parentOrgCode} AND DEL_FLAG = '0'
    </select>

    <!-- 按年份和下级机构统计使用情况 -->
    <select id="statisticsByYearAndSubOrg" resultType="com.js.hszpt.dto.TimeTypeSubOrgUsageStatisticsDTO">
        SELECT
        d.DEPT_ID as orgCode,
        d.NAME as orgName,
        SUBSTR(u.ds_certificate_date, 1, 4) as year,
        SUM(u.usage_count) as usageCount
        FROM
        dws_certificate_usage u
        JOIN
        sys_dept d ON u.issuer_code LIKE CONCAT(d.DEPT_ID, '%')
        WHERE
        d.PARENT_ID = #{param.loginUserOrgCode}
        AND u.issuer_code LIKE CONCAT(#{param.loginUserOrgCode}, '%')
        <if test="param.issuerCodes != null and param.issuerCodes.size() > 0">
            AND (
            <foreach collection="param.issuerCodes" item="issuerCode" separator=" OR ">
                u.issuer_code LIKE CONCAT(#{issuerCode}, '%')
            </foreach>
            )
        </if>
        <if test="param.certificateTypeCodes != null and param.certificateTypeCodes.size() > 0">
            AND EXISTS (
            SELECT 1 FROM ert_type_directory etd_filter
            WHERE etd_filter.CERTIFICATE_TYPE_CODE = u.certificate_type_code
            AND (
            <foreach collection="param.certificateTypeCodes" item="typeCode" separator=" OR ">
                etd_filter.CERTIFICATE_TYPE_CODE = #{typeCode}
                OR EXISTS (
                SELECT 1 FROM ert_type_directory etd_child
                WHERE etd_child.CERTIFICATE_TYPE_CODE = etd_filter.CERTIFICATE_TYPE_CODE
                START WITH etd_child.PARENT_ID = (
                SELECT etd_parent.CERT_TYPE_DIR_ID
                FROM ert_type_directory etd_parent
                WHERE etd_parent.CERTIFICATE_TYPE_CODE = #{typeCode}
                )
                CONNECT BY PRIOR etd_child.CERT_TYPE_DIR_ID = etd_child.PARENT_ID
                )
            </foreach>
            )
            )
        </if>
        <if test="param.holderCategoryCodes != null and param.holderCategoryCodes.size() > 0">
            AND u.holder_category_code IN
            <foreach collection="param.holderCategoryCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.matterNatureCodes != null and param.matterNatureCodes.size() > 0">
            AND u.matter_nature_code IN
            <foreach collection="param.matterNatureCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.applicationSourceCode != null and param.applicationSourceCode != ''">
            AND u.application_source_code = #{param.applicationSourceCode}
        </if>
        <if test="param.statusCode != null and param.statusCode != ''">
            AND u.status_code = #{param.statusCode}
        </if>
        <if test="param.usageType != null and param.usageType != ''">
            AND u.usage_type = #{param.usageType}
        </if>
        <if test="param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND SUBSTR(u.ds_certificate_date, 1, 4) BETWEEN #{param.startTime} AND #{param.endTime}
        </if>
        <if test="param.relatedMatterName != null and param.relatedMatterName != ''">
            <if test="param.relatedMatterName.startsWith('#') and param.relatedMatterName.endsWith('%')">
                AND EXISTS (
                SELECT 1 FROM ctf_certificate_type cct
                WHERE cct.CERTIFICATE_TYPE_ID = u.certificate_id
                AND cct.DESCRIPTION LIKE #{param.relatedMatterName}
                )
            </if>
            <if test="!param.relatedMatterName.startsWith('#') or !param.relatedMatterName.endsWith('%')">
                AND EXISTS (
                SELECT 1 FROM ctf_certificate_type cct
                WHERE cct.CERTIFICATE_TYPE_ID = u.certificate_id
                AND cct.DESCRIPTION IN
                <foreach collection="param.relatedMatterName.split(',')" item="name" open="(" separator="," close=")">
                    #{name}
                </foreach>
                )
            </if>
        </if>
        <if test="param.certificateIds != null and param.certificateIds.size() > 0">
            AND u.certificate_id IN
            <foreach collection="param.certificateIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        GROUP BY d.DEPT_ID, d.NAME, SUBSTR(u.ds_certificate_date, 1, 4)
        ORDER BY d.DEPT_ID, year
    </select>

    <!-- 按下级机构统计使用情况并计算占比 -->
    <select id="statisticsBySubOrgWithRatio" resultType="com.js.hszpt.dto.SubOrgUsageRatioStatisticsDTO">
        SELECT
        d.DEPT_ID as orgCode,
        d.NAME as orgName,
        SUM(u.usage_count) as usageCount,
        0.0 as usageRatio,
        0 as isSummary
        FROM
        dws_certificate_usage u
        JOIN
        sys_dept d ON u.issuer_code LIKE CONCAT(d.DEPT_ID, '%')
        WHERE
        d.PARENT_ID = #{param.loginUserOrgCode}
        AND u.issuer_code LIKE CONCAT(#{param.loginUserOrgCode}, '%')
        <if test="param.issuerCodes != null and param.issuerCodes.size() > 0">
            AND (
            <foreach collection="param.issuerCodes" item="issuerCode" separator=" OR ">
                u.issuer_code LIKE CONCAT(#{issuerCode}, '%')
            </foreach>
            )
        </if>
        <if test="param.certificateTypeCodes != null and param.certificateTypeCodes.size() > 0">
            AND EXISTS (
            SELECT 1 FROM ert_type_directory etd_filter
            WHERE etd_filter.CERTIFICATE_TYPE_CODE = u.certificate_type_code
            AND (
            <foreach collection="param.certificateTypeCodes" item="typeCode" separator=" OR ">
                etd_filter.CERTIFICATE_TYPE_CODE = #{typeCode}
                OR EXISTS (
                SELECT 1 FROM ert_type_directory etd_child
                WHERE etd_child.CERTIFICATE_TYPE_CODE = etd_filter.CERTIFICATE_TYPE_CODE
                START WITH etd_child.PARENT_ID = (
                SELECT etd_parent.CERT_TYPE_DIR_ID
                FROM ert_type_directory etd_parent
                WHERE etd_parent.CERTIFICATE_TYPE_CODE = #{typeCode}
                )
                CONNECT BY PRIOR etd_child.CERT_TYPE_DIR_ID = etd_child.PARENT_ID
                )
            </foreach>
            )
            )
        </if>
        <if test="param.holderCategoryCodes != null and param.holderCategoryCodes.size() > 0">
            AND u.holder_category_code IN
            <foreach collection="param.holderCategoryCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.matterNatureCodes != null and param.matterNatureCodes.size() > 0">
            AND u.matter_nature_code IN
            <foreach collection="param.matterNatureCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.applicationSourceCode != null and param.applicationSourceCode != ''">
            AND u.application_source_code = #{param.applicationSourceCode}
        </if>
        <if test="param.statusCode != null and param.statusCode != ''">
            AND u.status_code = #{param.statusCode}
        </if>
        <if test="param.usageType != null and param.usageType != ''">
            AND u.usage_type = #{param.usageType}
        </if>
        <if test="param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            <choose>
                <when test="param.timeType == 'year'">
                    AND SUBSTR(u.ds_certificate_date, 1, 4) BETWEEN #{param.startTime} AND #{param.endTime}
                </when>
                <when test="param.timeType == 'quarter'">
                    AND (
                    SUBSTR(u.ds_certificate_date, 1, 4) > #{param.startTime}
                    OR (
                    SUBSTR(u.ds_certificate_date, 1, 4) = #{param.startTime}
                    AND CEIL(TO_NUMBER(SUBSTR(u.ds_certificate_date, 5, 2)) / 3) &gt;= CEIL(TO_NUMBER(SUBSTR(#{param.startTime}, 5, 2)) / 3)
                    )
                    )
                    AND (
                    SUBSTR(u.ds_certificate_date, 1, 4) &lt;#{param.endTime}
                    OR (
                    SUBSTR(u.ds_certificate_date, 1, 4) = #{param.endTime}
                    AND CEIL(TO_NUMBER(SUBSTR(u.ds_certificate_date, 5, 2)) / 3) &lt;= CEIL(TO_NUMBER(SUBSTR(#{param.endTime}, 5, 2)) / 3)
                    )
                    )
                </when>
                <when test="param.timeType == 'month'">
                    AND SUBSTR(u.ds_certificate_date, 1, 6) BETWEEN
                    CONCAT(#{param.startTime}, SUBSTR(u.ds_certificate_date, 5, 2)) AND
                    CONCAT(#{param.endTime}, SUBSTR(u.ds_certificate_date, 5, 2))
                </when>
                <otherwise>
                    AND u.ds_certificate_date BETWEEN #{param.startTime} AND #{param.endTime}
                </otherwise>
            </choose>
        </if>
        <if test="param.relatedMatterName != null and param.relatedMatterName != ''">
            <if test="param.relatedMatterName.startsWith('#') and param.relatedMatterName.endsWith('%')">
                AND EXISTS (
                SELECT 1 FROM ctf_certificate_type cct
                WHERE cct.CERTIFICATE_TYPE_ID = u.certificate_id
                AND cct.DESCRIPTION LIKE #{param.relatedMatterName}
                )
            </if>
            <if test="!param.relatedMatterName.startsWith('#') or !param.relatedMatterName.endsWith('%')">
                AND EXISTS (
                SELECT 1 FROM ctf_certificate_type cct
                WHERE cct.CERTIFICATE_TYPE_ID = u.certificate_id
                AND cct.DESCRIPTION IN
                <foreach collection="param.relatedMatterName.split(',')" item="name" open="(" separator="," close=")">
                    #{name}
                </foreach>
                )
            </if>
        </if>
        <if test="param.certificateIds != null and param.certificateIds.size() > 0">
            AND u.certificate_id IN
            <foreach collection="param.certificateIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        GROUP BY d.DEPT_ID, d.NAME
        ORDER BY usageCount DESC
    </select>

    <!-- 获取登录人所在机构的总使用量 -->
    <select id="getTotalUsageCount" resultType="java.lang.Long">
        SELECT
        SUM(u.usage_count) as totalCount
        FROM
        dws_certificate_usage u
        WHERE
        u.issuer_code LIKE CONCAT(#{param.loginUserOrgCode}, '%')
        <if test="param.issuerCodes != null and param.issuerCodes.size() > 0">
            AND (
            <foreach collection="param.issuerCodes" item="issuerCode" separator=" OR ">
                u.issuer_code LIKE CONCAT(#{issuerCode}, '%')
            </foreach>
            )
        </if>
        <if test="param.certificateTypeCodes != null and param.certificateTypeCodes.size() > 0">
            AND EXISTS (
            SELECT 1 FROM ert_type_directory etd_filter
            WHERE etd_filter.CERTIFICATE_TYPE_CODE = u.certificate_type_code
            AND (
            <foreach collection="param.certificateTypeCodes" item="typeCode" separator=" OR ">
                etd_filter.CERTIFICATE_TYPE_CODE = #{typeCode}
                OR EXISTS (
                SELECT 1 FROM ert_type_directory etd_child
                WHERE etd_child.CERTIFICATE_TYPE_CODE = etd_filter.CERTIFICATE_TYPE_CODE
                START WITH etd_child.PARENT_ID = (
                SELECT etd_parent.CERT_TYPE_DIR_ID
                FROM ert_type_directory etd_parent
                WHERE etd_parent.CERTIFICATE_TYPE_CODE = #{typeCode}
                )
                CONNECT BY PRIOR etd_child.CERT_TYPE_DIR_ID = etd_child.PARENT_ID
                )
            </foreach>
            )
            )
        </if>
        <if test="param.holderCategoryCodes != null and param.holderCategoryCodes.size() > 0">
            AND u.holder_category_code IN
            <foreach collection="param.holderCategoryCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.matterNatureCodes != null and param.matterNatureCodes.size() > 0">
            AND u.matter_nature_code IN
            <foreach collection="param.matterNatureCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.applicationSourceCode != null and param.applicationSourceCode != ''">
            AND u.application_source_code = #{param.applicationSourceCode}
        </if>
        <if test="param.statusCode != null and param.statusCode != ''">
            AND u.status_code = #{param.statusCode}
        </if>
        <if test="param.usageType != null and param.usageType != ''">
            AND u.usage_type = #{param.usageType}
        </if>
        <if test="param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            <choose>
                <when test="param.timeType == 'year'">
                    AND SUBSTR(u.ds_certificate_date, 1, 4) BETWEEN #{param.startTime} AND #{param.endTime}
                </when>
                <when test="param.timeType == 'quarter'">
                    AND (
                    SUBSTR(u.ds_certificate_date, 1, 4) > #{param.startTime}
                    OR (
                    SUBSTR(u.ds_certificate_date, 1, 4) = #{param.startTime}
                    AND CEIL(TO_NUMBER(SUBSTR(u.ds_certificate_date, 5, 2)) / 3) &gt;= CEIL(TO_NUMBER(SUBSTR(#{param.startTime}, 5, 2)) / 3)
                    )
                    )
                    AND (
                    SUBSTR(u.ds_certificate_date, 1, 4) &lt;#{param.endTime}
                    OR (
                    SUBSTR(u.ds_certificate_date, 1, 4) = #{param.endTime}
                    AND CEIL(TO_NUMBER(SUBSTR(u.ds_certificate_date, 5, 2)) / 3) &lt;= CEIL(TO_NUMBER(SUBSTR(#{param.endTime}, 5, 2)) / 3)
                    )
                    )
                </when>
                <when test="param.timeType == 'month'">
                    AND SUBSTR(u.ds_certificate_date, 1, 6) BETWEEN
                    CONCAT(#{param.startTime}, SUBSTR(u.ds_certificate_date, 5, 2)) AND
                    CONCAT(#{param.endTime}, SUBSTR(u.ds_certificate_date, 5, 2))
                </when>
                <otherwise>
                    AND u.ds_certificate_date BETWEEN #{param.startTime} AND #{param.endTime}
                </otherwise>
            </choose>
        </if>
        <if test="param.relatedMatterName != null and param.relatedMatterName != ''">
            <if test="param.relatedMatterName.startsWith('#') and param.relatedMatterName.endsWith('%')">
                AND EXISTS (
                SELECT 1 FROM ctf_certificate_type cct
                WHERE cct.CERTIFICATE_TYPE_ID = u.certificate_id
                AND cct.DESCRIPTION LIKE #{param.relatedMatterName}
                )
            </if>
            <if test="!param.relatedMatterName.startsWith('#') or !param.relatedMatterName.endsWith('%')">
                AND EXISTS (
                SELECT 1 FROM ctf_certificate_type cct
                WHERE cct.CERTIFICATE_TYPE_ID = u.certificate_id
                AND cct.DESCRIPTION IN
                <foreach collection="param.relatedMatterName.split(',')" item="name" open="(" separator="," close=")">
                    #{name}
                </foreach>
                )
            </if>
        </if>
        <if test="param.certificateIds != null and param.certificateIds.size() > 0">
            AND u.certificate_id IN
            <foreach collection="param.certificateIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <!-- 按持证主体类别统计使用情况并计算占比 -->
    <select id="statisticsByHolderCategoryWithRatio" resultType="com.js.hszpt.dto.HolderCategoryUsageStatisticsDTO">
        SELECT
        u.holder_category_code as holderCategoryCode,
        (SELECT b.title FROM t_dict a, t_dict_data b
        WHERE a.type = 'CERTIFICATE_HOLDER_CATEGORY'
        AND a.id = b.dict_id
        AND b.value = u.holder_category_code) as holderCategoryName,
        SUM(u.usage_count) as usageCount,
        0.0 as usageRatio,
        0 as isSummary
        FROM
        dws_certificate_usage u
        WHERE
        u.issuer_code LIKE CONCAT(#{param.loginUserOrgCode}, '%')
        <if test="param.issuerCodes != null and param.issuerCodes.size() > 0">
            AND (
            <foreach collection="param.issuerCodes" item="issuerCode" separator=" OR ">
                u.issuer_code LIKE CONCAT(#{issuerCode}, '%')
            </foreach>
            )
        </if>
        <if test="param.certificateTypeCodes != null and param.certificateTypeCodes.size() > 0">
            AND EXISTS (
            SELECT 1 FROM ert_type_directory etd_filter
            WHERE etd_filter.CERTIFICATE_TYPE_CODE = u.certificate_type_code
            AND (
            <foreach collection="param.certificateTypeCodes" item="typeCode" separator=" OR ">
                etd_filter.CERTIFICATE_TYPE_CODE = #{typeCode}
                OR EXISTS (
                SELECT 1 FROM ert_type_directory etd_child
                WHERE etd_child.CERTIFICATE_TYPE_CODE = etd_filter.CERTIFICATE_TYPE_CODE
                START WITH etd_child.PARENT_ID = (
                SELECT etd_parent.CERT_TYPE_DIR_ID
                FROM ert_type_directory etd_parent
                WHERE etd_parent.CERTIFICATE_TYPE_CODE = #{typeCode}
                )
                CONNECT BY PRIOR etd_child.CERT_TYPE_DIR_ID = etd_child.PARENT_ID
                )
            </foreach>
            )
            )
        </if>
        <if test="param.holderCategoryCodes != null and param.holderCategoryCodes.size() > 0">
            AND u.holder_category_code IN
            <foreach collection="param.holderCategoryCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.matterNatureCodes != null and param.matterNatureCodes.size() > 0">
            AND u.matter_nature_code IN
            <foreach collection="param.matterNatureCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.applicationSourceCode != null and param.applicationSourceCode != ''">
            AND u.application_source_code = #{param.applicationSourceCode}
        </if>
        <if test="param.statusCode != null and param.statusCode != ''">
            AND u.status_code = #{param.statusCode}
        </if>
        <if test="param.usageType != null and param.usageType != ''">
            AND u.usage_type = #{param.usageType}
        </if>
        <if test="param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            <choose>
                <when test="param.timeType == 'year'">
                    AND SUBSTR(u.ds_certificate_date, 1, 4) BETWEEN #{param.startTime} AND #{param.endTime}
                </when>
                <when test="param.timeType == 'quarter'">
                    AND (
                    SUBSTR(u.ds_certificate_date, 1, 4) > #{param.startTime}
                    OR (
                    SUBSTR(u.ds_certificate_date, 1, 4) = #{param.startTime}
                    AND CEIL(TO_NUMBER(SUBSTR(u.ds_certificate_date, 5, 2)) / 3) &gt;= CEIL(TO_NUMBER(SUBSTR(#{param.startTime}, 5, 2)) / 3)
                    )
                    )
                    AND (
                    SUBSTR(u.ds_certificate_date, 1, 4) &lt;#{param.endTime}
                    OR (
                    SUBSTR(u.ds_certificate_date, 1, 4) = #{param.endTime}
                    AND CEIL(TO_NUMBER(SUBSTR(u.ds_certificate_date, 5, 2)) / 3) &lt;= CEIL(TO_NUMBER(SUBSTR(#{param.endTime}, 5, 2)) / 3)
                    )
                    )
                </when>
                <when test="param.timeType == 'month'">
                    AND SUBSTR(u.ds_certificate_date, 1, 6) BETWEEN
                    CONCAT(#{param.startTime}, SUBSTR(u.ds_certificate_date, 5, 2)) AND
                    CONCAT(#{param.endTime}, SUBSTR(u.ds_certificate_date, 5, 2))
                </when>
                <otherwise>
                    AND u.ds_certificate_date BETWEEN #{param.startTime} AND #{param.endTime}
                </otherwise>
            </choose>
        </if>
        <if test="param.relatedMatterName != null and param.relatedMatterName != ''">
            <if test="param.relatedMatterName.startsWith('#') and param.relatedMatterName.endsWith('%')">
                AND EXISTS (
                SELECT 1 FROM ctf_certificate_type cct
                WHERE cct.CERTIFICATE_TYPE_ID = u.certificate_id
                AND cct.DESCRIPTION LIKE #{param.relatedMatterName}
                )
            </if>
            <if test="!param.relatedMatterName.startsWith('#') or !param.relatedMatterName.endsWith('%')">
                AND EXISTS (
                SELECT 1 FROM ctf_certificate_type cct
                WHERE cct.CERTIFICATE_TYPE_ID = u.certificate_id
                AND cct.DESCRIPTION IN
                <foreach collection="param.relatedMatterName.split(',')" item="name" open="(" separator="," close=")">
                    #{name}
                </foreach>
                )
            </if>
        </if>
        <if test="param.certificateIds != null and param.certificateIds.size() > 0">
            AND u.certificate_id IN
            <foreach collection="param.certificateIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        GROUP BY u.holder_category_code
        ORDER BY usageCount DESC
    </select>

    <!-- 获取持证主体类别名称 -->
    <select id="getHolderCategoryName" resultType="java.lang.String">
        SELECT b.title FROM t_dict a, t_dict_data b
        WHERE a.type = 'CERTIFICATE_HOLDER_CATEGORY'
          AND a.id = b.dict_id
          AND b.value = #{holderCategoryCode}
    </select>

    <!-- 按时间统计使用情况并计算占比 -->
    <select id="statisticsByTimeWithRatio" resultType="com.js.hszpt.dto.TimeUsageStatisticsDTO">
        SELECT
        <choose>
            <when test="param.timeType == 'year'">
                SUBSTR(u.ds_certificate_date, 1, 4) as timePoint
            </when>
            <when test="param.timeType == 'quarter'">
                CONCAT(SUBSTR(u.ds_certificate_date, 1, 4), 'Q', CEIL(TO_NUMBER(SUBSTR(u.ds_certificate_date, 5, 2)) / 3)) as timePoint
            </when>
            <when test="param.timeType == 'month'">
                SUBSTR(u.ds_certificate_date, 1, 6) as timePoint
            </when>
            <otherwise>
                u.ds_certificate_date as timePoint
            </otherwise>
        </choose>,
        SUM(u.usage_count) as usageCount,
        0.0 as usageRatio,
        0 as isSummary
        FROM
        dws_certificate_usage u
        WHERE
        u.issuer_code LIKE CONCAT(#{param.loginUserOrgCode}, '%')
        <if test="param.issuerCodes != null and param.issuerCodes.size() > 0">
            AND (
            <foreach collection="param.issuerCodes" item="issuerCode" separator=" OR ">
                u.issuer_code LIKE CONCAT(#{issuerCode}, '%')
            </foreach>
            )
        </if>
        <if test="param.certificateTypeCodes != null and param.certificateTypeCodes.size() > 0">
            AND EXISTS (
            SELECT 1 FROM ert_type_directory etd_filter
            WHERE etd_filter.CERTIFICATE_TYPE_CODE = u.certificate_type_code
            AND (
            <foreach collection="param.certificateTypeCodes" item="typeCode" separator=" OR ">
                etd_filter.CERTIFICATE_TYPE_CODE = #{typeCode}
                OR EXISTS (
                SELECT 1 FROM ert_type_directory etd_child
                WHERE etd_child.CERTIFICATE_TYPE_CODE = etd_filter.CERTIFICATE_TYPE_CODE
                START WITH etd_child.PARENT_ID = (
                SELECT etd_parent.CERT_TYPE_DIR_ID
                FROM ert_type_directory etd_parent
                WHERE etd_parent.CERTIFICATE_TYPE_CODE = #{typeCode}
                )
                CONNECT BY PRIOR etd_child.CERT_TYPE_DIR_ID = etd_child.PARENT_ID
                )
            </foreach>
            )
            )
        </if>
        <if test="param.holderCategoryCodes != null and param.holderCategoryCodes.size() > 0">
            AND u.holder_category_code IN
            <foreach collection="param.holderCategoryCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.matterNatureCodes != null and param.matterNatureCodes.size() > 0">
            AND u.matter_nature_code IN
            <foreach collection="param.matterNatureCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.applicationSourceCode != null and param.applicationSourceCode != ''">
            AND u.application_source_code = #{param.applicationSourceCode}
        </if>
        <if test="param.statusCode != null and param.statusCode != ''">
            AND u.status_code = #{param.statusCode}
        </if>
        <if test="param.usageType != null and param.usageType != ''">
            AND u.usage_type = #{param.usageType}
        </if>
        <if test="param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            <choose>
                <when test="param.timeType == 'year'">
                    AND SUBSTR(u.ds_certificate_date, 1, 4) BETWEEN #{param.startTime} AND #{param.endTime}
                </when>
                <when test="param.timeType == 'quarter'">
                    AND (
                    SUBSTR(u.ds_certificate_date, 1, 4) &gt; #{param.startTime}
                    OR (
                    SUBSTR(u.ds_certificate_date, 1, 4) = #{param.startTime}
                    AND CEIL(TO_NUMBER(SUBSTR(u.ds_certificate_date, 5, 2)) / 3) &gt;= CEIL(TO_NUMBER(SUBSTR(#{param.startTime}, 5, 2)) / 3)
                    )
                    )
                    AND (
                    SUBSTR(u.ds_certificate_date, 1, 4) &lt;#{param.endTime}
                    OR (
                    SUBSTR(u.ds_certificate_date, 1, 4) = #{param.endTime}
                    AND CEIL(TO_NUMBER(SUBSTR(u.ds_certificate_date, 5, 2)) / 3) &lt;= CEIL(TO_NUMBER(SUBSTR(#{param.endTime}, 5, 2)) / 3)
                    )
                    )
                </when>
                <when test="param.timeType == 'month'">
                    AND SUBSTR(u.ds_certificate_date, 1, 6) BETWEEN
                    CONCAT(#{param.startTime}, SUBSTR(u.ds_certificate_date, 5, 2)) AND
                    CONCAT(#{param.endTime}, SUBSTR(u.ds_certificate_date, 5, 2))
                </when>
                <otherwise>
                    AND u.ds_certificate_date BETWEEN #{param.startTime} AND #{param.endTime}
                </otherwise>
            </choose>
        </if>
        <if test="param.relatedMatterName != null and param.relatedMatterName != ''">
            <if test="param.relatedMatterName.startsWith('#') and param.relatedMatterName.endsWith('%')">
                AND EXISTS (
                SELECT 1 FROM ctf_certificate_type cct
                WHERE cct.CERTIFICATE_TYPE_ID = u.certificate_id
                AND cct.DESCRIPTION LIKE #{param.relatedMatterName}
                )
            </if>
            <if test="!param.relatedMatterName.startsWith('#') or !param.relatedMatterName.endsWith('%')">
                AND EXISTS (
                SELECT 1 FROM ctf_certificate_type cct
                WHERE cct.CERTIFICATE_TYPE_ID = u.certificate_id
                AND cct.DESCRIPTION IN
                <foreach collection="param.relatedMatterName.split(',')" item="name" open="(" separator="," close=")">
                    #{name}
                </foreach>
                )
            </if>
        </if>
        <if test="param.certificateIds != null and param.certificateIds.size() > 0">
            AND u.certificate_id IN
            <foreach collection="param.certificateIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        GROUP BY
        <choose>
            <when test="param.timeType == 'year'">
                SUBSTR(u.ds_certificate_date, 1, 4)
            </when>
            <when test="param.timeType == 'quarter'">
                CONCAT(SUBSTR(u.ds_certificate_date, 1, 4), 'Q', CEIL(TO_NUMBER(SUBSTR(u.ds_certificate_date, 5, 2)) / 3))
            </when>
            <when test="param.timeType == 'month'">
                SUBSTR(u.ds_certificate_date, 1, 6)
            </when>
            <otherwise>
                u.ds_certificate_date
            </otherwise>
        </choose>
        ORDER BY timePoint
    </select>
</mapper>
