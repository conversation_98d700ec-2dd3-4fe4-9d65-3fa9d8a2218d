package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@TableName("CTF_CERTIFICATE_TYPE")
@ApiModel(value = "证照分类表")
public class CertificateType {

    // 主键
    @TableId
    private String certificateTypeId;

    // 证照名称
    private String certificateName;

    // 证照分类编码
    private String certificateTypeCode;

    // 证照相关的描述
    private String description;

    //证照目录id
    private String certTypeDirId;

    // 证照版本号
    private String certVersion;

    // 字段映射配置 JSON格式
    private String fieldMapping;

    // 创建人
    private String createOperId;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    // 创建时间
    private Date createDate;

    // 修改人
    private String modifyOperId;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    // 修改时间（注意字段名拼写错误已修正）
    private Date modifyDate;

    // 逻辑删除标记(0--正常 1--删除)
    @TableLogic(value = "0", delval = "1")
    private String delFlag;

    private String CatalogId;

    private String affairName;

    private String certTypeDirName;

}
