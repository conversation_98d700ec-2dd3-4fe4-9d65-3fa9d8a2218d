package com.js.hszpt.service;

import com.js.hszpt.mapper.OfficerInfoCertDao;
import com.js.hszpt.entity.OfficerInfoCert;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.core.common.utils.PageUtil;
import com.js.core.common.vo.PageVo;
import com.js.core.common.vo.SearchVo;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
 
/**
 * 
 * @ClassName:  OfficerInfoCertService    
 * @Description:TODO(接口实现)   
 * @author:  System Generation
 *
 */
@Slf4j
@Service
public class OfficerInfoCertService extends ServiceImpl<OfficerInfoCertDao,OfficerInfoCert> {
	/**
	 * 多条件分页查询
	 * @param param
	 * @param searchVo
	 * @param pageVo
	 * @return
	 */
	public Page<OfficerInfoCert> findByCondition(OfficerInfoCert param, SearchVo searchVo, PageVo pageVo) {
		Page<OfficerInfoCert> page = PageUtil.initMpPage(pageVo);
		QueryWrapper<OfficerInfoCert> queryWrapper = this.getCondition(param,searchVo);
		return this.page(page, queryWrapper);
	}
	
	/**
	 * 
	 * @Title: findByCondition   
	 * @Description: TODO(多条件列表查询)   
	 * @param param
	 * @param searchVo
	 * @return  List<OfficerInfoCert>      
	 * @throws
	 */
	public List<OfficerInfoCert> findByCondition(OfficerInfoCert param, SearchVo searchVo){
		QueryWrapper<OfficerInfoCert> queryWrapper = this.getCondition(param, searchVo);
		//添加排序字段
		queryWrapper.orderByAsc("create_time");
		
		return this.list(queryWrapper);
	}
	
	
	/**
	 * 
	 * @Title: getCondition   
	 * @Description: TODO(构建自定义查询条件)   
	 * @param param
	 * @param searchVo
	 * @return  QueryWrapper<OfficerInfoCert>      
	 * @throws
	 */
	private QueryWrapper<OfficerInfoCert> getCondition(OfficerInfoCert param, SearchVo searchVo){
		QueryWrapper<OfficerInfoCert> queryWrapper = new QueryWrapper<OfficerInfoCert>();
		//请根据实际字段自行扩展
		 
		// 创建时间
		if (StrUtil.isNotBlank(searchVo.getStartDate()) && StrUtil.isNotBlank(searchVo.getEndDate())) {
			Date start = DateUtil.parse(searchVo.getStartDate());
			Date end = DateUtil.parse(searchVo.getEndDate());
			queryWrapper.between("create_time",start, DateUtil.endOfDay(end));
		}
		return queryWrapper;
		
	}
}