package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableId;

import java.util.Date;

/**
 * 受理审批-业务受理表 实体类
 */
public class BizEgAccept {

    /**
     * 受理结果唯一标识（主键）
     */
    @TableId
    private String acceptResultId;

    /**
     * 申请标识
     */
    private String applyId;

    /**
     * 报告类型名称
     */
    private String rptClassName;

    /**
     * 受理结果代码（1：受理；2：不予受理；3：材料补正）
     */
    private String acceptResultCode;

    /**
     * 受理意见
     */
    private String acceptMsg;

    /**
     * 发送文书
     */
    private String sendBook;

    /**
     * 受理人（关联字典 DICT_EG_USER）
     */
    private String accepter;

    /**
     * 证书地址
     */
    private String url;

    /**
     * 证照编号1
     */
    private String certNo1;

    /**
     * 证照编号2
     */
    private String certNo2;

    /**
     * 证照编号3
     */
    private String certNo3;

    /**
     * 证书ID1
     */
    private String certId1;

    /**
     * 证书ID2
     */
    private String certId2;

    /**
     * 证书ID3
     */
    private String certId3;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人标识（关联字典 DICT_EG_USER）
     */
    private String creatorId;

    /**
     * 创建日期
     */
    private Date createDate;

    /**
     * 创建人所属机构代码
     */
    private String creatorOrgCode;

    /**
     * 修改人标识（关联字典 DICT_EG_USER）
     */
    private String modifierId;

    /**
     * 修改日期
     */
    private Date modifyDate;

    /**
     * 源系统代码
     */
    private String sourceCode;

    /**
     * 记录创建日期
     */
    private Date recCreateDate;

    /**
     * 记录修改日期
     */
    private Date recModifyDate;

    /**
     * 数据归属机构代码（关联字典 DICT_EG_ORG）
     */
    private String msaOrgCode;

    // Getter and Setter 方法
    public String getAcceptResultId() { return acceptResultId; }
    public void setAcceptResultId(String acceptResultId) { this.acceptResultId = acceptResultId; }

    public String getApplyId() { return applyId; }
    public void setApplyId(String applyId) { this.applyId = applyId; }

    public String getRptClassName() { return rptClassName; }
    public void setRptClassName(String rptClassName) { this.rptClassName = rptClassName; }

    public String getAcceptResultCode() { return acceptResultCode; }
    public void setAcceptResultCode(String acceptResultCode) { this.acceptResultCode = acceptResultCode; }

    public String getAcceptMsg() { return acceptMsg; }
    public void setAcceptMsg(String acceptMsg) { this.acceptMsg = acceptMsg; }

    public String getSendBook() { return sendBook; }
    public void setSendBook(String sendBook) { this.sendBook = sendBook; }

    public String getAccepter() { return accepter; }
    public void setAccepter(String accepter) { this.accepter = accepter; }

    public String getUrl() { return url; }
    public void setUrl(String url) { this.url = url; }

    public String getCertNo1() { return certNo1; }
    public void setCertNo1(String certNo1) { this.certNo1 = certNo1; }

    public String getCertNo2() { return certNo2; }
    public void setCertNo2(String certNo2) { this.certNo2 = certNo2; }

    public String getCertNo3() { return certNo3; }
    public void setCertNo3(String certNo3) { this.certNo3 = certNo3; }

    public String getCertId1() { return certId1; }
    public void setCertId1(String certId1) { this.certId1 = certId1; }

    public String getCertId2() { return certId2; }
    public void setCertId2(String certId2) { this.certId2 = certId2; }

    public String getCertId3() { return certId3; }
    public void setCertId3(String certId3) { this.certId3 = certId3; }

    public String getRemark() { return remark; }
    public void setRemark(String remark) { this.remark = remark; }

    public String getCreatorId() { return creatorId; }
    public void setCreatorId(String creatorId) { this.creatorId = creatorId; }

    public Date getCreateDate() { return createDate; }
    public void setCreateDate(Date createDate) { this.createDate = createDate; }

    public String getCreatorOrgCode() { return creatorOrgCode; }
    public void setCreatorOrgCode(String creatorOrgCode) { this.creatorOrgCode = creatorOrgCode; }

    public String getModifierId() { return modifierId; }
    public void setModifierId(String modifierId) { this.modifierId = modifierId; }

    public Date getModifyDate() { return modifyDate; }
    public void setModifyDate(Date modifyDate) { this.modifyDate = modifyDate; }

    public String getSourceCode() { return sourceCode; }
    public void setSourceCode(String sourceCode) { this.sourceCode = sourceCode; }

    public Date getRecCreateDate() { return recCreateDate; }
    public void setRecCreateDate(Date recCreateDate) { this.recCreateDate = recCreateDate; }

    public Date getRecModifyDate() { return recModifyDate; }
    public void setRecModifyDate(Date recModifyDate) { this.recModifyDate = recModifyDate; }

    public String getMsaOrgCode() { return msaOrgCode; }
    public void setMsaOrgCode(String msaOrgCode) { this.msaOrgCode = msaOrgCode; }

    // 可选：可添加 toString() 方法
    @Override
    public String toString() {
        return "BizEgAccept{" +
                "acceptResultId='" + acceptResultId + '\'' +
                ", applyId='" + applyId + '\'' +
                // 其他字段拼接...
                '}';
    }
}